# Plataforma FARM Micro Front End Cadastro

<p align="center">
  <img  src="./docs/images/banner.png" width="100%" />
</p>

[![Quality Gate Status](https://sonarqube.dev-api.portalfarm.com.br/api/project_badges/measure?branch=main&project=Farm-Investimentos_front-mfe-cadastros_AYROg78kgC3v78ERowlM&metric=alert_status&token=sqb_578296cd185a0896011bf6b9955786bf46e694ec)](https://sonarqube.dev-api.portalfarm.com.br/dashboard?id=Farm-Investimentos_front-mfe-cadastros_AYROg78kgC3v78ERowlM&branch=main)
[![Build & Deploy main branch on prd](https://github.com/Farm-Investimentos/front-mfe-cadastros/actions/workflows/deploy_branch_prd.yml/badge.svg)](https://github.com/Farm-Investimentos/front-mfe-cadastros/actions/workflows/deploy_branch_prd.yml)
[![codecov](https://codecov.io/gh/Farm-Investimentos/front-mfe-cadastros/branch/develop/graph/badge.svg?token=LSZXJ5LDG0)](https://codecov.io/gh/Farm-Investimentos/front-mfe-cadastros)

## 🚀 Como iniciar o desenvolvimento

#### 1. Clone este repositório utilizando o comando abaixo:

```bash
$ git clone https://github.com/Farm-Investimentos/front-mfe-cadastros.git
```

#### 2. Entre na pasta do projeto utilizando o comando abaixo:

```bash
$ cd front-mfe-cadastros
```

#### 3. Instale as dependências do projeto utilizando o comando abaixo:

```bash
$ npm i
```

ou

```bash
$ yarn add
```

#### 4. Inicie o servidor do projeto utilizando o comando abaixo:

```bash
$ npm run serve
```

ou

```bash
$  yarn serve
```

💡 Caso seu navegador não abra o projeto automaticamente, abra uma nova aba manualmente e acesse `http://localhost:5000` ou [clique aqui](http://localhost:5000).
Lembrando que vc precisa subir o micro front end orquestrador para pode abrir na porta acima.

## 🛠️ Outros scripts

Outros comandos que estão no arquivo package.json.

#### Comandos que inicia o servidor

```bash
$  npm run serve
```

ou

```bash
$  yarn serve
```

⚠️ Após rodar o comando acima o ambiente que a das `APIs` será do de `Desenvolvimento`, caso queira
testar em outro ambiente você precisa mudar ir no micro front end orquestrador ou [clique aqui](https://github.com/Farm-Investimentos/front-mfe-orquestrador). ⚠️

#### Comandos de Formatação e validação de código

O comando abaixo ele roda o ESLint configurado no projeto.

```bash
$  npm run lint
```

ou

```bash
$  yarn lint
```

O comando abaixo ele roda o prettier que esta configurado no projeto ele vai formatar os arquivos para o estilo de código definido na configuração.

```bash
$  npm run prettier
```

ou

```bash
$  yarn prettier
```

#### Comandos de build

Os comandos abaixos ele faz o build do front end.

```bash
$  npm run build
```

ou

```bash
$  yarn build
```

## 🏈 Features deste micro front end

Veja abaixo as features que este micro front end contém na plataforma Farm.

#### Cadastro -> Clientes

Tela de Clientes onde o usuário pode adicionar um novo cliente, editar seus dados, excluir, importar e exporta planilhas, editar os dados da empresa, e ver seus documentos.

<details>
  <summary>Clique aqui e veja o print das telas</summary>
  <ol>
    <li style="list-style:none">
      <p align="center">
        <img  src="./docs/images/clientes.png" width="100%" />
      </p>
    </li>
  </ol>
</details>

#### Cadastro -> Empresas

Tela de Empresas onde o usuário pode adicionar uma nova empresa, editar seus dados, excluir, importar e exporta planilhas.

<details>
  <summary>Clique aqui e veja o print das telas</summary>
  <ol>
    <li style="list-style:none">
      <p align="center">
        <img  src="./docs/images/empresas.png" width="100%" />
      </p>
    </li>
  </ol>
</details>

#### Cadastro -> Fornecedores

Tela de Fornecedores onde o usuário pode adicionar um novo fornecedores, editar seus dados, excluir e exporta planilhas.

<details>
  <summary>Clique aqui e veja o print das telas</summary>
  <ol>
    <li style="list-style:none">
      <p align="center">
        <img  src="./docs/images/fornecedores.png" width="100%" />
      </p>
    </li>
  </ol>
</details>

#### Cadastro -> Grupos

Tela de Grupos onde o usuário pode adicionar um novo grupos, editar seus dados, excluir, importar e exporta planilhas.

<details>
  <summary>Clique aqui e veja o print das telas</summary>
  <ol>
    <li style="list-style:none">
      <p align="center">
        <img  src="./docs/images/grupos.png" width="100%" />
      </p>
    </li>
  </ol>
</details>

#### Cadastro -> Gerentes

Tela de Gerentes onde o usuário pode adicionar um novo gerentes, editar seus dados, excluir e exporta planilhas.

<details>
  <summary>Clique aqui e veja o print das telas</summary>
  <ol>
    <li style="list-style:none">
      <p align="center">
        <img  src="./docs/images/gerentes.png" width="100%" />
      </p>
    </li>
  </ol>
</details>

#### Cadastro -> Dados Cadastrais

Tela de Dados Cadastrais onde o usuário pode editar o dado Cadastral.

<details>
  <summary>Clique aqui e veja o print das telas</summary>
  <ol>
    <li style="list-style:none">
      <p align="center">
        <img  src="./docs/images/dados-cadastrais.png" width="100%" />
      </p>
    </li>
  </ol>
</details>

## 📁 Estrura de pasta do projeto

```
/
├─ .github/                      # Onde contém as action de deploy, release e tag.
├─ .husky/                       # Responsável por disparar ações na hora de fazer o push.
├─ docs/                         # Onde contém as images referente ao Readme.
├─ mocks/                        # Onde contém os mocks das APIs que esse front acessa.
├─ src/
│  ├─ assets/                    # Arquivos de estilos e imagens.
│  ├─ components/                # Componentes que e compartilhado por todo o micro front end.
│  ├─ configurations/            # Arquivos de configurações do projeto ex: vuetify.
│  ├─ features/                  # Contém as Funcionalidades do projeto
│  ├─ helpers/                   # Funções de ajuda que pode ser utilizado no projeto inteiro.
│  ├─ i18n/                      # Arquivos de tradução do sistema.
│  ├─ mixins/                    # Arquivos da feature toggle do projeto.
│  ├─ routes/                    # Arquivos que contém todas as rotas deste micro front end.
│  ├─ services/                  # Arquivos que contém todas as chamadas das APIs.
│  ├─ store/                     # Contém os estados, action global deste micro front end.
│  ├─ App.vue                    # Componente responsável pela a aplicação
|  └─ main.ts                    # Arquivo responsável por iniciar todas as lib e dependencias.
│
├─ tests/                        # Onde contém todos os teste unitários deste micro front end.
├─ .editorconfig                 # Responsável pela padronização dos espaçamentos.
├─ .eslintrc                     # Regras de validação e formatação de código.
├─ .gitignore                    # Lista de arquivos e pasta que não vai subir para o repositório.
├─ .prettierrc                   # Configuração do estilo de código.
├─ babel.config.js               # Configuração do Babel com o vue.
├─ jest.config.js                # Configuração do jest no projeto.
├─ package.json                  # Dependencias do projeto.
├─ README.md                     # Este arquivo.
├─ tsconfig.json                 # Configuração do typescript no projeto.
└─ vue.config.js                 # Configuração do Vue no projeto.
```

## 💻 Tecnologia usada neste micro front end

#### Veja abaixo a lista de todas as tecnologias utilizada neste micro front end:

-   [Single SPA](https://single-spa.js.org) controla os micros front ends;
-   [Vue 2](https://vuejs.org) framework de desenvolvimento;
-   [Typescript](https://www.typescriptlang.org/docs);
-   [Sass](https://sass-lang.com) ferramenta de estilo;
-   [Vuetify](https://vuetifyjs.com) framework de components;
-   [Apexcharts](https://apexcharts.com/docs/vue-charts) framework de gráficos;
-   [Vuex](https://vuex.vuejs.org) gerenciamento de estado global;
-   [Axios](https://axios-http.com/ptbr/docs/intro) lib de request;
-   [Eslint](https://eslint.org) validação e estilo de escrita de código;
-   [Prettier](https://prettier.io) formatação de código;

## 😎 Autores

#### Time de Front End `Farm`
