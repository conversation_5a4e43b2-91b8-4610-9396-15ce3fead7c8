import { addDecorator } from '@storybook/vue';
import vuetify from './vuetify_storybook';

import Vue from 'vue';
import installComponents from '@farm-investimentos/front-mfe-libs-ts/helpers/installComponents';
import '@farm-investimentos/front-mfe-components/dist/front-mfe-components.css';
import '@farm-investimentos/front-mfe-components/src/scss/ButtonOverrides.scss';
import '@farm-investimentos/front-mfe-components/src/scss/DefaultModal.scss';
import '@farm-investimentos/front-mfe-components/src/scss/DialogOverrides.scss';
import '@farm-investimentos/front-mfe-components/src/scss/FormOverrides.scss';
import '@farm-investimentos/front-mfe-components/src/scss/Status-Chip.scss';
import '@farm-investimentos/front-mfe-components/src/scss/VMenuOverrides.scss';
import '@farm-investimentos/front-mfe-components/src/scss/utils.scss';
import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table.scss';
import '@farm-investimentos/front-mfe-components/src/scss/cssVariablesGenerator.scss';

import '@farm-investimentos/front-mfe-components/src/scss/Table.scss';

import * as farmComponents from '@farm-investimentos/front-mfe-components';
installComponents(Vue, farmComponents);


addDecorator(() => ({
	vuetify,
	template: `
    <v-app>
      <v-main>
        <v-container fluid >
          <story/>
        </v-container>
      </v-main>
    </v-app>
    `,
}));
