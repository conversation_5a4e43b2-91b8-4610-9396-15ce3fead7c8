import { ref } from 'vue';
import { getAllCfops, getSelectedCfops } from '@/features/products/services';

interface CfopParams {
	vehicleData: { financialVehicleId: number };
	productId: number;
	commercialProductId: number;
	filterParams?: Record<string, any>;
}

interface Cfop {
	id: string;
	code: string;
	description: string;
	salesPerformed: number;
	associated: string;
	selected?: boolean;
}

export function useCfops({
	vehicleData,
	productId,
	commercialProductId,
	filterParams = {},
}: CfopParams) {
	const cfopsList = ref<Cfop[]>([]);
	const cfopData = ref<any>(null);
	const selectedCFPOPs = ref<string[]>([]);
	const errorMessage = ref<string | null>(null);

	const fetchCfops = async (params: Record<string, any> = {}) => {
		try {
			const response = await getAllCfops({
				id: vehicleData?.financialVehicleId,
				params: { ...filterParams, ...params, onlyAssociated: 1 },
			});

			cfopsList.value = response.data.content.map((cfop: Cfop) => ({
				...cfop,
				selected: selectedCFPOPs.value.includes(cfop.id),
			}));
			cfopData.value = response.data;
			errorMessage.value = null;
		} catch (error: any) {
			if (error.response?.data?.error === 'NotFoundException') {
				errorMessage.value = error.response.data.message;
			} else {
				console.error('Erro ao buscar CFOPs:', error);
			}
		}
	};

	const getCfopsSelected = async () => {
		try {
			const response = await getSelectedCfops({
				productId,
				commercialProductId: commercialProductId,
				vehicleId: vehicleData?.financialVehicleId,
				params: {},
			});
			selectedCFPOPs.value = response.data.content;
			errorMessage.value = null;
		} catch (error: any) {
			if (error.response?.data?.error === 'NotFoundException') {
				errorMessage.value = error.response.data.message;
			} else {
				console.error('Erro ao buscar CFOPs selecionados:', error);
			}
		}
	};

	return {
		cfopsList,
		cfopData,
		selectedCFPOPs,
		errorMessage, // Retorna a mensagem de erro
		fetchCfops,
		getCfopsSelected,
	};
}
