<template>
	<v-app>
		<router-view v-if="ready"></router-view>
	</v-app>
</template>
<script lang="ts">
import { defineComponent, getCurrentInstance, onBeforeMount, ref } from 'vue';
import { notification } from '@farm-investimentos/front-mfe-libs-ts';

export default defineComponent({
	setup() {
		const internalInstance: any = getCurrentInstance().proxy;
		const ready = ref(false);

		onBeforeMount(async () => {

			await internalInstance.loadFeatures('cadastros');
			window.addEventListener('MENU_IS_READY', () => {
				ready.value = true;
			});
			notification('RETRIEVE_MENU_IS_READY');
		});
		return {
			ready,
		};
	},
});
</script>
