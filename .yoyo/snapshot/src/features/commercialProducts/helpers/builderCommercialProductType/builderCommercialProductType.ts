import { fixCommercialProductType } from '../fixCommercialProductType';

type BuilderCommercialProductType = {
	id: string;
	label: string
}

export function builderCommercialProductType(response): Array<BuilderCommercialProductType> {
	const productTypes = response.data;
	const newData = productTypes.map((item)=>{
		return {
			id: item.id,
			label: fixCommercialProductType(item.type).toUpperCase()
		};
	});
	return newData;
}
