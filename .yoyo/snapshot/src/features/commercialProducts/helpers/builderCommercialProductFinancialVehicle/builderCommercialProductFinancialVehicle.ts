function createListDocumentAndId(item){
	return[
		{ label: 'ID', value: item.id, copyText: '' },
		{
			label: 'CNPJ',
			value: item.document,
			copyText: item.document,
			successMessage: 'Documento copiado para área de transferência!',
		},
	];
}

export function builderCommercialProductFinancialVehicle(response) {
	const { content, page, size, totalItems, totalPages } = response.data;
	const newData = content.map((item) => {
		return {
			id: item.id || 0,
			type: item.type || 'N/A',
			name: item.name || 'N/A',
			startRelationship: item.associationStart || 'N/A',
			endRelationship: item.associationEnd,
			listIdAndDocument: createListDocumentAndId(item),
			status: item.associationEnd == null ? 'ATIVO': 'INATIVO'
		};
	});

	return {
		content: newData,
		pagination: {
			pageNumber: page,
			pageSize: size,
			sort: null,
			totalElements: totalItems,
			totalPages: totalPages,
		}
	};
}
