import { format } from '@/helpers/formatUpdateUser';

import { COMMERCIAL_PRODUCT_TYPE, LIMITS, STATUS } from '../../constants';
import { fixCommercialProductType } from '../fixCommercialProductType';
import { getFilterCommercialProductTypes } from '../../services';
import { AxiosResponse } from 'axios';

type CommercialProductByIdMeta = {
	createdAt: string;
	createdBy: string;
	updatedAt: string;
	updatedBy: string;
};

type CommercialProductByIdContent = {
	id: number;
	name: string;
	limitId?: number;
	typeId: number;
	startAt: string;
	endAt: string | null;
	enabled: 0 | 1;
	cardImgUrl: string | null;
	accountProductId: number;
};

type ContentResponse = {
	content: CommercialProductByIdContent;
	meta: CommercialProductByIdMeta;
};

function parseNameStatus(value: number): string {
	return STATUS[value];
}

function parseNameLimit(value: number): string {
	return LIMITS[value] || 'N/A';
}

function checkDate(value) {
	if (!value) {
		return null;
	}
	return value.split('T')[0];
}

export async function builderCommercialProductById(response: AxiosResponse<ContentResponse>) {
	const { data: productTypesData } = await getFilterCommercialProductTypes();
	const productTypesObj = productTypesData.reduce(
		(acc: Record<number, string>, item: { id: number; type: string }) => {
			acc[item.id] = item.type;
			return acc;
		},
		{}
	);

	const { content, meta } = response.data;

	return {
		data: {
			...content,
			id: content.id,
			name: content.name,
			dateStart: checkDate(content.startAt),
			dateEnd: checkDate(content.endAt),
			status: parseNameStatus(content.enabled),
			limit: parseNameLimit(content.limitId),
			type: fixCommercialProductType(productTypesObj[content.typeId]),
		},
		meta: format(meta),
	};
}
