import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { builderCommercialProductType } from '@/features/commercialProducts/helpers/builderCommercialProductType';
import { getFilterCommercialProductTypes } from '@/features/commercialProducts/services';

type UseFilterCommercialProductType = {
	commercialProductType : Ref<Array<any>>;
	isLoadingCommercialProductType : ComputedRef<boolean>;
	isErrorCommercialProductType : ComputedRef<boolean>;
	getCommercialProductType : Function;
};

export function useFilterCommercialProductType(): UseFilterCommercialProductType {
	const commercialProductType  = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getFilterCommercialProductTypes(),
		onSuccess: (response) => {
			const data = builderCommercialProductType(response);
			commercialProductType.value = data;
		},
		onError: () => {
			commercialProductType.value = [];
		},
	});

	const isLoadingCommercialProductType = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProductType = computed(() => {
		return isError.value;
	});

	function getCommercialProductType() {
		mutate();
	}

	return {
		commercialProductType,
		isLoadingCommercialProductType,
		isErrorCommercialProductType,
		getCommercialProductType,
	};
}
