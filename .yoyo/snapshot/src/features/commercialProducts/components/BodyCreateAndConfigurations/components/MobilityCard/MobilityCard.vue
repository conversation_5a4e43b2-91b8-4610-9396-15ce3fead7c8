<template>
	<farm-collapsible custom title="" :open="true">
		<template #custom>
			<div class="d-flex align-center">
				<div class="mobility-card-content-img">
					<img
						src="@/assets/icons/currency_exchange.svg"
						alt="imagem referente a dinheiro"
					/>
				</div>
				<div>
					<farm-bodytext variation="bold" :type="1">
						{{ data.operationModalityName }}
					</farm-bodytext>
					<farm-subtitle variation="regular" :type="2">
						Defina os tipos de operações de {{ data.operationModalityName }} permitidas para este produto comercial.
					</farm-subtitle>
				</div>
			</div>
		</template>
		<farm-row>
				<farm-col cols="12" v-for="(item, index) in data.settlementTypeStatus" :key="item.key">
					<div class="d-flex align-center mb-3 mt-3">
						<div class="mobility-label-content mr-5">
							<farm-label class="label-mobility">
								{{ item.settlementTypeName }}
							</farm-label>
						</div>
						<div class="mobility-switcher-content">
							<farm-switcher v-model="item.selected" block />
						</div>
					</div>
					<farm-line noSpacing v-if="(data.settlementTypeStatus.length - 1) !== index" />
				</farm-col>
			</farm-row>
	</farm-collapsible>
</template>

<script lang="ts">

import { defineComponent, ref, watch, toRefs } from 'vue';

export default defineComponent({
	name: 'mobility-card',
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { data } = toRefs(props);

		const selectedValue = ref(false);

		watch(()=> data.value, (newValue)=>{
			emit('updatedCheck', newValue);
		}, {deep: true});

		return {
			selectedValue
		};
	}
});
</script>
<style lang="scss" scoped>
@import './MobilityCard';
</style>

