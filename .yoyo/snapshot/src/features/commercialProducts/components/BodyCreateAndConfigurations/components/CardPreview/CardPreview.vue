<template>
	<farm-box>
		<farm-row class="mb-6">
			<farm-col col="12">
				<farm-alertbox color="neutral" icon="alert-circle-outline" dismissable>
					Essa é uma pré-visualização da imagem do cartão aplicada na plataforma.
				</farm-alertbox>
			</farm-col>
		</farm-row>
		<farm-row>
			<farm-col class="pr-6" col="12" md="6">
				<CarouselCards :items="items" />
			</farm-col>
			<farm-col class="line pl-6" col="12" md="6">
				<farm-row>
					<farm-col class="d-flex align-center justify-space-between mb-8" cols="12">
						<farm-bodytext class="mb-0" :type="2" variation="bold">{{
							name
						}}</farm-bodytext>
						<Skeleton class="mb-1" width="138px" height="42px" radius="10px" />
					</farm-col>
					<farm-col col="12" md="4">
						<Skeleton class="mb-1" width="140px" height="14px" radius="24px" />
						<Skeleton width="80px" height="14px" radius="24px" />
						<Skeleton class="mt-8 mb-1" width="140px" height="14px" radius="24px" />
						<Skeleton width="80px" height="14px" radius="24px" />
					</farm-col>
					<farm-col col="12" md="4">
						<Skeleton class="mb-1" width="140px" height="14px" radius="24px" />
						<Skeleton width="80px" height="14px" radius="24px" />
						<Skeleton class="mt-8 mb-1" width="140px" height="14px" radius="24px" />
						<Skeleton width="80px" height="14px" radius="24px" />
					</farm-col>
					<farm-col class="d-flex justify-end" cols="12" md="4">
						<Skeleton width="84px" height="84px" circle />
					</farm-col>
				</farm-row>
			</farm-col>
		</farm-row>
	</farm-box>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue';

import Skeleton from '@/components/Skeleton';
import CarouselCards from '@/components/CarouselCards/CarouselCards.vue';
import { toRefs } from 'vue';

export default defineComponent({
	name: 'card-preview',
	components: {
		Skeleton,
		CarouselCards,
	},
	props: {
		name: {
			type: String,
			required: true,
		},
		image: {
			type: String,
			required: true,
		},
	},
	setup(props) {
		const { image } = toRefs(props);
		const items = ref([
			{
				sourceImage: image,
			},
			{
				sourceImage: image,
			},
			{
				sourceImage: image,
			},
		]);

		return {
			items,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './CardPreview';
</style>
