<template>
	<farm-box>
		<mobility-list
			v-if="!isError"
			:data="data"
			@updatedCheck="updatedCheck"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';

import { useRouter } from '@/composibles';
import { useCommercialProductById } from '@/features/commercialProducts/composables/useCommercialProductById';
import { useRedirectCommercialProduct } from '@/features/commercialProducts/composables/useRedirectCommercialProduct';

import MobilityList from '../MobilityList';
import { useEditCommercialProduct } from '../../composibles/useEditCommercialProduct';
import { useCommercialProductMobility } from '../../composibles/useCommercialProductMobility';
import { useCommercialProductMessage } from '../../composibles/useCommercialProductMessage';

export default defineComponent({
	name: 'tab-mobility',
	components: {
		MobilityList
	},
	setup(_, { emit }) {
		const router = useRouter();
		const {
			getCommercialProductMobility,
			isLoadingCommercialProductMobility
		} = useCommercialProductMobility();
		const {
			editCommercialProduct,
			isLoadingEditCommercialProduct
		} = useEditCommercialProduct();
		const {
			getCommercialProductById,
			isLoadingCommercialProductById
		} = useCommercialProductById();

		const {
			notificationErrorCommercialProduct,
			updatedMobilityCommercialProduct,
			notificationSuccessMobility
		} = useCommercialProductMessage();

		const {
			redirectEditForceMobility
		} = useRedirectCommercialProduct();

		const isLoading = computed(() => {
			return (
				isLoadingCommercialProductMobility.value ||
				isLoadingEditCommercialProduct.value ||
				isLoadingCommercialProductById.value
			);
		});
		const isError = ref(false);
		const data = ref([]);
		const dataCurrent = ref([]);

		function createPayload(product, mobility){
			const configurations = mobility.map((item) => {
				return {
					operationModality: item.operationModality,
					settlementTypeStatus: item.settlementTypeStatus.map((t)=>{
						return {
							settlementType: t.settlementType,
							status: t.selected ? 1: 0,
						};
					})
				};
			});
			return {
				name: product.name,
				typeId: product.typeId,
				accountProductId: product.accountProductId,
				startAt: product.startAt,
				endAt: product.endAt,
				enabled: product.enabled === 1,
				configurations
			};
		}

		function updatedPageEdit(hasError): void {
			if(hasError) {
				notificationErrorCommercialProduct(false);
				return;
			}
			notificationSuccessMobility();
			setTimeout(()=>{
				redirectEditForceMobility(router.currentRoute.params.id);
			}, 1500);
		}

		function updatedPreSave(_, data) {
			const dataForm = createPayload(data.data, dataCurrent.value);
			const payload = {
				id: router.currentRoute.params.id,
				payload: dataForm
			};
			editCommercialProduct(payload, updatedPageEdit);
		}

		function nextSteps(): void {
			const payload = {
				id: router.currentRoute.params.id
			};
			getCommercialProductById(payload, updatedPreSave);
		}

		function save(): void {
			updatedMobilityCommercialProduct(nextSteps);
		}

		function updatedCheck(value): void {
			const newData = data.value.map((item)=>{
				if(item.operationModality === value.operationModality) {
					return {
						...item,
						settlementTypeStatus:[...value.settlementTypeStatus]
					};
				}
				return item;
			});
			dataCurrent.value = [...newData];
		}

		function updatedPage(hasError, dataAPis): void {
			if(hasError) {
				isError.value = true;
				return;
			}
			isError.value = false;
			data.value = dataAPis.list;
			dataCurrent.value = dataAPis.list;
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			const payload = {
				id: router.currentRoute.params.id
			};
			getCommercialProductMobility(payload, updatedPage);
		}

		onMounted(() => {
			load();
			emit('onUpdatedFuncSave', save);
		});


		return {
			isLoading,
			isError,
			data,
			onReload,
			updatedCheck
		};
	}
});
</script>
