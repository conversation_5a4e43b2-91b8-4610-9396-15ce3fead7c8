
<template>
	<farm-card :class="{ 'farm-card--active': isChecked }">
		<farm-card-content>
			<farm-row align="center">
				<farm-col md="6" class="d-flex mr-7">
					<farm-checkbox
						class="mr-2 mt-3"
						size="sm"
						v-model="isChecked"
						ref="checkbox"
						:value="true"
					/>
					<farm-col>
						<farm-caption variation="regular" class="label-color">Razão Social</farm-caption>
						<farm-bodytext class="value-color" :type="2" ellipsis variation="bold" >
							{{ data.name }}
						</farm-bodytext>
					</farm-col>
				</farm-col>
				<farm-col>
					<farm-caption variation="regular" class="label-color">CNPJ</farm-caption>
					<farm-bodytext :type="2" variation="bold">
						{{ data.document }}
					</farm-bodytext>
				</farm-col>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch } from 'vue';

export default defineComponent({
	name: 'provider-selected-card',
	props: {
		data: {
			type: Object,
			required: true,
		},
		checked: {
			type: Boolean,
			required: false,
			default: true,
		},
		unauthorizedProviders: {
			type: Array,
			required: false,
			default: () => [],
		},
	},
	emits: ['add-to-array', 'remove-from-array'],
	setup(props, { emit }) {
		const isChecked = ref(props.checked);

		function handleCheck() {
			if (isChecked.value) {
				emit('add-to-array', props.data.id);
			} else {
				emit('remove-from-array', props.data.id);
			}
		}

		watch(
			() => isChecked.value,
			() => {
				handleCheck();
			},
			{ immediate: true }
		);

		onMounted(() => {
			if (props.unauthorizedProviders.includes(props.data.id)) {
				isChecked.value = false;
			}
		});

		watch(
			() => props.unauthorizedProviders,
			newUnauthorizedProviders => {
				isChecked.value = !newUnauthorizedProviders.includes(props.data.id);
			}
		);

		return {
			isChecked,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './ProviderSelectedCard';


.label-color {
	color: rgba(117, 117, 117, 1);
}
.value-color {
	color: rgba(51,51,51,1);
}
</style>
