<template>
	<farm-form class="mb-6" v-model="isValid" autocomplete="off">
		<farm-row v-if="!isError">
			<farm-col cols="12" md="4" sm="4">
				<farm-label for="form-type" required>
					Tipo
				</farm-label>
				<farm-select
					ref="type"
					id="form-type"
					item-text="label"
					item-value="id"
					v-model="form.typeId"
					:items="commercialProductTypes"
					:rules="[rules.required]"
					@change="onChangeType"
				/>
			</farm-col>
			<farm-col cols="12" md="4" sm="4">
				<farm-label for="form-status" required>
					Nome
				</farm-label>
				<farm-textfield-v2
					ref="name"
					id="form-name"
					uppercase
					required
					v-model="form.name"
					:rules="[rules.required, errorForm]"
					@change="onChangeInput"
				/>
			</farm-col>
			<farm-col cols="12" md="4" sm="4">
				<farm-label for="form-start-date" required>
					Data de Início
				</farm-label>
				<farm-input-datepicker
					inputId="form-start-date"
					position="top"
					maxText="A data de início deve ser anterior a data de finalização."
					v-model="form.startDate"
					required
					:max="onMaxDate()"
				/>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isError">
			<farm-col cols="12" md="4" sm="4">
				<farm-label for="form-end-date">
					Data de Finalização
				</farm-label>
				<farm-input-datepicker
					v-model="form.endDate"
					position="top"
					minText="A data de finalização deve ser posterior a data de início."
					inputId="form-end-date"
					hint="Esse campo será responsável pela inativação automática do Produto Comercial."
					persistent-hint
					:min="onEndDate(form.endDate)"
				/>
			</farm-col>
			<farm-col md="2" class="farm-col_switch" v-if="isEdit">
				<farm-label for="form-pf-status"> Status</farm-label>
				<farm-switcher
					class="mt-3"
					id="form-pf-status"
					v-model="status"
					block
				></farm-switcher>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isError && isFieldSponsor">
			<farm-col cols="12" md="4" sm="4" class="mt-6">
				<farm-label for="form-type" required>
					Parceiro (Sponsor)
				</farm-label>
				<farm-select-auto-complete
					ref="sponsor"
					id="form-sponsor"
					item-text="label"
					item-value="id"
					v-model="form.sponsor"
					:items="commercialProducSponsor"
					:rules="[rules.required]"
					@change="onChangeSponsor"
					@input="onInputSponsor"
				/>
			</farm-col>
			<farm-col cols="12" md="8" v-if="isShowList" class="mt-6">
				<list-information :data="dataListInformation"  class="mt-6"/>
			</farm-col>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-form>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch, onMounted, computed } from 'vue';

import ListInformation from '@/components/ListInformation';
import { useRouter } from '@/composibles';

import { useCommercialProductById } from '../../../../composables/useCommercialProductById';
import { useCommercialProductMobility } from '../../composibles/useCommercialProductMobility';
import { useCommercialProductMessage } from '../../composibles/useCommercialProductMessage';
import { listAssociation } from './configurations';

export default defineComponent({
	name: 'form-data',
	components: {
		ListInformation
	},
	props: {
		commercialProductTypes: {
			type: Array,
			required: true,
		},
		commercialProducSponsor: {
			type: Array,
			required: true,
		},
		isEdit: {
			type: Boolean,
			required: true,
		},
		errorForm: {
			type: [Boolean, String],
		},
	},
	setup(props, {emit}) {
		const { commercialProductTypes, commercialProducSponsor, isEdit, errorForm } = toRefs(props);

		const router = useRouter();
		const {
			getCommercialProductById,
			isLoadingCommercialProductById
		} = useCommercialProductById();
		const {
			getCommercialProductMobility,
			isLoadingCommercialProductMobility
		} = useCommercialProductMobility();
		const {
			updatedStatus
		} = useCommercialProductMessage();

		const firstRender = ref(true);
		const status = ref(true);
		const isValid = ref(null);
		const form = ref({
			typeId: '',
			name: '',
			sponsor: '',
			startDate: null,
			endDate: null
		});
		const rules = ref({
			required: text => !!text || 'Campo obrigatório',
		});
		const isError = ref(false);
		const isFieldSponsor = ref(false);
		const isShowList = ref(false);
		const dataListInformation = ref([]);

		const isLoading = computed(() => {
			return (
				isLoadingCommercialProductById.value ||
				isLoadingCommercialProductMobility.value
			);
		});

		function onChangeInput(): void {
			if (errorForm.value !== true) {
				emit('onUpdatedDisabledButton', true);
			}
		}

		function splitDate(date: string) {
			const [year, month, day] = date.split('-');
			return {
				year: parseInt(year, 10),
				month: parseInt(month, 10),
				day: parseInt(day, 10),
			};
		}

		function onEndDate(dateInput: string): string {
			if (dateInput && dateInput.length > 0) {
				const { year, month, day } = splitDate(dateInput);
				const dateAPI = new Date(year, month - 1, day);
				const dateNow = new Date();
				if (dateAPI > dateNow) {
					return new Date().toISOString();
				}
				return new Date(year, month - 1, day).toISOString();
			}
			return new Date().toISOString();
		}

		function onMaxDate(): string {
			return new Date().toISOString();
		}

		function updatedMobility(hasError, data): string {
			if(hasError){
				isError.value= true;
				return;
			}
			isError.value = false;
			const payload ={
				...form.value,
				enabled: status.value,
				configurations: data.edit
			};
			emit('onUpdatedDataForm', payload);
			firstRender.value = false;

		}

		function updatedPage(hasError, data): void {
			if(hasError){
				isError.value= true;
				return;
			}
					isError.value = false;
			form.value ={
				typeId: data.data.typeId,
				name: data.data.name,
				startDate: data.data.dateStart,
				endDate: data.data.dateEnd,
				sponsor: data.data.accountProductId
			};
			if(data.data.typeId === 2){
				const commercialProducSponsorSeleted = getCommercialProducSponsorSeleted(data.data.accountProductId);

				dataListInformation.value = mapToListAssociation(commercialProducSponsorSeleted);
				isShowList.value = true;
				isFieldSponsor.value = true;
			}
			status.value = parseInt(data.data.enabled, 10) === 1 ? true : false;
			getCommercialProductMobility({id: data.data.id}, updatedMobility);
		}

		function onChangeType(value): void {
			if(value === 2){
				isFieldSponsor.value = true;
				return;
			}
			isFieldSponsor.value = false;
		}

		function getCommercialProducSponsorSeleted(id){
			const newData = commercialProducSponsor.value.filter((item)=>{
				return item.id === id;
			});
			if(newData.length > 0){
				return newData[0];
			}
			return null;
		}

		function mapToListAssociation(data){
			if(data === null){
				return [];
			}
			const keys = [data.id, data.status, data.type];
			const newData = listAssociation.map((item, index)=>{
				return {
					...item,
					value: keys[index]
				};
			});
			return newData;
		}

		function onChangeSponsor(value): void {
			isShowList.value = false;
			const commercialProducSponsorSeleted = getCommercialProducSponsorSeleted(value);
			console.log('commercialProducSponsorSeleted: ', commercialProducSponsorSeleted);
			if(commercialProducSponsorSeleted !== null){
				dataListInformation.value = mapToListAssociation(commercialProducSponsorSeleted);
				isShowList.value = true;
				emit('onUpdatedDisabledButton', true);
			}
		}

		function onInputSponsor(value){
			if(value === null || value === null){
				emit('onUpdatedDisabledButton', false);
			}
		}

		function changeStatusProduct(): void{
			status.value = true;
		}

		function onReload(): void{
			load();
		}

		function load(): void {
			const payload = {
				id: router.currentRoute.params.id
			};
			getCommercialProductById(payload, updatedPage);
		}

		watch(()=> form, (newValue) => {
			const {name, startDate, typeId, sponsor} = newValue.value;
			if(name !== '' && startDate !== null && typeId !== ''){
				if(parseInt(typeId) === 2){
					emit('onUpdatedDisabledButton', sponsor === '' ? false : true);
					emit('onUpdatedDataForm', newValue);
					return;
				}
				emit('onUpdatedDisabledButton', true);
				emit('onUpdatedDataForm', newValue);
				return;
			}
			emit('onUpdatedDisabledButton', false);
		},{ deep: true });

		watch(status, (newValue) => {
			if(!newValue && !firstRender.value){
				updatedStatus(()=>{}, changeStatusProduct);
			}
			emit('onUpdatedDataForm', {...form.value, enabled: newValue, });
		});

		onMounted(() => {
			if(isEdit.value){
				load();
				return;
			}
			emit('onUpdatedDisabledButton', false);
		});

		return {
			isLoading,
			isError,
			isValid,
			isEdit,
			isFieldSponsor,
			isShowList,
			form,
			rules,
			status,
			commercialProductTypes,
			dataListInformation,
			errorForm,
			onEndDate,
			onMaxDate,
			onReload,
			onChangeInput,
			onChangeType,
			onChangeSponsor,
			onInputSponsor
		};
	}
});
</script>
