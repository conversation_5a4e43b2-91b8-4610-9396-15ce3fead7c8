<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" v-for="item in data" :key="item.key">
				<mobility-card :data="item"  class="mb-2" @updatedCheck="updatedCheck" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

import MobilityCard from '../MobilityCard';

export default defineComponent({
	name: 'mobility-list',
	components: {
		MobilityCard,
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
	},
	setup(props, { emit }) {
		const isDataEmpty = computed(() => props.data.length === 0);

		function updatedCheck(value): void {
			emit('updatedCheck', value);
		}

		return {
			isDataEmpty,
			updatedCheck
		};
	},
});
</script>
