<template>
	<farm-box>
		<financial-vehicle-filter-and-button-new
			v-if="!isError"
			@onSortSelect="onSortSelect"
			@onFilter="onFilter"
			@onFilterClicked="onFilterClicked"
			@onClickMainFilter="onClickMainFilter"
			@onInputChangeMainFilter="onInputChangeMainFilter"
		/>
		<financial-vehicle-list
			v-if="!isError"
			class="mt-4"
			:data="financialVehicle"
		/>
		<farm-row extra-decrease class="mb-4" v-if="!isError && financialVehicle.length > 0">
			<farm-box>
				<farm-datatable-paginator
					class="mt-6 mb-n6"
					:page="page"
					:totalPages="financialVehiclePagination.totalPages || 1"
					:initialLimitPerPage="filterCurrent.limit"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';

import { useRouter } from '@/composibles';
import { usePageable } from '@/composibles/usePageable';

import FinancialVehicleFilterAndButtonNew from '../FinancialVehicleFilterAndButtonNew';
import FinancialVehicleList from '../FinancialVehicleList';
import { useCommercialProductFinancialVehicle } from '../../composibles/useCommercialProductFinancialVehicle';

export default defineComponent({
	name: 'tab-financial-vehicle',
	components: {
		FinancialVehicleFilterAndButtonNew,
		FinancialVehicleList
	},
	setup() {
		const router = useRouter();
		const {
			financialVehicle,
			financialVehiclePagination,
			getCommercialProductFinancialVehicle,
			isLoadingCommercialProductFinancialVehicle
		} = useCommercialProductFinancialVehicle();
		const {
			page,
			isOpenFilter,
			isFilterCounter,
			pagination,
			onClickMainFilter,
			onInputChangeMainFilter,
			onFiltersApplied,
			onChangePage,
			onChangePageLimit,
			onSortSelect
		} = usePageable(
			{
				calbackFn: params => {
					if(!params.limit){
						params = {
							...params,
							limit: filterCurrent.value.limit || 10
						};
					}
					filterCurrent.value = { ...params };
					const dataParams = {
						id: router.currentRoute.params.id,
						filters:{
							...filterCurrent.value
						}
					};
					getCommercialProductFinancialVehicle(dataParams, updatedPage);
				},
				filters: {},
				keyInputSearch: 'search',
				sort: {
					order: 'DESC',
					orderby: 'associationStart',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			financialVehiclePagination.value
		);

		const isLoading = computed(() => {
			return isLoadingCommercialProductFinancialVehicle.value;
		});

		const isError = ref(false);
		const filterCurrent = ref({page: 0, limit: 10});

		function onFilter(data): void {
			filterCurrent.value ={
				...filterCurrent.value,
				...data
			};
			const payload = {
				id: router.currentRoute.params.id,
				filters: {
					...filterCurrent.value,
				}
			};
			getCommercialProductFinancialVehicle(payload, updatedPage);
		}

		function onFilterClicked(value): void{
			onFiltersApplied(value);
			if(value === false){
				filterCurrent.value = {
					page: 0,
					limit: filterCurrent.value.limit,
					order: 'DESC',
					orderby: 'associationStart',
				};
				const payload = {
					id: router.currentRoute.params.id,
					filters: {
						...filterCurrent.value,
					}
				};
				getCommercialProductFinancialVehicle(payload, updatedPage);
			}
		}

		function updatedPage(hasError): void{
			if(hasError){
				isError.value = true;
				return;
			}
			isError.value = false;
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			const payload = {
				id: router.currentRoute.params.id,
				filters: {
					page:0,
					limit:10,
				}
			};
			getCommercialProductFinancialVehicle(payload, updatedPage);
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			isOpenFilter,
			isFilterCounter,
			financialVehicle,
			financialVehiclePagination,
			filterCurrent,
			pagination,
			page,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onClickMainFilter,
			onInputChangeMainFilter,
			onFilterClicked,
			onFilter,
			onReload,
		};
	}
});
</script>
