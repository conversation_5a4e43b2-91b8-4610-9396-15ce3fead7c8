<template>
	<farm-box>
		<modal-allowed-suppliers
			v-if="isShowModalSuppliers"
			:supplierData="selectedSupplierData"
			:isModalOpen="isShowModalSuppliers"
			:closeModal="handleCloseModal"
			@dataSent="handleDataSent"
		/>

		<farm-modal
			v-model="showSuccessMessage"
			title="Sucesso"
			subtitle="Fornecedores alterados com sucesso"
			size="xs"
			:offsetTop="16"
			:offsetBottom="16"
		>
			<template v-slot:content>
				<farm-row class="px-3">
					<farm-row justify="space-between">
						<farm-bodytext :type="2" variation="bold" class="px-3"
							>Sucesso</farm-bodytext
						>
						<farm-dialog-header class="pb-8" @onClose="handlePromptClose" />
					</farm-row>
				</farm-row>

				<farm-bodytext :type="2" variation="regular"
					>Fornecedores alterados com sucesso!</farm-bodytext
				>
			</template>
		</farm-modal>

		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="6" v-for="(item, index) in data" :key="index">
				<financial-vehicles-card :data="item" @onAllowedSuppliers="openModal" />
			</farm-col>
		</farm-row>

		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente associar um veículo financeiro." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, ref, toRefs } from 'vue';
import FinancialVehiclesCard from '../FinancialVehiclesCard';
import ModalAllowedSuppliers from '../ModalAllowedSuppliers/ModalAllowedSuppliers.vue';

export default defineComponent({
	name: 'financial-vehicle-list',
	components: {
		FinancialVehiclesCard,
		ModalAllowedSuppliers,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	setup(props) {
		const { data } = toRefs(props);
		const isShowModalSuppliers = ref(false);
		const selectedSupplierData = ref(null);
		const showSuccessMessage = ref(false);

		const openModal = payload => {
			selectedSupplierData.value = payload;
			isShowModalSuppliers.value = true;
		};

		const handleCloseModal = () => {
			isShowModalSuppliers.value = false;
		};

		const handleDataSent = () => {
			isShowModalSuppliers.value = false;
			showSuccessMessage.value = true;
			setTimeout(() => {
				showSuccessMessage.value = false;
			}, 2000);
		};

		const handlePromptClose = () => {
			showSuccessMessage.value = false;
		};

		const isDataEmpty = computed(() => data.value.length === 0);

		return {
			isDataEmpty,
			openModal,
			handleCloseModal,
			handleDataSent,
			handlePromptClose,
			isShowModalSuppliers,
			selectedSupplierData,
			showSuccessMessage,
		};
	},
});
</script>
