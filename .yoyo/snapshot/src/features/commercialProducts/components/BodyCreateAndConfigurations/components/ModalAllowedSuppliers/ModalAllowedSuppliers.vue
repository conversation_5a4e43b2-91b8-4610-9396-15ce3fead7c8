<template>
	<farm-modal v-model="isModalOpen" :offsetBottom="68" size="default" :persistent="false">
		<template v-slot:content>
			<farm-row justify="space-between" class="mt-6 ml-2">
				<farm-idcaption icon="account-multiple" copyText="">
					<template v-slot:title>
						<farm-heading type="6"> Alterar Fornecedores Permitidos </farm-heading>
					</template>
					<template v-slot:subtitle>
						Selecione os fornecedores permitidos entre o produto comercial e o veículo
						financeiro.
					</template>
				</farm-idcaption>
			</farm-row>

			<farm-line class="mt-6 mb-6" />

			<farm-col col="12">
				<div class="product-info mb-4">
					<img src="@/assets/icons/bank.svg" alt="Imagem referente a dinheiro" />
					<farm-row>
						<farm-col cols="12">
							<farm-bodysmall
								:style="{ fontSize: ' 12px !important' }"
								variation="bold"
								>{{ supplierData.name }}</farm-bodysmall
							>
						</farm-col>
						<farm-col cols="12" class="detail-product">
							<farm-bodysmall class="supplier-info" variation="bold">
								ID:</farm-bodysmall
							>
							<farm-bodysmall class="supplier-info">
								{{ supplierData.id }} |
							</farm-bodysmall>
							<farm-bodysmall class="supplier-info" variation="bold">
								CNPJ:</farm-bodysmall
							>
							<farm-bodysmall class="supplier-info">
								{{ supplierData.listIdAndDocument[1].copyText }} |
							</farm-bodysmall>
							<farm-bodysmall class="supplier-info" variation="bold">
								Tipo:</farm-bodysmall
							>
							<farm-bodysmall class="supplier-info">
								{{ supplierData.type }}</farm-bodysmall
							>
						</farm-col>
					</farm-row>
				</div>

				<farm-row justify="space-between" class="filter">
					<farm-col md="8" sm="12" lg="8">
						<farm-form-mainfilter
							:hasExtraFilters="false"
							label="Buscar Fornecedor"
							@onInputChange="onInputChangeMainFilter"
						>
							<farm-label>Buscar Fornecedor</farm-label>
						</farm-form-mainfilter>
					</farm-col>
					<farm-col md="2" align="end" :style="{ paddingTop: '25px' }">
						<farm-select
							v-model="filterCurrent.defaultFilter"
							item-text="text"
							item-value="value"
							:items="sortByOptions"
							@change="onSortSelect"
						/>
					</farm-col>
				</farm-row>
			</farm-col>

			<farm-row justify="space-between" align="center" v-if="!isDataEmpty">
				<farm-col md="6">
					<farm-chip color="primary" variation="lighten" :dense="true">
						{{ authorizedProvidersCount > 0 ? authorizedProvidersCount : 0 }}
						selecionado(s)
					</farm-chip>
				</farm-col>
				<farm-col md="6" align="end">
					<div class="footer-form-buttons">
						<farm-btn
							class="farm-btn--responsive"
							title="Selecionar todos"
							outlined
							@click="selectAllProviders"
						>
							Selecionar todos
						</farm-btn>
						<farm-btn
							class="farm-btn--responsive ml-2"
							title="Desmarcar Selecionados"
							outlined
							@click="deselectAllProviders"
						>
							Desmarcar Selecionados
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>

			<provider-selected-list
				v-if="providersData.content && providersData.content.length > 0"
				:data="providersData.content"
				:unauthorized-providers="unauthorizedProviders"
				:listOfSelected="selectedProviders"
				@add-to-list="handleAddToAuthorizedListProvider"
				@remove-from-list="addToUnauthorizedProvidersList"
			/>
			<farm-row v-if="isDataEmpty">
				<farm-box>
					<farm-emptywrapper :bordered="false" />
				</farm-box>
			</farm-row>

			<farm-datatable-paginator
				:page="page"
				:totalPages="providersData.totalPages || 1"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
				:has-gutter="false"
				v-if="!isDataEmpty"
			/>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				:isConfirmDisabled="isDataEmpty"
				@onConfirm="sendData"
				@onClose="closeModal"
				close-label="Cancelar"
				confirmLabel="Salvar"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import ProviderSelectedList from '../ProviderSelectedList';
import {
	getProvidersByFinancialVehicle,
	getUnauthorizedProviders,
	updateUnauthorizedProviders,
} from '@/features/commercialProducts/services/services';
import { usePageable } from '@/composibles/usePageable';
import { useRouter } from '@/composibles';

export default {
	name: 'ModalAllowedSuppliers',
	props: {
		supplierData: {
			type: Object,
			required: true,
		},
		isModalOpen: {
			type: Boolean,
			default: false,
		},
		closeModal: {
			type: Function,
		},
	},
	components: {
		ProviderSelectedList,
	},
	setup(props, { emit }) {
		const router = useRouter();
		const commercialProductId = router.currentRoute.params.id;
		const totalProvidersLength = ref(0);

		const selectedProviders = ref([]);

		const unauthorizedProviders = ref([]);
		const isDataEmpty = ref(false);
		const providersData = ref({
			content: [],
			pageNumber: 0,
			pageSize: 10,
			totalPages: 1,
			totalElements: 0,
		});
		const filterCurrent = ref({
			page: 0,
			limit: 10,
			order: 'ASC',
			orderby: 'name',
			defaultFilter: 'name_ASC',
		});

		const sortByOptions = ref([
			{ text: 'Alfabético A-Z', value: 'name_ASC' },
			{ text: 'Alfabético Z-A', value: 'name_DESC' },
		]);

		const matchingProviders = computed(() => {
			const unauthorizedIds = unauthorizedProviders.value || [];
			return providersData.value.content.filter(provider =>
				unauthorizedIds.includes(provider.id)
			);
		});

		const authorizedProvidersCount = computed(() => {
			const totalProviders = totalProvidersLength.value;
			const unauthorizedCount = unauthorizedProviders.value.length;
			return totalProviders - unauthorizedCount;
		});

		const fetchUnauthorizedProviders = async () => {
			try {
				const response = await getUnauthorizedProviders({
					commercialProductId: commercialProductId,
					financialVehicleId: props.supplierData?.id,
				});
				unauthorizedProviders.value = response.data.content;
			} catch (error) {
				console.error('Erro ao buscar fornecedores não autorizados:', error);
				unauthorizedProviders.value = [];
			}
		};

		const fetchProviders = async (params = {}) => {
			try {
				//status: 1 is a filter to get only active
				const mergedParams = { ...params, status: 1, associated: 1 };

				const response = await getProvidersByFinancialVehicle({
					id: props.supplierData?.id,
					params: { ...filterCurrent.value, ...mergedParams },
				});

				const providersLength = await getProvidersByFinancialVehicle({
					id: props.supplierData?.id,
					params: { status: 1, associated: 1 },
				});

				if (response.status === 200) {
					providersData.value = { ...response.data };
					totalProvidersLength.value = providersLength.data.content.length;
					isDataEmpty.value = false;
				}
			} catch (error) {
				if (error.response?.status === 404) {
					isDataEmpty.value = true;
				} else {
					console.error('Erro ao buscar fornecedores:', error);
				}
				providersData.value = [];
				totalProvidersLength.value = 0;
			}
		};

		const sendData = async () => {
			try {
				const payload = {
					commercialProductId: commercialProductId,
					financialVehicleId: props.supplierData?.id,
					providerIds: unauthorizedProviders.value,
				};

				await updateUnauthorizedProviders(payload);

				emit('dataSent');
			} catch (error) {
				console.error('Erro ao enviar dados:', error);
			}
		};

		const { page, onChangePage, onChangePageLimit, onSortSelect, onInputChangeMainFilter } =
			usePageable(
				{
					calbackFn: params => fetchProviders({ ...params, status: 1, associated: 1 }),
					filters: {},
					sort: { order: 'ASC', orderby: 'name' },
					keyInputSearch: 'search',
					charInputSearch: 1,
				},
				providersData.value
			);

		const loadModalData = async () => {
			await Promise.all([fetchProviders(), fetchUnauthorizedProviders()]);
		};

		const handleAddToAuthorizedListProvider = id => {
			if (unauthorizedProviders.value.includes(id)) {
				unauthorizedProviders.value = unauthorizedProviders.value.filter(
					item => item !== id
				);
			}
		};

		const addToUnauthorizedProvidersList = id => {
			if (!unauthorizedProviders.value.includes(id)) {
				unauthorizedProviders.value.push(id);
			}
		};

		const selectAllProviders = () => {
			unauthorizedProviders.value = [];
			selectedProviders.value = providersData.value.content.map(provider => provider.id);
			emit('select-all');
		};

		const deselectAllProviders = () => {
			const allProviderIds = providersData.value.content.map(provider => provider.id);
			unauthorizedProviders.value = Array.from(
				new Set([...unauthorizedProviders.value, ...allProviderIds])
			);

			selectedProviders.value = [];

			emit('deselect-all');
		};

		watch(
			unauthorizedProviders,
			newValue => {
				unauthorizedProviders.value = newValue;
			},
			{ deep: true }
		);

		onMounted(async () => {
			if (props.isModalOpen) {
				await loadModalData();
			}
		});

		return {
			isDataEmpty,
			deselectAllProviders,
			selectedProviders,
			unauthorizedProviders,
			sendData,
			handleAddToAuthorizedListProvider,
			addToUnauthorizedProvidersList,
			matchingProviders,
			providersData,
			filterCurrent,
			sortByOptions,
			page,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onInputChangeMainFilter,
			authorizedProvidersCount,
			selectAllProviders,
		};
	},
};
</script>

<style scoped>
.product-info {
	margin-bottom: 1rem;
	display: flex;
	gap: 10px;
	margin: 0px -6px;
}
.filter {
	margin: 0px -20px;
}
.detail-product {
	display: flex;
	gap: 0.15rem;
}
.supplier-info {
	font-size: 12px !important;
	color: rgba(92, 92, 92, 1);
}
</style>
