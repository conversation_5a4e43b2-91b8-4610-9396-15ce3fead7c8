<template>
	<farm-row justify="space-between">
		<farm-col cols="12" md="6">
			<farm-form-mainfilter
				label="Buscar Veículo Financeiro"
				:showFilters="showFilters"
				@onClick="onShowFilters"
				@onInputChange="onInputChangeFilter"
			/>
		</farm-col>
		<farm-col cols="12" md="6" align="end">
			<farm-btn-confirm
				class="mt-8"
				customIcon="plus"
				title="Associar Veículo Financeiro"
				:icon="true"
				@click="onNewAssociation"
			>
				Associar Veículo Financeiro
			</farm-btn-confirm>
		</farm-col>
		<farm-col cols="12">
			<collapse-transition :duration="300">
				<financial-vehicle-filter-form
					v-show="showFilters"
					:financialVehicleTypes="financialVehicleTypes"
					@onFilterClicked="onFilterClicked"
					@onFilter="onFilter"
				/>
			</collapse-transition>
		</farm-col>
		<farm-col cols="12" md="9"></farm-col>
		<farm-col cols="12" md="3" align="end">
			<farm-select-auto-complete
				item-text="label"
				item-value="value"
				v-model="sortModel"
				:items="sortItems"
				@change="onChangeSort"
			/>
		</farm-col>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-4 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';

import { useRouter } from '@/composibles';

import FinancialVehicleFilterForm from '../FinancialVehicleFilterForm';
import { useFinancialVehicleRedirect } from '../../composibles/useFinancialVehicleRedirect';
import { useFinancialVehicleTypes } from '../../composibles/useFinancialVehicleTypes';

import { sort as sortItems } from './configurations';

export default defineComponent({
	name:'financial-vehicle-filter-and-button-new',
	components:{
		FinancialVehicleFilterForm
	},
	setup(_, { emit }){
		const router = useRouter();
		const {
			redirectNewAssociateFinancialVehicle
		} = useFinancialVehicleRedirect();
		const {
			financialVehicleTypes,
			getFinancialVehicleTypes,
			isLoadingFinancialVehicleTypes,
			isErrorFinancialVehicleTypes
		} = useFinancialVehicleTypes();

		const showFilters = ref(false);
		const sortModel = ref('associationStart_DESC');

		const isLoading = computed(() => {
			return (
				isLoadingFinancialVehicleTypes.value
			);
		});
		const isError = computed(() => {
			return (
				isErrorFinancialVehicleTypes.value
			);
		});

		function onNewAssociation(): void {
			const id = router.currentRoute.params.id;
			redirectNewAssociateFinancialVehicle(id);
		}

		function onChangeSort(value): void {
			emit('onSortSelect', value);
		}

		function onShowFilters(value): void {
			emit('onClickMainFilter', value);
			showFilters.value = !showFilters.value;
		}

		function onInputChangeFilter(value): void {
			emit('onInputChangeMainFilter', value);
		}

		function onFilterClicked(value): void{
			emit('onFilterClicked', value);
		}

		function onFilter(value): void {
			emit('onFilter', value);
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			getFinancialVehicleTypes();
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			financialVehicleTypes,
			showFilters,
			sortItems,
			sortModel,
			onNewAssociation,
			onShowFilters,
			onInputChangeFilter,
			onChangeSort,
			onFilterClicked,
			onFilter,
			onReload
		};
	}
});
</script>
