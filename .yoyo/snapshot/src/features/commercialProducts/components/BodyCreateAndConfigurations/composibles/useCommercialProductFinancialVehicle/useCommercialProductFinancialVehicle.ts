import { computed, ref } from 'vue';
import type { ComputedRef, Ref } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { getCommercialProductFinancialVehicle as getCommercialProductFinancialVehicleService } from '@/features/commercialProducts/services';
import { builderCommercialProductFinancialVehicle } from '@/features/commercialProducts/helpers/builderCommercialProductFinancialVehicle';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseCommercialProductFinancialVehicle = {
	financialVehicle: Ref<Array<any>>;
	financialVehiclePagination: Ref<any>;
	isLoadingCommercialProductFinancialVehicle: ComputedRef<boolean>;
	isErrorCommercialProductFinancialVehicle: ComputedRef<boolean>;
	getCommercialProductFinancialVehicle: Function;
};

export function useCommercialProductFinancialVehicle(): UseCommercialProductFinancialVehicle {
	let callFunc: Function | null = null;

	const financialVehicle = ref([]);
	const financialVehiclePagination = ref({});

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getCommercialProductFinancialVehicleService(params),
		onSuccess: (response) => {
			const { content, pagination } = builderCommercialProductFinancialVehicle(response);
			financialVehicle.value = content;
			financialVehiclePagination.value = pagination;
			if(callFunc) callFunc(false, null);
		},
		onError: (error) => {
			if (isHttpRequestError(error, 404)) {
				financialVehicle.value = [];
				if(callFunc) callFunc(false);
				return;
			}
			financialVehicle.value = [];
			financialVehiclePagination.value = null;
			if(callFunc) callFunc(true);
		},
	});

	const isLoadingCommercialProductFinancialVehicle = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProductFinancialVehicle = computed(() => {
		return isError.value;
	});

	function getCommercialProductFinancialVehicle(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
	}

	return {
		financialVehicle,
		financialVehiclePagination,
		isLoadingCommercialProductFinancialVehicle,
		isErrorCommercialProductFinancialVehicle,
		getCommercialProductFinancialVehicle,
	};
}
