import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { createCommercialProduct as createCommercialProductService } from '@/features/commercialProducts/services';
import { builderCreateCommercialProduct } from '@/features/commercialProducts/helpers/builderCreateCommercialProduct';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseCreateCommercialProduct = {
	isLoadingCreateCommercialProduct: ComputedRef<boolean>;
	isErrorCreateCommercialProduct: ComputedRef<boolean>;
	createCommercialProduct: Function;
};

export function useCreateCommercialProduct(): UseCreateCommercialProduct {
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => createCommercialProductService(params),
		onSuccess: response => {
			const data = builderCreateCommercialProduct(response);
			if (callFunc) callFunc(false, data);
		},
		onError: error => {
			if (isHttpRequestError(error, 400)) {
				if (callFunc) callFunc(true, { duplicated: true });
				return;
			}
			if (callFunc) callFunc(true, null);
		},
	});

	const isLoadingCreateCommercialProduct = computed(() => {
		return isLoading.value;
	});

	const isErrorCreateCommercialProduct = computed(() => {
		return isError.value;
	});

	function createCommercialProduct(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
	}

	return {
		isLoadingCreateCommercialProduct,
		isErrorCreateCommercialProduct,
		createCommercialProduct,
	};
}
