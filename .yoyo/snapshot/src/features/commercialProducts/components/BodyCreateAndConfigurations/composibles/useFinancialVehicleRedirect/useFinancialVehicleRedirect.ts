import { useRouter } from '@/composibles/useRouter';

type UseFinancialVehicleRedirect = {
	redirectNewAssociateFinancialVehicle: Function;
	redirectFinancialVehicle: Function;
	forceRedirectFinancialVehicle: Function;
};

export function useFinancialVehicleRedirect(): UseFinancialVehicleRedirect {
	const router = useRouter();

	function redirectNewAssociateFinancialVehicle(id): void {
		router.push(`/admin/cadastros/produto_comercial/${id}/associacao_veiculo_financeiro`);
	}

	function redirectFinancialVehicle(id): void {
		
		router.push(`/admin/cadastros/produto_comercial/${id}/editar?path=veiculos_financeiro`);
	}

	function forceRedirectFinancialVehicle(id): void {
		router.push(`/admin/cadastros/produto_comercial/${id}/editar?path=veiculos_financeiro`);
		router.go(0);
	}

	return {
		redirectNewAssociateFinancialVehicle,
		redirectFinancialVehicle,
		forceRedirectFinancialVehicle
	};
}
