import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { editCommercialProduct as editCommercialProductService } from '@/features/commercialProducts/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseEditCommercialProduct = {
	isLoadingEditCommercialProduct: ComputedRef<boolean>;
	isErrorEditCommercialProduct: ComputedRef<boolean>;
	editCommercialProduct: Function;
};

export function useEditCommercialProduct(): UseEditCommercialProduct {
	let payloadCache = null;
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => editCommercialProductService(params),
		onSuccess: () => {
			if(callFunc) callFunc(false, payloadCache);
		},
		onError: (error) => {
			if (isHttpRequestError(error, 400)) {
				if (callFunc) callFunc(true, { duplicated: true });
				return;
			}
			if(callFunc) callFunc(true, null);
		},
	});

	const isLoadingEditCommercialProduct = computed(() => {
		return isLoading.value;
	});

	const isErrorEditCommercialProduct = computed(() => {
		return isError.value;
	});

	function editCommercialProduct(payload, callback: Function) {
		mutate(payload);
		callFunc = callback;
		payloadCache = payload;
	}

	return {
		isLoadingEditCommercialProduct,
		isErrorEditCommercialProduct,
		editCommercialProduct,
	};
}
