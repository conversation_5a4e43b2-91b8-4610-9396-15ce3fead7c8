<template>
	<farm-container>
		<informations-header @onUpdatedFooter="() => {}" withLine />
		<title-page-form value="Associar Veículo Financeiro" noPipe />
		<farm-row v-if="!isError">
			<farm-col cols="12" md="4">
				<farm-label for="form-address-type" required>
					Veículo Financeiro
				</farm-label>
				<farm-select-auto-complete
					id="form-address-type"
					item-text="name"
					item-value="id"
					v-model="valueModel"
					:items="associationFinancialVehicle"
					:rules="[rules.required]"
					@change="onChange"
				/>
			</farm-col>
			<farm-col cols="12" md="8" v-if="isShowFields">
				<list-information :data="dataListInformation" class="mt-6" />
			</farm-col>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<footer-form
			labelButton="Associar"
			:isDisabledButton="isDisabledButton"
			:showLayoutData="false"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue';

import FooterForm from '@/components/FooterForm';
import ListInformation from '@/components/ListInformation';
import TitlePageForm from '@/components/TitlePageForm';
import { useRouter } from '@/composibles';

import InformationsHeader from '../InformationsHeader';
import {
	useAddAssociationFinancialVehicle
 } from './composibles/useAddAssociationFinancialVehicle';
import {
	useAssociationFinancialVehicle
 } from './composibles/useAssociationFinancialVehicle';
import {
	useRedirectFinancialVehicle
} from '../../composables/useRedirectFinancialVehicle';
import { listAssociation } from './configurations';

export default defineComponent({
	name: 'body-association-with-financial-vehicle',
	components: {
		InformationsHeader,
		FooterForm,
		TitlePageForm,
		ListInformation
	},
	setup() {
		const router = useRouter();
		const {
			redirectFinancialVehicle
		} = useRedirectFinancialVehicle();
		const {
			associationFinancialVehicle,
			getAssociationFinancialVehicle,
			isErrorAssociationFinancialVehicle,
			isLoadingAssociationFinancialVehicle
		} = useAssociationFinancialVehicle();
		const {
			createAssociationFinancialVehicle,
			isLoadingAddAssociationFinancialVehicle
		} = useAddAssociationFinancialVehicle();

		const valueModel = ref(null);
		const isDisabledButton = ref(false);
		const isShowFields = ref(false);
		const dataListInformation = ref([]);
		const rules = ref({
			required: text => !!text || 'Campo obrigatório',
		});

		const isLoading = computed(() => {
			return (
				isLoadingAssociationFinancialVehicle.value ||
				isLoadingAddAssociationFinancialVehicle.value
			);
		});
		const isError = computed(() => {
			return (
				isErrorAssociationFinancialVehicle.value
			);
		});

		function getFinancialVehicleSeleted(id){
			const newData = associationFinancialVehicle.value.filter((item)=>{
				return item.id === id;
			});
			if(newData.length > 0){
				return newData[0];
			}
			return null;
		}

		function mapToListAssociation(data){
			const keys = [data.id, data.code, data.document];
			const newData = listAssociation.map((item, index)=>{
				return {
					...item,
					value: keys[index]
				};
			});
			return newData;
		}

		function onChange(value): void {
			isShowFields.value = false;
			const financialVehicleSeleted = getFinancialVehicleSeleted(value);
			if(financialVehicleSeleted !== null){
				dataListInformation.value = mapToListAssociation(financialVehicleSeleted);
				isShowFields.value = true;
			}
		}

		function callRedirect(): void {
			const id = router.currentRoute.params.id;
			redirectFinancialVehicle(id);
		}

		function onSave(): void {
			const payload = {
				id: router.currentRoute.params.id,
				payload:{
					financialVehicleId: valueModel.value
				}
			};
			createAssociationFinancialVehicle(payload, callRedirect);
		}

		function onCancel(): void {
			const id = router.currentRoute.params.id;
			redirectFinancialVehicle(id);
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			const payload = {
				id: router.currentRoute.params.id
			};
			getAssociationFinancialVehicle(payload);
		}

		watch(valueModel, (newValue) => {
			if(newValue !== null){
				isDisabledButton.value = true;
				return;
			}
			isDisabledButton.value = false;
			isShowFields.value = false;
		});

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			isDisabledButton,
			isShowFields,
			valueModel,
			rules,
			associationFinancialVehicle,
			dataListInformation,
			onCancel,
			onSave,
			onReload,
			onChange
		};
	}
});
</script>
