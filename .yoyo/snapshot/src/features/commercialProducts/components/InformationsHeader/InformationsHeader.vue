<template>
	<farm-box class="mb-6">
		<farm-row v-if="!isError">
			<farm-col cols="12">
				<farm-heading :type="6" class="mb-4">
					{{ title || 'Carregando...' }}
				</farm-heading>
			</farm-col>
			<farm-col cols="12">
				<list-information
					class="mb-4"
					:data="dataList"
					:messageSucess="''"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="!isError && withLine">
			<farm-line noSpacing />
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';

import ListInformation from '@/components/ListInformation';
import { useRouter } from '@/composibles';

import { headersPages } from './configurations';
import { useCommercialProductById } from '../../composables/useCommercialProductById';

export default defineComponent({
	name: 'informations-header',
	components: {
		ListInformation
	},
	props:{
		withLine: {
			type:Boolean,
			default:false
		},
	},
	setup(_, { emit }){
		const router = useRouter();
		const {
			getCommercialProductById,
			isLoadingCommercialProductById
		} = useCommercialProductById();

		const title = ref('');
		const dataList = ref(headersPages);
		const isError = ref(false);

		const isLoading = computed(() => {
			return (
				isLoadingCommercialProductById.value
			);
		});

		function updatedHeaders(data): void {
			title.value = data.name;
			const keys = ['type', 'id', 'status'];
			const newData = dataList.value.map((item, index) => {
				return {
					...item,
					value: data[keys[index]]
				};
			});
			dataList.value = [...newData];
		}

		function updatedPage(hasError, data): void {
			if(hasError){
				isError.value= true;
				return;
			}
			isError.value = false;
			updatedHeaders(data.data);
			onUpdatedFooter(data.meta);
		}

		function onUpdatedFooter(value): void {
			emit('onUpdatedFooter', value);
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			const payload = {
				id: router.currentRoute.params.id
			};
			getCommercialProductById(payload, updatedPage);
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			title,
			dataList,
			onReload
		};
	}
});
</script>
