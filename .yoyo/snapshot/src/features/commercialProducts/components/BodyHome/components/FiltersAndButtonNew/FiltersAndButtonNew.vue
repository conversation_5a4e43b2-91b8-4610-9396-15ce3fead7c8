<template>
	<farm-box>
		<farm-row justify="space-between" align="center" v-if="!isError">
			<farm-col cols="12" md="8" lg="6">
				<farm-form-mainfilter
					label="Buscar Produto Comercial"
					:showFilters="isOpenFilter"
					@onClick="onOpenMainFilter"
					@onInputChange="onInputChangeMainFilter"
				>
					<div class="d-flex">
						<farm-label class="mb-0 mr-2">
							Buscar Produto Comercial
							<farm-tooltip>
								Realize sua busca pelo ID ou Nome do Produto Comercial
								<template v-slot:activator>
									<farm-icon size="sm" color="gray">help-circle</farm-icon>
								</template>
							</farm-tooltip>
						</farm-label>
						<label-request-results
							v-if="isFilterCounter"
							:totalItems="pagination.pageSize || 0"
						/>
					</div>
				</farm-form-mainfilter>
			</farm-col>
			<farm-col cols="12" md="4" class="d-md-flex justify-end">
				<farm-btn color="primary" @click="onAddNewCommercialProduct">
					<farm-icon>plus</farm-icon>
					Adicionar Produto Comercial
				</farm-btn>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<filter-form
				v-show="isOpenFilter && !isError"
				:commercialProductType="commercialProductType"
				@onFilterClicked="onFilterClicked"
				@onFilter="onFilter"
			/>
		</collapse-transition>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-4 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from 'vue';

import LabelRequestResults from '@/components/LabelRequestResults';
import { useRedirectCommercialProduct } from '@/features/commercialProducts/composables/useRedirectCommercialProduct';
import { useFilterCommercialProductType } from '@/features/commercialProducts/composables/useFilterCommercialProductType';

import FilterForm from '../FilterForm';

export default defineComponent({
	name: 'filters-and-button-new',
	props: {
		isOpenFilter: {
			type: Boolean,
			required: true,
		},
		isFilterCounter: {
			type: Boolean,
			required: true,
		},
		pagination: {
			type: Object,
			require: true,
		},
	},
	components: {
		FilterForm,
		LabelRequestResults,
	},
	setup(_, { emit }) {
		const { redirectNewCommercialProduct } = useRedirectCommercialProduct();
		const {
			commercialProductType,
			getCommercialProductType,
			isErrorCommercialProductType,
			isLoadingCommercialProductType,
		} = useFilterCommercialProductType();

		const isLoading = computed(() => {
			return isLoadingCommercialProductType.value;
		});
		const isError = computed(() => {
			return isErrorCommercialProductType.value;
		});

		function onOpenMainFilter(data): void {
			emit('onClickMainFilter', data);
		}

		function onInputChangeMainFilter(data): void {
			emit('onInputChangeMainFilter', data);
		}

		function onAddNewCommercialProduct(): void {
			redirectNewCommercialProduct();
		}

		function onFilter(data): void {
			emit('onFilter', data);
		}

		function onFilterClicked(data): void {
			emit('onFilterClicked', data);
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			getCommercialProductType();
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			commercialProductType,
			onReload,
			onAddNewCommercialProduct,
			onOpenMainFilter,
			onInputChangeMainFilter,
			onFilter,
			onFilterClicked,
		};
	},
});
</script>
