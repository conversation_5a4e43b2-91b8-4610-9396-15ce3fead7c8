<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" md="3" sm="3">
				<farm-label for="filter-status">Status</farm-label>
				<farm-select
					ref="status"
					id="filter-status"
					item-text="label"
					item-value="id"
					v-model="statusModel"
					:items="itemsStatus"
				/>
			</farm-col>
			<farm-col cols="12" md="3" sm="3">
				<farm-label for="filter-type">Tipo</farm-label>
				<farm-select
					ref="type"
					id="filter-type"
					item-text="label"
					item-value="id"
					v-model="typeModel"
					:items="commercialProductType"
				/>
			</farm-col>
			<farm-col cols="12" md="3" sm="3">
				<farm-label for="filter-start-date">Data de Início (Início/fim)</farm-label>
				<farm-input-rangedatepicker
					inputId="filter-start-date"
					v-model="rangeDatepickerStartDate"
					@input="onInputDatepickerDateStart"
				/>
			</farm-col>
			<farm-col cols="12" md="3" sm="3">
				<farm-label for="filter-end-date"
					>Data de Finalização (Início/fim)
				</farm-label>
				<farm-input-rangedatepicker
					inputId="filter-end-date"
					v-model="rangeDatepickerEndDate"
					@input="onInputDatepickerDateEnd"
				/>
			</farm-col>
		</farm-row>
		<farm-row justify="start" align="center">

			<farm-col cols="12" md="9" sm="9">
				<farm-btn
					class="mr-2"
					title="Aplicar Filtros"
					outlined
					@click="applyFilters"
				>
					Aplicar Filtros
				</farm-btn>
				<farm-btn
					plain
					title="Limpar Filtros"
					color="primary"
					@click="onClickClearFilter"
				>
					Limpar Filtros
				</farm-btn>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name:'filter-form',
	props: {
		commercialProductType: {
			type: Array,
			required: true
		},
	},
	setup(_, { emit }) {
		const rangeDatepickerStartDate = ref([]);
		const rangeDatepickerEndDate = ref([]);
		const statusModel = ref('');
		const itemsStatus = ref([
			{ id: '0', label: 'INATIVO' },
			{ id: '1', label: 'ATIVO' }
		]);
		const typeModel = ref('');

		function checkDatepickerCompleted(data, keyStart, keyEnd) {
			return {
				[keyStart]: data[0],
				[keyEnd]: data[1]
			};
		}

		function checkValueDatepicker(value) {
			if (value.length === 2) {
				const startDate = new Date(value[0]);
				const endDate = new Date(value[1]);
				if (startDate > endDate) {
					return [value[1], value[0]];
				}
			}
			return value;
		}

		function onInputDatepickerDateStart(value: string): void {
			rangeDatepickerStartDate.value = checkValueDatepicker(value);
		}

		function onInputDatepickerDateEnd(value: string): void {
			rangeDatepickerEndDate.value = checkValueDatepicker(value);
		}

		function validDataFilter() {
			let obj = {};
			if(statusModel.value !== '') {
				obj = {
					enabled: statusModel.value
				};
			}
			if(typeModel.value !== '') {
				obj = {
					...obj,
					typeId: typeModel.value
				};
			}
			if(rangeDatepickerStartDate.value && rangeDatepickerStartDate.value.length === 2){
				const result = checkDatepickerCompleted(
					rangeDatepickerStartDate.value,
					'startAtStart',
					'startAtEnd'
				);
				obj = {
					...obj,
					...result
				};
			}
			if(rangeDatepickerEndDate.value && rangeDatepickerEndDate.value.length === 2){
				const result = checkDatepickerCompleted(
					rangeDatepickerEndDate.value,
					'endAtStart',
					'endAtEnd'
				);
				obj = {
					...obj,
					...result
				};
			}
			return obj;
		}

		function onClickClearFilter(): void {
			rangeDatepickerStartDate.value = null;
			rangeDatepickerEndDate.value = null;
			statusModel.value = null;
			typeModel.value = null;
			emit('onFilterClicked', false);
		}

		function applyFilters(): void {
			const payload = validDataFilter();
			emit('onFilter', payload);
			emit('onFilterClicked', true);
		}

		return {
			rangeDatepickerStartDate,
			rangeDatepickerEndDate,
			itemsStatus,
			statusModel,
			typeModel,
			onClickClearFilter,
			applyFilters,
			onInputDatepickerDateStart,
			onInputDatepickerDateEnd
		};
	}
});
</script>
