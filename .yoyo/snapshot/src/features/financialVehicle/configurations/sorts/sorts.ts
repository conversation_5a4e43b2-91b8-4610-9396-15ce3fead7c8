export const providerSortOptions = [
	{ label: 'Associação Mais Recente - Menos Recente', value: 'startRelationship_DESC' },
	{ label: 'Associação Menos Recente - Mai<PERSON>', value: 'startRelationship_ASC' },
	{ label: 'Fim de Associação Mais Recente - Menos Recente', value: 'endRelationship_DESC' },
	{ label: 'Fim de Associação Menos Recente - Mais Recente', value: 'endRelationship_ASC' },
	{ label: 'Alfabético A-Z', value: 'name_ASC' },
	{ label: 'Alfabético Z-A', value: 'name_DESC' },
];

export const providerAssociationSortOptions = [
	{ label: 'Cadastro Mais Recente - Menos Recente', value: 'createdAt_DESC' },
	{ label: 'Cadastro Menos Recente - Mai<PERSON> Recent<PERSON>', value: 'createdAt_ASC' },
	{ label: 'Alfabético A-Z', value: 'name_ASC' },
	{ label: 'Alfabético Z-A', value: 'name_DESC' },
];

export const productsSort = [
	{ label: 'Associação Mais Recente - Menos Recente', value: 'startRelationship_DESC' },
	{ label: 'Associação Menos Recente - Mai<PERSON> Recente', value: 'startRelationship_ASC' },
	{ label: 'Fim de Associação Mais Recente - Menos Recente', value: 'endRelationship_DESC' },
	{ label: 'Fim de Associação Menos Recente - Mais Recente', value: 'endRelationship_ASC' },
	{ label: 'Alfabético A-Z', value: 'name_ASC' },
	{ label: 'Alfabético Z-A', value: 'name_DESC' },
];

export const listCFOPSort = [
	{
		label: 'Código Crescente - Código Decrescente',
		value: 'ASC',
	},
	{
		label: 'Código Decrescente - Código Crescente',
		value: 'DESC',
	},
];
