<template>
	<div class="d-flex">
		<div>{{ label }}</div>
		<div class="pl-4">
			<farm-chip color="neutral" :dense="true" v-if="hasItems">
				<farm-typography size="sm" color="neutral" color-variation="darken" tag="span">
					{{ getQuantItems }}
				</farm-typography>
			</farm-chip>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	props: {
		label: {
			type: String,
		},
		dataSelected: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		hasItems(): boolean {
			return this.dataSelected.length > 0;
		},
		getQuantItems(): string {
			const count = this.dataSelected.length;
			return this.$tc('selected', count, { count });
		},
	},
});
</script>
