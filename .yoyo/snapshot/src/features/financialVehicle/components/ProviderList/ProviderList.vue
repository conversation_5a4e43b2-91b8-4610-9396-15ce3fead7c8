<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" yGridGutters>
			<farm-col cols="12" md="6" v-for="(item, index) in data" :key="index">
				<ProviderCard
					:data="item"
					@handleFinalizeAssociation="handleFinalizeAssociation"
					@handleReAssociateOption="handleReAssociateOption"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente associar um fornecedor." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import ProviderCard from '../ProviderCard';

export default defineComponent({
	components: {
		ProviderCard,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
	methods: {
		handleFinalizeAssociation(data): void {
			this.$emit('handleFinalizeAssociation', data);
		},
		handleReAssociateOption(data): void {
			this.$emit('handleReAssociateOption', data);
		},
	},
});
</script>
