<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				hide-default-footer
				:class="{
					'elevation-0 mt-0 table-register-financial-vehicle': true,
					'mb-8': data.length === 0,
				}"
				:headers="headers"
				:items="data"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader()"
				:options.sync="options"
				:header-props="headerProps"
			>
				<template slot="no-data">
					<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" />
				</template>
				<template v-slot:header="{ props: { headers } }" v-if="showCustomHeader()">
					<farm-datatable-header
						firstSelected
						:headers="headers"
						:sortClick="sortClicked"
						@onClickSort="onSort"
					/>
				</template>

				<template v-slot:[`item.startDate`]="{ item }">
					{{ defaultDateFormat(item.startDate) }}
				</template>

				<template v-slot:[`item.endDate`]="{ item }">
					{{ defaultDateFormat(item.endDate) }}
				</template>

				<template v-slot:[`item.infos`]="{ item }">
					<farm-context-menu :items="contextMenuItems(item)" @edit="editItem(item)" />
				</template>

				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="data.length > 0"
						class="mt-6 mb-n6"
						:page="paginationPageActive"
						:totalPages="paginationTotalPages"
						@onChangePage="onChangePageTable"
						@onChangeLimitPerPage="onChangeLimitPerPageTable"
					/>
				</template>
			</v-data-table>
		</farm-box>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { defaultDateFormat, edit as editOption } from '@farm-investimentos/front-mfe-libs-ts';

import { headers } from '../../configurations/headers';

export default defineComponent({
	props: {
		data: {
			type: Array,
			require: true,
		},
		paginationTotalPages: {
			type: Number,
			require: true,
		},
		paginationPageActive: {
			type: Number,
			default: 1,
		},
		filterCurrent: {
			type: Object,
			require: true,
		},
	},
	data() {
		return {
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
			sortClicked: [],
			headerProps: {
				sortByText: 'Ordenar por',
			},
			headers,
			options: {},
		};
	},
	computed: {
		breakpoint(): string {
			return this.$vuetify.breakpoint.name;
		},
	},
	methods: {
		onSort(data): void {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			const parseOrderby = {
				type: 'financialVehicleTypeId',
				name: 'name',
				endDate: 'endDate',
				startDate: 'startDate',
			};
			const filtersActive = {
				...this.filterCurrent,
				orderby: parseOrderby[data.field],
				order: data.descending,
			};
			this.$emit('onRequest', filtersActive);
		},
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			return [editOption];
		},

		editItem(item): void {
			this.$router.push({
				path: `/admin/cadastros/veiculos_financeiros/${item.id}/editar`,
			});
		},
		defaultDateFormat,
		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		onChangePageTable(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			this.filterCurrent.page = pageActive;
			this.$emit('onRequest', { ...this.filterCurrent, page: pageActive });
		},
		onChangeLimitPerPageTable(limit: number): void {
			this.filterCurrent.limit = limit;
			this.$emit('onRequest', { ...this.filterCurrent, page: 0, limit: limit });
		},
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-register-financial-vehicle', 1, (0));
</style>
