<template>
	<Cards>
		<template slot="header">
			<farm-row align="center">
				<farm-col cols="8">
					<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
					<CardListTextHeader noSpacing class="mb-1" :data="listIdAndRaiz" />
				</farm-col>
				<farm-col cols="4" align="end">
					<StatusActiveAndInactive :status="data.status" dense class="mr-2" />
					<farm-context-menu
						:items="contextMenuItems(data)"
						@finalizeAssociation="handleFinalizeAssociation(data)"
						@reAssociate="handleReAssociateOption(data)"
					/>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="6">
					<CardTextBody
						label="Início de Associação"
						:value="formatDateOrNA(data.startRelationship)"
					/>
				</farm-col>
				<farm-col cols="6">
					<CardTextBody
						label="Fim de Associação"
						:value="formatDateOrNA(data.endRelationship)"
					/>
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
	reAssociate as reAssociateOption,
	finalizeAssociation as finalizeAssociationOption,
} from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';
import { formatDateOrNA } from '@/helpers/formatCards';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
	},
	data() {
		return {
			formatDateOrNA,
			listIdAndRaiz: [
				{ label: 'ID', value: this.data.id, copyText: '' },
				{
					label: 'Raiz',
					value: this.data.raiz,
					copyText: this.data.raiz,
					successMessage: 'Raiz copiado para área de transferência!',
				},
			],
		};
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	methods: {
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}

			if (this.data.endRelationship === null) {
				return [finalizeAssociationOption];
			} else {
				return [reAssociateOption];
			}
		},
		handleFinalizeAssociation(item): void {
			this.$emit('handleFinalizeAssociation', item);
		},
		handleReAssociateOption(item): void {
			this.$emit('handleReAssociateOption', item);
		},
	},
});
</script>
