<template>
	<farm-box>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Produto"
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<ProductsFilters v-show="filter" key="filters" @onApply="searchListener" />
		</collapse-transition>
		<ProductsList v-if="!isError" :data="dataList" />
		<farm-row extra-decrease v-if="isPagination">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { pageable, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import ProductsFilters from '../ProductsFilters';
import ProductsList from '../ProductsList';
import { productsSort as sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		ProductsFilters,
		ProductsList,
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'startRelationship_DESC',
			sortOptions: sort,
			lastSearchFilters: { page: 0, limit: 10 },
			filter: false,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'startRelationship',
				order: 'DESC',
			},
			filterInputKey: 'search',
			dataFilter: [],
			dataList: [],
			totalPages: 1,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			financialVehicleHeaderRequestStatus: 'financialVehicleHeaderRequestStatus',
			financialVehicleHeaderData: 'financialVehicleHeaderData',
			financialVehicleAssociatedProductsData: 'financialVehicleAssociatedProductsData',
			financialVehicleAssociatedProductsRequestStatus:
				'financialVehicleAssociatedProductsRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.financialVehicleHeaderRequestStatus,
				this.financialVehicleAssociatedProductsRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.financialVehicleHeaderRequestStatus.type,
				this.financialVehicleAssociatedProductsRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		isPagination(): boolean {
			return !this.isError && this.dataList.length > 0;
		},
		currentId(): number {
			return this.$route.params.id;
		},
	},
	methods: {
		...mapActions('cadastros', {
			getFinancialVehicleHeaders: 'getFinancialVehicleHeaders',
			getFinancialVehicleProducts: 'getFinancialVehicleProducts',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getFinancialVehicleProducts({
				id: this.currentId,
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		reload(): void {
			this.doSearch();
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.getFinancialVehicleProducts({
				id: this.currentId,
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		updatedHeader(): void {
			const dataHeader = {
				title: this.financialVehicleHeaderData.content.name,
				messageSucess: 'Documento copiado para área de transferência!',
				listIcons: [
					this.financialVehicleHeaderData.content.id,
					this.financialVehicleHeaderData.content.type,
					[null, this.financialVehicleHeaderData.content.document],
				],
			};
			this.$emit('onUpdatedHeader', dataHeader);
		},
		updatedListCards(): void {
			this.dataList = this.financialVehicleAssociatedProductsData.content;
		},
		updateTotalPages(): void {
			this.totalPages = this.financialVehicleAssociatedProductsData.totalPages;
		},
	},
	watch: {
		financialVehicleHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader();
			}
		},
		financialVehicleAssociatedProductsRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedListCards();
				this.updateTotalPages();
			}
		},
	},
	mounted(): void {
		this.getFinancialVehicleHeaders({
			financialVehicleId: this.currentId,
		});
		this.doSearch();
	},
});
</script>
