<template>
	<farm-container>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" sm="7" md="6">
				<farm-form-mainfilter
					label="Buscar Veículo Financeiro"
					@onInputChange="filterInputChanged"
					:hasExtraFilters="false"
				/>
			</farm-col>
			<farm-col cols="12" sm="5" md="6" align="right">
				<farm-btn-confirm
					v-if="canWrite"
					class="v-btn--responsive mt-8"
					dense
					title="Adicionar Veículo Financeiro"
					customIcon="plus"
					to="/admin/cadastros/veiculos_financeiros/novo"
					:icon="true"
				>
					Adicionar Veículo Financeiro
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<HomeTable
			v-if="!isError"
			:data="dataTable"
			:filterCurrent="filters"
			:paginationTotalPages="paginationTotalPages"
			:paginationPageActive="currentPage"
			@onRequest="onRequest"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-container>
</template>

<script lang="ts">
import { mapActions, mapGetters } from 'vuex';
import { defineComponent } from 'vue';
import { RequestStatusEnum, pageable } from '@farm-investimentos/front-mfe-libs-ts';

import HomeTable from '../HomeTable';

export default defineComponent({
	components: {
		HomeTable,
	},
	mixins: [pageable],
	data() {
		return {
			lastSearchFilters: {},
			filterInputKey: 'name',
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'createdAt',
				order: 'DESC',
			},
			dataTable: [],
			paginationTotalPages: 0,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			financialVehicleData: 'financialVehicleData',
			financialVehicleDataRequestStatus: 'financialVehicleDataRequestStatus',
		}),
		isLoading(): boolean {
			return this.financialVehicleDataRequestStatus === RequestStatusEnum.START;
		},
		isError(): boolean {
			return this.financialVehicleDataRequestStatus.type === RequestStatusEnum.ERROR;
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getFinancialVehicle: 'getFinancialVehicle',
		}),
		reload(): void {
			this.doSearch();
		},
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getFinancialVehicle({
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		onRequest(filtersActive): void {
			this.lastSearchFilters = { ...filtersActive };
			const payload = {
				filters: filtersActive,
			};
			this.getFinancialVehicle(payload);
		},
		updatedTable(): void {
			this.dataTable = this.financialVehicleData.content;
			this.paginationTotalPages = this.financialVehicleData.totalPages;
		},
	},
	watch: {
		financialVehicleDataRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedTable();
			}
			return newValue;
		},
	},
});
</script>
