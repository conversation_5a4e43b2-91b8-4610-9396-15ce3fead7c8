<template>
	<farm-container>
		<HeaderForm :data="dataHeaderForm" withLine />
		<farm-box>
			<farm-row>
				<farm-col cols="12">
					<farm-idcaption
						tooltipColor="gray"
						key="list-information-item-1"
						icon="link"
						:class="{
							'list-information-item': true,
							'remove-copy-text': true,
						}"
					>
						<template #title>
							<farm-typography size="lg" weight="500">
								Associar CFOP
							</farm-typography>
						</template>
						<template #subtitle>
							<farm-typography size="sm">
								Selecione os CFOPs permitidos neste Veículo Financeiro. Lembre-se
								que ao menos 1 CFOP deve estar ativo.
							</farm-typography>
						</template>
					</farm-idcaption>
				</farm-col>
			</farm-row>

			<farm-row class="mt-6 justify-space-between">
				<farm-col cols="12" md="6">
					<farm-form-mainfilter
						label="Buscar CFOP"
						tooltip="Realize sua busca pelo código do CFOP"
						:showFilters="showFilters"
						@onInputChange="filterInputChanged"
						@onClick="toggleFilters"
					/>
				</farm-col>

				<farm-col cols="12" md="3" align="center" class="justify-end">
					<farm-select
						class="mt-8"
						item-text="label"
						item-value="value"
						v-model="selectedSort"
						:items="sortOptions"
						@change="changeSort"
					></farm-select>
				</farm-col>
			</farm-row>

			<collapse-transition :duration="300">
				<CfopsFilter v-show="showFilters" key="filters" @applyFilters="applyFilters" />
			</collapse-transition>

			<farm-row justify="end">
				<farm-col cols="12" md="3" align="end"> </farm-col>
			</farm-row>

			<farm-box class="px-2 mb-6">
				<farm-row extra-decrease v-if="cfopsList.length">
					<farm-col cols="12" md="12">
						<AssociationCfopCard
							v-for="item in cfopsList"
							:data="item"
							:key="item.id"
							class="mb-6"
							@update-status="updateCfopStatus"
						/>
					</farm-col>
				</farm-row>

				<farm-row extra-decrease v-else>
					<farm-col>
						<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
					</farm-col>
				</farm-row>
			</farm-box>

			<farm-row extra-decrease v-if="cfopsList.length">
				<farm-box>
					<farm-datatable-paginator
						:page="currentPage"
						:totalPages="totalPages"
						@onChangePage="changePageEvent"
						@onChangeLimitPerPage="changeLimitPerPageEvent"
					/>
				</farm-box>
			</farm-row>
		</farm-box>

		<farm-modal v-model="showConfirmPrompt" size="sm" :offsetTop="48" :offsetBottom="68">
			<template v-slot:header>
				<farm-dialog-header title="Alterações nos CFOPs" @onClose="closeConfirmModal" />
			</template>
			<template v-slot:content>
				<farm-typography size="sm" class="mt-4 mb-4">
					Você alterou a listagem atual dos CFOPs permitidos.
				</farm-typography>
				<farm-typography size="sm" class="mb-4">
					Essa modificação pode impactar em outras jornadas na plataforma.
				</farm-typography>
			</template>

			<template v-slot:footer>
				<farm-dialog-footer
					confirmLabel="Confirmar"
					closeLabel="Cancelar"
					@onConfirm="confirmPatchCfops"
					@onClose="closeConfirmModal"
				/>
			</template>
		</farm-modal>

		<FooterForm
			labelButton="Associar"
			:data="dataFooterForm"
			:isDisabledButton="!!cfopsToUpdateList.length"
			@onCancel="cancel"
			@onSave="openConfirmPrompt"
		/>

		<farm-loader mode="overlay" v-if="isPatchCfopLoading" />
	</farm-container>
</template>

<script lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { listCFOPSort } from '../../configurations/sorts/sorts';
import { format } from '@/helpers/formatUpdateUser';
import store from '@/store';

import AssociationCfopCard from './BodyCfopAssociationCard.vue';
import CfopsFilter from '../TabCFOP/components/CfopFilter.vue';

import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import { useRouter } from '@/composibles';
import type {
	FetchFinancialVehicleCFOPSQuery,
	FinancialVehicleCfop,
	FinancialVehicleCfopsResponse,
	PatchCfopItem,
} from '@/services/cfop';
import {
	useFetchFinancialVehicleCfops,
	usePatchFinancialVehicleCfops,
} from '@/features/_composables';
import { notification, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';
import { headersPages } from '../../configurations/headersPages';

type SortOrder = FetchFinancialVehicleCFOPSQuery['order'];

export default {
	components: {
		HeaderForm,
		FooterForm,
		CfopsFilter,
		AssociationCfopCard,
	},
	props: {
		currentId: {
			type: Number,
			required: true,
		},
	},
	setup({ currentId }) {
		const router = useRouter();
		const { state: fetchCfopsState, data, fethCfops } = useFetchFinancialVehicleCfops();
		const cfopsToUpdateList = ref<PatchCfopItem[]>([]);
		const { state: patchCfopState, patchCfops } = usePatchFinancialVehicleCfops();
		const dataHeaderForm = ref<HeaderFormTypes>(headerFormModel);
		const dataFooterForm = ref<FooterFormDataType>(createObject(modelFooterFormData));
		const selectedSort = ref<SortOrder>('ASC');
		const sortOptions = ref(listCFOPSort);
		const showFilters = ref(false);
		const showRemoveCfopPrompt = ref(false);
		const cfopsData = ref<FinancialVehicleCfopsResponse>(null);
		const cfopsList = ref<FinancialVehicleCfop[]>([]);
		const itemsPerPage = ref(10);
		const currentPage = ref(1);
		const totalPages = ref(1);
		const showConfirmPrompt = ref(false);
		const currentParams = ref<FetchFinancialVehicleCFOPSQuery>({
			orderby: 'code',
			order: selectedSort.value,
		});

		const financialVehicleHeaderData = computed(
			() => store.getters['cadastros/financialVehicleHeaderData']
		);
		const financialVehicleHeaderRequestStatus = computed(
			() => store.getters['cadastros/financialVehicleHeaderRequestStatus']
		);
		const financialVehicleProviderList = computed(
			() => store.getters['cadastros/financialVehicleProviderList']
		);
		const financialVehicleProviderRequestStatus = computed(
			() => store.getters['cadastros/financialVehicleProviderRequestStatus']
		);

		const isPatchCfopLoading = computed(() => patchCfopState.value === 'LOADING');

		const getFinancialVehicleHeaders = (financialVehicleId: number) =>
			store.dispatch('cadastros/getFinancialVehicleHeaders', { financialVehicleId });

		async function loadCfops(queryParams?: FetchFinancialVehicleCFOPSQuery) {
			const params: FetchFinancialVehicleCFOPSQuery = {
				...currentParams.value,
				...{ limit: itemsPerPage.value },
				...queryParams,
			};

			await fethCfops(currentId, params);

			cfopsData.value = data.value;
			totalPages.value = data.value.totalPages;
			cfopsList.value = data.value.content;
		}

		function changeSort(sort: SortOrder) {
			currentParams.value.order = sort;
			loadCfops();
		}

		function filterInputChanged(search: string) {
			loadCfops({ search });
		}

		function changePageEvent(page: number) {
			currentPage.value = page;
			currentParams.value.page = page - 1;
			loadCfops();
		}

		function changeLimitPerPageEvent(limit: number) {
			itemsPerPage.value = limit;
			currentParams.value.limit = limit;

			loadCfops();
		}

		function updateCfopStatus({ cfopCodeId, permitedByVehicle }: PatchCfopItem) {
			const newList = [...cfopsToUpdateList.value];
			const index = newList.findIndex(item => item.cfopCodeId === cfopCodeId);
			const isCfopAlreadyAdded = index !== -1;

			if (isCfopAlreadyAdded) {
				newList[index] = { ...newList[index], permitedByVehicle };
			} else {
				newList.push({ cfopCodeId, permitedByVehicle });
			}

			cfopsToUpdateList.value = newList;
		}

		function cancel() {
			router.push({
				path: `/admin/cadastros/veiculos_financeiros/${currentId}/editar?path=cfop`,
			});
		}

		async function save() {
			if (cfopsToUpdateList.value.length) {
				await patchCfops(currentId, cfopsToUpdateList.value);

				if (patchCfopState.value === 'SUCCESS') {
					closeConfirmModal();

					notification(
						RequestStatusEnum.SUCCESS,
						`CFOP(s) associados ao Veículo Financeiro com sucesso!`
					);

					loadCfops();
					cfopsToUpdateList.value = [];
				}
			}
		}

		function toggleFilters() {
			showFilters.value = !showFilters.value;
		}

		function getFinancialVehicleProviders(payload: {
			id: number;
			filters: Record<string, unknown>;
		}) {
			return store.dispatch('cadastros/getFinancialVehicleProviders', payload);
		}

		function updateHeader() {
			const dataHeader = {
				title: financialVehicleHeaderData.value.content.name,
				messageSucess: 'Documento copiado para área de transferência!',
				listIcons: [
					financialVehicleHeaderData.value.content.id,
					financialVehicleHeaderData.value.content.type,
					[null, financialVehicleHeaderData.value.content.document],
				],
			};

			dataHeaderForm.value = parseDataHeader(dataHeader, headersPages);
		}

		function updatedDateFooter() {
			const updatedFooterFormData = format(financialVehicleProviderList.value.meta);
			dataFooterForm.value = updatedFooterFormData;
		}

		function applyFilters(queryFilters: FetchFinancialVehicleCFOPSQuery) {
			loadCfops(queryFilters);
		}

		function confirmPatchCfops() {
			save();
		}

		function openConfirmPrompt() {
			showConfirmPrompt.value = true;
		}

		function closeConfirmModal() {
			showConfirmPrompt.value = false;
		}

		watch(financialVehicleHeaderData, () => {
			if (financialVehicleHeaderRequestStatus.value === 'SUCCESS') {
				updateHeader();
			}
		});

		watch(financialVehicleProviderRequestStatus, () => {
			if (financialVehicleProviderRequestStatus.value === 'SUCCESS') {
				updatedDateFooter();
			}
		});

		onMounted(async () => {
			loadCfops();
			getFinancialVehicleHeaders(currentId);
			getFinancialVehicleProviders({
				id: currentId,
				filters: {},
			});
		});

		return {
			dataHeaderForm,
			dataFooterForm,
			updateCfopStatus,
			changeSort,
			filterInputChanged,
			toggleFilters,
			changePageEvent,
			changeLimitPerPageEvent,
			showRemoveCfopPrompt,
			currentPage,
			totalPages,
			fetchCfopsState,
			patchCfopState,
			cfopsData,
			cfopsList,
			selectedSort,
			showFilters,
			sortOptions,
			cfopsToUpdateList,
			applyFilters,
			save,
			cancel,
			isPatchCfopLoading,
			openConfirmPrompt,
			showConfirmPrompt,
			closeConfirmModal,
			confirmPatchCfops,
		};
	},
};
</script>

<style lang="scss" scoped>
::v-deep {
	.remove-copy-text .farm-tooltip {
		display: none;
	}
}
</style>
