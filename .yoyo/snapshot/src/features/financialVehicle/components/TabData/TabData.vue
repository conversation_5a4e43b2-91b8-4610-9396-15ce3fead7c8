<template>
	<farm-box>
		<farm-form v-model="valid" v-if="!isError">
			<DataForm :form="form" :isEdit="isEdit" :dataSelect="dataSelect" />
		</farm-form>
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notification, stripTags } from '@farm-investimentos/front-mfe-libs-ts';

import { NEW, EDIT } from '@/constants';
import { mapToDataForm, createPayload } from '@/helpers/financialVehicleForm';
import { format } from '@/helpers/formatUpdateUser';

import DataForm from '../DataForm';

type MessageTypes = 'ERROR' | 'SUCCESS';

export default defineComponent({
	components: {
		DataForm,
	},
	props: {
		isEdit: {
			type: Boolean,
		},
	},
	data() {
		return {
			valid: false,
			form: {
				type: '',
				startDate: '',
				endDate: '',
				name: '',
				document: '',
			},
			dataSelect: [],
			postCreatedFinancialVehicleId: '',
		};
	},
	computed: {
		...mapGetters('cadastros', {
			financialVehicleTypes: 'financialVehicleTypes',
			financialVehicleById: 'financialVehicleById',
			financialVehicleTypesRequestStatus: 'financialVehicleTypesRequestStatus',
			financialVehicleByIdRequestStatus: 'financialVehicleByIdRequestStatus',
			financialVehicleSaveRequestStatus: 'financialVehicleSaveRequestStatus',
			financialVehicleHeaderRequestStatus: 'financialVehicleHeaderRequestStatus',
			financialVehicleHeaderData: 'financialVehicleHeaderData',
			financialVehiclePostCreatedId: 'financialVehiclePostCreatedId',
		}),
		isLoading(): boolean {
			const requestStatuses = [
				this.financialVehicleTypesRequestStatus,
				this.financialVehicleByIdRequestStatus,
				this.financialVehicleSaveRequestStatus,
			];
			return requestStatuses.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatuses = [
				this.financialVehicleTypesRequestStatus.type,
				this.financialVehicleByIdRequestStatus.type,
				this.financialVehicleSaveRequestStatus.type,
			];
			return requestStatuses.includes(RequestStatusEnum.ERROR);
		},
		currentId(): number {
			return this.$route.params.id;
		},
	},
	mounted(): void {
		this.load();
		this.$emit('onSubmit', this.submit);
		this.getFinancialVehicleHeaders({
			financialVehicleId: this.currentId,
		});
	},
	methods: {
		...mapActions('cadastros', {
			getFinancialVehicleTypes: 'getFinancialVehicleTypes',
			getFinancialVehicleById: 'getFinancialVehicleById',
			saveFinancialVehicle: 'saveFinancialVehicle',
			getFinancialVehicleHeaders: 'getFinancialVehicleHeaders',
		}),
		load(): void {
			this.getFinancialVehicleTypes();
		},
		reload(): void {
			this.load();
		},
		submit(): void {
			const payload = createPayload(this.form, this.isEdit, this.currentId);
			this.saveFinancialVehicle({
				payload,
				type: this.isEdit ? EDIT : NEW,
			});
		},
		updatedForm(): void {
			this.form = mapToDataForm(this.financialVehicleById.content);
		},
		updateHeader(): void {
			const dataHeader = {
				title: this.financialVehicleHeaderData.content.name,
				messageSucess: 'Documento copiado para área de transferência!',
				listIcons: [
					this.financialVehicleHeaderData.content.id,
					this.financialVehicleHeaderData.content.type,
					[null, this.financialVehicleHeaderData.content.document],
				],
			};
			this.$emit('onUpdatedHeader', dataHeader);
		},

		updatedDateFooter(): void {
			const updatedFooterFormData = format(this.financialVehicleById.meta);
			this.$emit('onUpdateFooterFormData', updatedFooterFormData);
		},
		createMessage(type: MessageTypes): string {
			const message = {
				ERROR: this.isEdit
					? 'Erro ao cadastrar o Veículo financeiro.'
					: 'Erro ao atualizar o Veículo financeiro.',
				SUCCESS: this.isEdit
					? `Dados do Veículo financeiro atualizado com sucesso. Deseja continuar editando?`
					: `Veículo financeiro cadastrado com sucesso. Deseja continuar para a edição?`,
			};
			return message[type];
		},
		redirectToHome(): void {
			this.$router.push({
				path: `/admin/cadastros/veiculos_financeiros`,
			});
		},
		goToEdit() {
			this.$router.push({
				path: `/admin/cadastros/veiculos_financeiros/${this.postCreatedFinancialVehicleId}/editar?path=dados`,
			});

			this.$router.go(0);
		},
		updatePostCreatedFinancialVehicleId() {
			this.postCreatedFinancialVehicleId = this.financialVehiclePostCreatedId.id;
		},
		createDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.goToEdit();
				})
				.catch(() => {
					this.redirectToHome();
				});
		},
		editDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.load();
				})
				.catch(() => {
					this.redirectToHome();
				});
		},
	},
	watch: {
		valid(newValue: boolean): void {
			this.$emit('onDisabledButtonFooter', newValue);
		},
		financialVehicleTypesRequestStatus(newValue: string) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataSelect = this.financialVehicleTypes;
				if (this.isEdit) {
					this.getFinancialVehicleById({ id: this.currentId });
				}
			}
		},
		financialVehicleByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedForm();
				this.updatedDateFooter();
			}
		},
		financialVehicleSaveRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				if (this.isEdit) {
					this.editDialog(this.createMessage('SUCCESS'));
					return;
				}
				this.updatePostCreatedFinancialVehicleId();
				this.createDialog(this.createMessage('SUCCESS'));
			} else if (newValue === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, this.createMessage('ERROR'));
				this.redirectToHome();
			}
		},
		financialVehicleHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updateHeader();
			}
		},
	},
});
</script>
