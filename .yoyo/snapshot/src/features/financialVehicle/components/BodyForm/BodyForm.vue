<template>
	<farm-container>
		<HeaderForm v-if="isEdit" :data="dataHeaderForm" />
		<TabsForm
			:tabList="tabs"
			:valueDefault="DATA"
			:isEdit="isEdit"
			@onUpdateCurrentTab="onUpdatedCurrentTab"
		/>
		<TabData
			v-if="isData"
			:isEdit="isEdit"
			@onSubmit="onSubmit"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdatedHeader="onUpdatedHeader"
		/>
		<TabProvider
			v-if="isProvider"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdatedHeader="onUpdatedHeader"
		/>
		<TabCFOP
			v-if="isCfop"
			:current-id="currentId"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdatedHeader="onUpdatedHeader"
		/>
		<TabProducts
			v-if="isProduct"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdatedHeader="onUpdatedHeader"
		/>
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="isEdit"
			:hiddenButton="isProvider || isCfop"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import TabsForm, { TabsFormTypes } from '@/components/TabsForm';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';
import { EDIT, DATA, PRODUCTS, CFOP } from '@/constants';

import TabData from '../TabData';
import TabProvider from '../TabProvider';
import TabCFOP from '../TabCFOP';
import TabProducts from '../TabProducts';
import { tabDefault, tabEdit } from '../../configurations/tabs';
import { headersPages } from '../../configurations/headersPages';
import { PROVIDERS } from '../../constants';

export default defineComponent({
	components: {
		TabsForm,
		TabData,
		TabProvider,
		TabCFOP,
		FooterForm,
		HeaderForm,
		TabProducts,
	},
	props: {
		type: {
			type: String,
		},
	},
	data() {
		return {
			disabledButtonFooter: null,
			tabs: tabDefault,
			currentTab: DATA,
			DATA,
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			idSelected: 0,
			labelButton: 'Cadastrar',
		};
	},
	computed: {
		isEdit(): boolean {
			return this.type === EDIT;
		},
		isCfop(): boolean {
			return this.currentTab === CFOP;
		},
		isProvider(): boolean {
			return this.currentTab === PROVIDERS;
		},
		isData(): boolean {
			return this.currentTab === DATA;
		},
		isProduct(): boolean {
			return this.currentTab === PRODUCTS;
		},
		currentId(): number {
			return Number(this.$route.params.id);
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.configurationEdit();
		}
	},
	methods: {
		configurationEdit(): void {
			this.labelButton = 'Salvar';
			this.idSelected = this.$route.params.id;
			this.tabs = tabEdit;
		},
		onCancel(): void {
			this.backToHome();
		},
		onSubmit(call: () => void): void {
			this.dispatchSubmit = call;
		},
		onSave(): void {
			this.dispatchSubmit();
		},
		onUpdateDataUser(data): void {
			this.dataFooterForm = data;
		},
		backToHome(): void {
			this.$router.push({
				path: `/admin/cadastros/veiculos_financeiros`,
			});
		},
		onUpdatedCurrentTab(value: Array<TabsFormTypes>): void {
			this.currentTab = value;
		},
		onUpdateFooterFormData(data: FooterFormDataType): void {
			this.dataFooterForm = data;
		},
		onDisabledButtonFooter(value: boolean): void {
			this.disabledButtonFooter = value;
		},
		onUpdatedHeader(data): void {
			this.dataHeaderForm = parseDataHeader(data, headersPages);
		},
	},
});
</script>
