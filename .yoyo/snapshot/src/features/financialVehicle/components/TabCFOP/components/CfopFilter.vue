<template>
	<farm-box>
		<farm-form v-model="valid">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-status"> Venda Performada </farm-label>
					<farm-select-auto-complete
						id="form-filtro-status"
						v-model="filters.salesPerformed"
						:items="statusList"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
				<farm-col cols="12" md="9" class="pt-0 pt-sm-8">
					<farm-btn title="Aplicar Filtros" outlined @click="apply">
						Aplicar Filtros
					</farm-btn>
					<farm-btn
						plain
						depressed
						class="ml-0 ml-sm-2 button-top"
						title="Limpar Filtros"
						@click="clearFilter"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { RegistersSalesPerformedValueEnum } from '@/constants';

export default defineComponent({
	props: {
		data: {
			type: Array as () => any[],
			default: () => [],
		},
	},
	setup(props, { emit }) {
		const valid = ref(false);
		const filters = ref({ salesPerformed: null });

		const statusList = computed(() =>
			Object.keys(RegistersSalesPerformedValueEnum)
				.filter(el => isNaN(Number(el)))
				.map(key => ({
					id: RegistersSalesPerformedValueEnum[key],
					label: key,
				}))
				.sort((x, y) => x.label.localeCompare(y.label))
		);

		const apply = () => {
			emit('applyFilters', { ...filters.value });
		};

		const clearFilter = () => {
			filters.value = { salesPerformed: null };
			emit('applyFilters', { ...filters.value });
		};

		return {
			valid,
			filters,
			statusList,
			apply,
			clearFilter,
		};
	},
});
</script>
