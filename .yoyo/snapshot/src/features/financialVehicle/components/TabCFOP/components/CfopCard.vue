<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="11">
					<CfopCardTitleAndValueList :data="cardHeaderList" />
				</farm-col>
				<farm-col cols="1" align="end" class="card-action">
					<farm-icon color="error" size="md" class="cursor-pointer" @click="removeCfop">
						{{ 'close-circle-outline' }}
					</farm-icon>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row y-grid-gutters>
				<farm-col cols="12">
					<CardTextBody label="Descrição" :value="data.description" />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import {
	formatDateOrNA,
	formatMandateSignature,
	formatYesOrNo,
	formatValueOrNA,
} from '@/helpers/formatCards';
import CfopCardTitleAndValueList from './CfopCardTitleAndValueList.vue';
import { FinancialVehicleCfop } from '@/services/cfop';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
		CfopCardTitleAndValueList,
	},
	props: {
		data: {
			type: Object as () => FinancialVehicleCfop,
			required: true,
		},
	},
	setup(props, { emit }) {
		const cardHeaderList = ref([
			{
				title: 'Código',
				value: props.data.code,
			},
			{
				title: 'Nome',
				value: props.data.name || 'Venda de mercadoria adquirida ou recebida de terceiros',
			},
			{
				title: 'Origem/Destino',
				value: props.data.sameState ? 'Mesmo Estado' : 'Outro Estado',
			},
			{
				title: 'Amostra/Bonificação',
				value: props.data.sample ? 'Sim' : 'Não',
			},
			{
				title: 'Venda Performada?',
				value: props.data.salesPerformed ? 'Sim' : 'Não',
			},
		]);

		function removeCfop() {
			emit('remove-cfop', {
				id: props.data.id,
				code: props.data.code,
			});
		}

		return {
			cardHeaderList,
			removeCfop,
			formatValueOrNA,
			formatYesOrNo,
			formatDateOrNA,
			formatMandateSignature,
		};
	},
});
</script>
