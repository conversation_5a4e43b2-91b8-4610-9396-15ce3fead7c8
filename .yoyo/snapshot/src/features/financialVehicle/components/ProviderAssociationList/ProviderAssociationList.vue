<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" yGridGutters>
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<ProviderAssociationCard
					v-model="itemSeleted"
					:data="item"
					@updateItem="updateItem"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import ProviderAssociationCard from '../ProviderAssociationCard';

export default defineComponent({
	components: {
		ProviderAssociationCard,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			itemSeleted: [],
		};
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
	methods: {
		updateItem(data) {
			this.$emit('updatedItemSeleted', data);
		},
	},
});
</script>
