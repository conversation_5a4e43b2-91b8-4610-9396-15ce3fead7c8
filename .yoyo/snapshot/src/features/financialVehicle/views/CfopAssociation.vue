<template>
	<BodyCfopAssociation :current-id="currentId" />
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import BodyCfopAssociation from '../components/BodyCfopAssociation/BodyCfopAssociation.vue';
import { useRoute } from '@/composibles';

export default defineComponent({
	components: {
		BodyCfopAssociation,
	},

	setup() {
		const currentId = parseInt(useRoute().params.id, 10);

		return {
			currentId,
		};
	},
});
</script>
