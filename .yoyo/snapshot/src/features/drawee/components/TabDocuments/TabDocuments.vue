<template>
	<farm-box>
		<PageHome
			v-if="!isError"
			domain="sacados"
			:peopleId="currentPeopleId"
			@onUpdateDocumentsDataUser="onUpdateDocumentsDataUser"
		/>
		<FooterForm
			:isValidForm="false"
			:hiddenLeft="false"
			:hiddenButton="true"
			:data="dataUpdated"
			@onCancel="onCancel"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapGetters, mapActions } from 'vuex';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { PF } from '@/constants';
import FooterForm from '@/components/FooterForm';
import PageHome from '@/modules/documents/components/PageHome';

import { checkOriginURL } from '@/helpers/checkOriginURL';

export default defineComponent({
	components: {
		PageHome,
		FooterForm,
	},
	data() {
		return {
			dataUpdated: {},
			showTable: false,
			reload: false,
			currentPeopleId: 0,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			draweeDataHeader:'draweeDataHeader',
			draweeDataHeaderRequestStatus: 'draweeDataHeaderRequestStatus',
		}),
		isLoading(): boolean {
			return this.draweeDataHeaderRequestStatus === RequestStatusEnum.START;
		},
		isError(): boolean {
			return this.draweeDataHeaderRequestStatus.type === RequestStatusEnum.START;
		},
		hasOriginURL(): string {
			return this.$route.query.origin || '';
		},
		currentTypeForm(): string {
			return this.$route.params.typeForm;
		},
		isPF(): boolean {
			return this.$route.params.typeForm === PF.toLowerCase();
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
	},
	mounted(): void {
		this.showTable = false;
		this.getDraweeHeaders({
			document: this.currentDocument
		});
	},
	methods: {
		...mapActions('cadastros', {
			getDraweeHeaders:'getDraweeHeaders',
		}),
		onUpdateDocumentsDataUser(data): void {
			this.dataUpdated = data;
		},
		onCancel(): void {
			let path = checkOriginURL(this.hasOriginURL, `/admin/cadastros/sacados`);
			this.$router.push({
				path,
			});
		},
		updatedHeader(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [
					data.id,
					data.type,
					[data.document, data.raiz],
				],
			});
			this.currentPeopleId = data.peopleId;
		},
	},
	watch: {
		draweeDataHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader(this.draweeDataHeader.content);
			}
		},
	},
});
</script>
