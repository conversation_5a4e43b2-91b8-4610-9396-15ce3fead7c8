<template>
	<farm-box>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Produto"
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="end">
				<farm-btn-confirm
					v-if="canWrite"
					class="v-btn--responsive mt-8"
					customIcon="plus"
					title="Associar Produto"
					:to="`/admin/cadastros/sacados/${currentType}/${currentDocument}/associacao_produto/novo`"
					:icon="true"
				>
					Associar Produto
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<ProductsFilter
				v-show="filter"
				key="filters"
				:listType="dataFilter"
				@onApply="searchListener"
			/>
		</collapse-transition>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="8" md="8"></farm-col>
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<ProductsList
			v-if="!isError"
			:data="dataCard"
			@handleEdit="handleEdit"
			@handleFinalizeAssociation="handleFinalizeAssociation"
			@handleReAssociateOption="handleReAssociateOption"
		/>
		<farm-row extra-decrease v-if="isPagination">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-prompt-user
			v-model="showModal"
			title="Finalizar Associação"
			subtitle=""
			match="REMOVER"
			confirmLabel="Sim"
			@onConfirm="onConfirm"
			@onClose="onClose"
		>
			<template v-slot:subtitle>
				<farm-typography size="md" class="mt-6">
					Deseja realmente finalizar a associação com o produto
				</farm-typography>
				<farm-typography bold size="md"> {{ dataSelected.name }}?</farm-typography>
				<farm-typography size="md" class="mt-3">
					Escreva no campo abaixo
					<farm-typography bold size="md" tag="span">“REMOVER”</farm-typography> para
					confirmar o fim da associação.
				</farm-typography>
			</template>
		</farm-prompt-user>
		<FooterForm :hiddenButton="true" :data="dataFooterForm" @onCancel="onCancel" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	notification,
	pageable,
	RequestStatusEnum,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import { createObject } from '@/helpers/createObject';
import { format } from '@/helpers/formatUpdateUser';
import { PF } from '@/constants';

import ProductsFilter from '../ProductsFilter';
import ProductsList from '../ProductsList';
import { sortAssociationProduct as sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		ProductsFilter,
		ProductsList,
		FooterForm,
	},
	computed: {
		...mapGetters('cadastros', {
			draweeAssociationProductRequestStatus: 'draweeAssociationProductRequestStatus',
			draweeAssociationProductData: 'draweeAssociationProductData',
			addDraweeAssociationProductRequestStatus: 'addDraweeAssociationProductRequestStatus',
			removeDraweeAssociationProductRequestStatus:
				'removeDraweeAssociationProductRequestStatus',
			productTypeData: 'productTypeData',
			productTypeDataRequestStatus: 'productTypeDataRequestStatus',
			draweeDataHeader: 'draweeDataHeader',
			draweeDataHeaderRequestStatus: 'draweeDataHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.productTypeDataRequestStatus,
				this.draweeAssociationProductRequestStatus,
				this.removeDraweeAssociationProductRequestStatus,
				this.addDraweeAssociationProductRequestStatus,
				this.draweeDataHeaderRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			return this.draweeAssociationProductRequestStatus.type === RequestStatusEnum.ERROR;
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
		currentType(): string {
			return this.$route.params.typeForm;
		},
		isPF(): boolean {
			return this.$route.params.typeForm === PF.toLowerCase();
		},
		isPagination(): boolean {
			return !this.isError && this.dataCard.length > 0;
		},
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'startRelationship_DESC',
			sortOptions: sort,
			lastSearchFilters: { page: 0, limit: 10 },
			filter: false,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'startRelationship',
				order: 'DESC',
			},
			filterInputKey: 'search',
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataCard: [],
			dataFilter: [],
			totalPages: 0,
			dataSelected: null,
			showModal: false,
			currentId: 0,
		};
	},
	mounted(): void {
		this.getDraweeHeaders({
			document: this.currentDocument,
		});

		this.getProductTypes();
	},
	methods: {
		...mapActions('cadastros', {
			getDraweeAssociationProducts: 'getDraweeAssociationProducts',
			addDraweeAssociationProducts: 'addDraweeAssociationProducts',
			removeDraweeAssociationProducts: 'removeDraweeAssociationProducts',
			getProductTypes: 'getProductTypes',
			getDraweeHeaders: 'getDraweeHeaders',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getDraweeAssociationProducts({
				filters: { ...this.filters, ...this.hasSort },
				id: this.currentId,
			});
		},
		reload(): void {
			this.doSearch();
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.doSearch();
		},
		updatedList(): void {
			this.dataCard = this.draweeAssociationProductData.content;
		},
		updatedTotalPages(): void {
			this.totalPages = this.draweeAssociationProductData.totalPages;
		},
		updatedHeader(data): void {
			this.currentId = data.id;
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [data.id, data.type, [data.document, data.raiz]],
			});
		},
		updatedFooter(): void {
			const updatedFooterFormData = format(this.draweeAssociationProductData.meta);
			this.dataFooterForm = updatedFooterFormData;
		},
		updatedFilter(): void {
			this.dataFilter = this.productTypeData;
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/sacados`,
			});
		},
		handleEdit(data): void {
			const idProduct = data.id;
			const document = this.currentDocument;
			const type = this.currentType;
			const path = `/admin/cadastros/sacados/${type}/${document}/associacao_produto/${idProduct}/editar`;
			this.$router.push({
				path,
			});
		},
		handleFinalizeAssociation(data): void {
			this.showModal = true;
			this.dataSelected = data;
		},
		handleReAssociateOption(data) {
			this.dataSelected = data;

			this.$dialog
				.confirm(
					{
						body: `Deseja associar novamente o produto <b>${data.name}</b>? `,
						title: 'Associar Novamente',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					const payload = {
						id: this.currentId,
						idProduct: data.id,
					};
					this.addDraweeAssociationProducts(payload);
				})
				.catch(() => {});
		},
		createDialogEdit() {
			this.$dialog
				.confirm(
					{
						body: `Sacado associado com sucesso ao produto <b>${stripTags(
							this.dataSelected.name
						)}</b>!<br/>Deseja editar associação? `,
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					const idProduct = this.dataSelected.id;
					const document = this.currentDocument;
					const type = this.currentType;
					this.$router.push({
						path: `/admin/cadastros/sacados/${type}/${document}/associacao_produto/${idProduct}/editar`,
					});
				})
				.catch(() => {
					this.doSearch();
				});
		},
		onConfirm(): void {
			const payload = {
				id: this.currentId,
				idProduct: this.dataSelected.id,
			};
			this.onClose();
			this.removeDraweeAssociationProducts(payload);
		},
		onClose(): void {
			this.showModal = false;
		},
		reloadPage(): void {
			setTimeout(() => {
				this.doSearch();
			}, 2000);
		},
	},
	watch: {
		draweeDataHeaderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader(this.draweeDataHeader.content);
				this.doSearch();
			}
		},
		draweeAssociationProductRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedList();
				this.updatedTotalPages();
				this.updatedFooter();
			}
		},
		addDraweeAssociationProductRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.createDialogEdit();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					`Não foi possível associar o produto <b>${this.dataSelected.name}</b>.`
				);
			}
		},
		removeDraweeAssociationProductRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(
					RequestStatusEnum.SUCCESS,
					`Associação finalizada com sucesso para o produto <b>${this.dataSelected.name}</b>!`
				);
				this.reloadPage();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					`Não foi possível finalizada a associação deste produto <b>${this.dataSelected.name}</b>.`
				);
			}
		},
		productTypeDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedFilter();
			}
		},
	},
});
</script>
