<template>
	<farm-row>
		<farm-col cols="12">
			<TitlePageForm noPipe label="Importar em" value="Lote" />
		</farm-col>
		<farm-col cols="12" class="d-flex align-center flex-wrap">
			<farm-bodytext class="mb-0" variation="regular" :type="2">
				Não possui o arquivo modelo?
			</farm-bodytext>
			<farm-btn
				class="ml-0 px-0 px-sm-2 ml-sm-4"
				color="primary"
				title="Baixar Planilha Modelo"
				plain
				@click="downloadTemplate"
			>
				<farm-icon class="mr-2">download</farm-icon>
				Baixar Planilha Modelo
			</farm-btn>
		</farm-col>
		<farm-col cols="12" >
			<farm-alertbox
				class="mt-6"
				icon="alert-circle"
				color="info"
				v-if="!filePickerMaxSizeWarning && !filePickerInvalid"
			>
				Envie arquivos no formato: .xls, .xlsx
			</farm-alertbox>

			<farm-alertbox
				class="mt-6"
				icon="alert-circle"
				color="info"
				v-if="filePickerMaxSizeWarning"
			>
				Você excedeu o limite permitido de 10mb. Envie arquivos de até 10mb para prosseguir
				com a solicitação.
			</farm-alertbox>

			<farm-alertbox
				class="mt-6"
				icon="alert-circle"
				color="info"				
				v-if="filePickerInvalid"
			>
				O arquivo não pode ser enviado. Envie arquivos no formato: .xls, .xlsx
			</farm-alertbox>
		</farm-col>
		<farm-col cols="12" class="my-6">
			<farm-multiple-filepicker
				:maxFileSize="10"
				:maxFilesNumber="1"
				acceptedFileTypes="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
				@onMaxFileSizeWarning="showMaxSizeWarning"
				@onFileChange="updateFiles"
				@onInvalidFiles="onInvalidFiles"
			/>
		</farm-col>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import TitlePageForm from '@/components/TitlePageForm';

export default defineComponent({
	components: {
		TitlePageForm,
	},
	data() {
		return {
			file: null,
			filePickerMaxSizeWarning: false,
			filePickerInvalid: false,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			saveImportRequestStatus: 'saveImportRequestStatus',
			downloadModelPlanDraweeRequestStatus: 'downloadModelPlanDraweeRequestStatus',
		}),
		isLoading(): boolean {
			return this.saveImportRequestStatus === RequestStatusEnum.START;
		},
		isError(): boolean {
			return this.saveImportRequestStatus.type === RequestStatusEnum.ERROR;
		},
	},
	mounted(): void {
		this.$emit('onSubmit', this.submit);
	},
	methods: {
		...mapActions('cadastros', {
			saveImport: 'saveImport',
			downloadModelPlanDrawee: 'downloadModelPlanDrawee',
		}),
		updateFiles(newFile: File): void {
			this.file = newFile;
			const hasFile = this.file.length > 0;
			if (hasFile) {
				this.resetted();
			}
			this.$emit('onDisabledButtonFooter', hasFile);
		},
		resetted(): void {
			this.filePickerInvalid = false;
			this.filePickerMaxSizeWarning = false;
		},
		showMaxSizeWarning(): void {
			this.filePickerMaxSizeWarning = true;
		},
		onInvalidFiles() {
			this.filePickerInvalid = true;
		},
		downloadTemplate(): void {
			notification(RequestStatusEnum.SUCCESS, {
				message: `O download do arquivo será iniciado em uma nova aba.`,
				title: 'Download',
			});
			setTimeout(() => {
				this.downloadModelPlanDrawee();
			}, 1000);
		},
		submit(): void {
			const formData = new FormData();
			formData.append('file', this.file[0], this.file[0].name);
			this.saveImport({ formData });
		},
		createDialog() {
			this.$dialog
				.confirm(
					{
						title: 'Importação em Lote',
						body: `Importação em andamento! Deseja acompanhar a importação?`,
					},
					{
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.$emit('changeRouter');
				})
				.catch(() => {
					this.$router.push({
						path: `/admin/cadastros/sacados`,
					});
				});
		},
	},
	watch: {
		saveImportRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.createDialog();
				this.$emit('onDisabledButtonFooter', false);
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, 'Não foi possivel importar a planilha');
				this.resetted();
				this.file = [];
				this.$emit('onDisabledButtonFooter', false);
			}
		},
		downloadModelPlanDraweeRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(
					RequestStatusEnum.SUCCESS,
					'Download da planilha modelo realizado com sucesso!'
				);
			}
		},
	},
});
</script>
