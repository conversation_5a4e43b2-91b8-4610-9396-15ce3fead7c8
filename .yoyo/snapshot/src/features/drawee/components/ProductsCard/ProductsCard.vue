<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="9">
					<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
					<CardListTextHeader :data="listIdAndType" noSpacing class="mb-1" />
				</farm-col>
				<farm-col cols="3" align="end">
					<div class="d-flex justify-end align-center">
						<StatusActiveAndInactive :status="data.status" dense class="mr-2" />
						<farm-context-menu
							:items="contextMenuItems(data)"
							@finalizeAssociation="handleFinalizeAssociation(data)"
							@reAssociate="handleReAssociateOption(data)"
							@edit="handleEdit(data)"
						/>
					</div>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="4">
					<CardTextBody label="Carteira" :value="formatValueOrNA(data.taxonomyWallet)" />
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Classificação"
						:value="formatValueOrNA(data.classification)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody label="Onboarding" :value="formatYesOrNo(data.onboarding)" />
				</farm-col>
			</farm-row>
			<farm-row y-grid-gutters>
				<farm-col cols="4">
					<CardTextBody
						label="Adesão ao Programa"
						:value="formatYesOrNo(data.membership)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Liberação Originador"
						:value="formatYesOrNo(data.releasedSource)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Assinatura Mandato"
						:value="formatMandateSignature(data.mandateSignature)"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col cols="4">
					<CardTextBody
						label="Data de Relacionamento"
						:value="formatDateOrNA(data.relationshipDate)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Início de Associação"
						:value="formatDateOrNA(data.startRelationship)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="Fim de Associação"
						:value="formatDateOrNA(data.endRelationship)"
					/>
				</farm-col>
			</farm-row>
			<farm-row class="mt-3">
				<farm-col cols="5">
					<CardTextBody
						label="Percentual de Concentração"
						:value="formatConcentrationPercentage(data.typeList)"
					/>
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
	edit as editOption,
	reAssociate as reAssociateOption,
	finalizeAssociation as finalizeAssociationOption,
} from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';

import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import {
	formatDateOrNA,
	formatMandateSignature,
	formatYesOrNo,
	formatValueOrNA,
} from '@/helpers/formatCards';
import { parseConcentrationPercentage as formatConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			formatYesOrNo,
			formatDateOrNA,
			formatMandateSignature,
			formatConcentrationPercentage,
			formatValueOrNA,
			listIdAndType: [
				{ label: 'ID', value: this.data.id, copyText: '' },
				{ label: 'Tipo', value: this.data.type, copyText: '' },
			],
		};
	},
	methods: {
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}

			if (this.data.endRelationship === null) {
				return [editOption, finalizeAssociationOption];
			} else {
				return [reAssociateOption];
			}
		},
		handleEdit(item): void {
			this.$emit('handleEdit', item);
		},
		handleFinalizeAssociation(item): void {
			this.$emit('handleFinalizeAssociation', item);
		},
		handleReAssociateOption(item): void {
			this.$emit('handleReAssociateOption', item);
		},
	},
});
</script>
