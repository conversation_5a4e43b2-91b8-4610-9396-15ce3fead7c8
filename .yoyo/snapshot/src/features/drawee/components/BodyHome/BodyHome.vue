<template>
	<farm-container>
		<farm-row v-if="!isError">
			<farm-col cols="6">
				<div class="d-flex align-center justify-center">
					<farm-form-mainfilter
						label=""
						:showFilters="filter"
						@onInputChange="filterInputChangedCustom"
						@onClick="showFilters"
					>
						<div class="d-flex">
							<farm-label class="mb-0 mr-2"> Buscar Sacado </farm-label>
							<v-tooltip top >
								<template v-slot:activator="{ on, attrs }">
									<v-icon small class="mr-2" color="#858585" v-bind="attrs" v-on="on">mdi-help-circle</v-icon>
								</template>

								<div class="mb-2 mt-2 mr-2 ml-2">
									<v-icon small class="mr-2" color="#ffff" v-bind="attrs" v-on="on">mdi-help-circle</v-icon>
									<strong style="font-size:14px" >Buscar Sacado</strong><br>
								</div>
								<div class="mb-2 mt-2 mr-2 ml-2" style="font-size: 12px; font-family: 'Manrope', sans-serif;">
									Realize sua busca pelo ID, Nome,<br>
									Razão Social, CPF, CNPJ ou Raiz do sacado.
								</div>
							</v-tooltip>
							<LabelRequestResults
								v-if="showResultsLabel"
								:totalItems="draweeList.totalItems"
							/>
						</div>
					</farm-form-mainfilter>
				</div>
			</farm-col>
			<farm-col class="d-flex align-center justify-end" md="6">
				<farm-btn-confirm
					v-if="canWrite"
					title="Adicionar Sacado"
					customIcon="plus"
					:icon="true"
					@click="handleNewDrawee"
				>
					Adicionar Sacado
				</farm-btn-confirm>
				<ButtonImport
					class="mx-2"
					color="primary"
					icon
					outlined
					:options="options"
					@onClick="handleClick"
				/>
				<farm-btn title="Exportar" outlined disabled v-if="alertData.toggleButtonExport">
					Exportar
					<farm-icon size="md">chevron-down</farm-icon>
				</farm-btn>

				<farm-btn-export
					v-if="!alertData.toggleButtonExport"
					color="primary"
					:disabled="alertData.toggleButtonExport"
					:optionsList="optionsList"
					@onClick="handleOpenExport"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300" v-if="!isError">
			<Filters @onApply="searchListener" @onValidate="handleValidateDate" v-show="filter" />
		</collapse-transition>
		<farm-row class="mb-6" v-if="alertData.showAlert">
			<farm-col cols="12">
				<farm-alertbox :icon="alertData.icon" :color="alertData.color" dismissable>
					<span v-html="alertData.text"></span> </farm-alertbox
			></farm-col>
		</farm-row>
		<DraweeTable
			v-if="!isError"
			:data="dataTable"
			:filterCurrent="filters"
			:paginationTotalPages="paginationTotalPages"
			:paginationPageActive="currentPage"
			@onRequest="onRequest"
		/>
		<ModalDocument
			v-model="modalDocument.show"
			title="Novo Sacado"
			label="Digite o CNPJ/CPF"
			:loading="modalDocument.loading"
			:hasPF="true"
			@onContinues="onConfirmModal"
		/>
		<ModalExport
			v-if="showExportModal"
			v-model="showExportModal"
			:createdAtRange="createdAtRange"
			:updatedAtRange="updatedAtRange"
			:exportType="exportType"
			:filtersExport="filters"
			title="Selecione o produto"
			label="Selecione um ou mais Produtos relacionados aos Sacados filtrados para exportação."
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { mapActions, mapGetters } from 'vuex';
import {
	pageable,
	RequestStatusEnum,
	notification,
	isValidCNPJ,
	isValidCPF,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import ModalDocument from '@/components/ModalDocument';
import ModalExport from '@/components/ModalExport';
import ButtonImport from '@/components/ButtonImport';
import LabelRequestResults from '@/components/LabelRequestResults';
import { PF, PJ } from '@/constants';
import { cleanDocument } from '@/helpers/masks';
import storage from '@/helpers/storage';

import DraweeTable from '../DraweeTable';
import Filters from '../Filters';
import { TYPE_DRAWEE } from '../../constants';
import { message } from '../../configurations/dialogMessages';
import { alertDefault, alertError } from '../../configurations/alertBoxMessages/alertBoxMessages';

export default defineComponent({
	components: {
		Filters,
		ModalDocument,
		DraweeTable,
		ButtonImport,
		ModalExport,
		LabelRequestResults,
	},
	mixins: [pageable],
	data() {
		return {
			showExportModal: false,
			dataTable: [],
			lastSearchFilters: {},
			filter: false,
			filterInputKey: 'search',
			filters: {
				page: 0,
				limit: 10,
			},
			idClicked: 0,
			modalDocument: {
				show: false,
				loading: false,
				value: '',
			},
			paginationTotalPages: 0,
			typeSelected: '',
			options: [
				{
					link: '/importacao_em_lote?path=importar',
					title: 'Importação em Lote',
					icon: 'upload',
				},
				{
					link: '/importacao_em_lote?path=historico',
					title: 'Histórico de Importações',
					icon: 'update',
				},
			],
			alertData: alertDefault,
			optionsList: [
				{ key: 'XLSX', label: 'XLSX' },
				{ key: 'CSV', label: 'CSV' },
			],
			modalData: {},
			exportType: null,
			showResultsLabel: false,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			draweeList: 'draweeList',
			draweeRequestStatus: 'draweeRequestStatus',
			documentDataCheckDrawee: 'documentDataCheckDrawee',
			documentDataCheckDraweeRequestStatus: 'documentDataCheckDraweeRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.draweeRequestStatus,
				this.documentDataCheckDraweeRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.draweeRequestStatus,
				this.documentDataCheckDraweeRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getDrawee: 'getDrawee',
			getDocumentDataCheckDrawee: 'getDocumentDataCheckDrawee',
		}),
		doSearch(): void {
			this.lastSearchFilters = {
				...this.filters,
			};

			this.getDrawee({
				filters: {
				...this.filters,
				...this.hasSort
				}
			});

			this.shouldShowResultsLabel();
		},
		searchListener(filterValues): void {
			this.filters = {
				...this.filters,
				...filterValues,
				page: 0,
			};
			this.doSearch();
		},
		reload(): void {
			this.doSearch();
		},
		handleValidateDate({ values, createdAtRangeObject, updatedAtRangeObject }): void {
			if (values.includes(false)) {
				this.alertData = { ...alertError, toggleButtonExport: true };
				return;
			}
			if (values.every(date => date === undefined)) {
				this.alertData = { ...alertDefault, toggleButtonExport: true };
				return;
			}

			this.alertData = { toggleButtonExport: false };
			this.alertData = { showAlert: false };

			this.createdAtRange = createdAtRangeObject;
			this.updatedAtRange = updatedAtRangeObject;
		},
		onRequest(filtersActive): void {
		this.lastSearchFilters = {
			...filtersActive,
		};

		this.filters = {
			...filtersActive,
		};

		const payload = {
			filters: this.filters
		};

		this.getDrawee(payload);
		},
		shouldShowResultsLabel() {
			const keys = Object.keys(this.filters);
			const matchFilter = [
				'createdAtRangeFim',
				'createdAtRangeInicio',
				'search',
				'status',
				'type',
				'updatedAtRangeFim',
				'updatedAtRangeInicio',
			].some(filter => keys.includes(filter));

			if (matchFilter) {
				this.showResultsLabel = !this.showResultsLabel;
			}
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		handleNewDrawee(): void {
			this.modalDocument.show = true;
		},
		handleOpenExport(data): void {
			this.exportType = data;
			this.showExportModal = true;
		},
		onConfirmModal(document): void {
			const payload = {
				document: document,
			};
			this.modalDocument.value = document;
			this.modalDocument.loading = true;
			this.getDocumentDataCheckDrawee(payload);
		},
		onConfirmModalRedirect(): void {
			const typeForm = this.typeSelected.toLowerCase();
			const document = this.modalDocument.value;
			this.redirect(`/${typeForm}/${document}/editar?path=dados`);
		},
		checkTypeDrawee(type: string): number {
			return type === TYPE_DRAWEE ? 0 : 1;
		},
		isCompany(document: string): string {
			const result = isValidCNPJ(document) ? PJ : PF;
			return result.toLowerCase();
		},
		redirect(url: string): void {
			this.$router.push({
				path: `/admin/cadastros/sacados/${url}`,
			});
		},
		filterInputChangedCustom(value: string) {
			if (value.length >= 3 || value === '') {
				this.filters = {
					...this.filters,
					[this.filterInputKey]: value,
					page: 0,
				};
				const dataValue = cleanDocument(value);
				if (dataValue.length <= 11) {
					if (isValidCPF(dataValue)) {
						this.filters = {
							...this.filters,
							[this.filterInputKey]: dataValue,
						};
					}
				}
				if (dataValue.length > 12) {
					if (isValidCNPJ(dataValue)) {
						this.filters = {
							...this.filters,
							[this.filterInputKey]: dataValue,
						};
					}
				}
				this.getDrawee({
					filters: { ...this.filters, ...this.hasSort },
				});
			}
		},
		handleClick(url: string): void {
			this.redirect(url);
		},
		updatedTable(): void {
			this.dataTable = this.draweeList.content;
			this.paginationTotalPages = this.draweeList.totalPages;
		},
		createDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Novo Sacado',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.onConfirmModalRedirect();
				})
				.catch(() => {});
		},
	},
	watch: {
		draweeRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedTable();
			}
		},
		documentDataCheckDraweeRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				if (parseInt(this.documentDataCheckDrawee.id, 10) === 0) {
					storage.clear();
					if (this.documentDataCheckDrawee.hasRegisteredRaiz === true) {
						storage.set('branch', 'true');
					}
					const typeFormNew = this.isCompany(this.modalDocument.value);
					this.redirect(`/${typeFormNew}/${this.modalDocument.value}/novo`);
					return;
				}
				this.idClicked = this.documentDataCheckDrawee.id;
				this.modalDocument.show = false;
				this.modalDocument.loading = false;
				const isPaper = this.documentDataCheckDrawee.documentsType.filter(item => {
					return item === 'SACADO';
				});
				this.typeSelected = this.documentDataCheckDrawee.type;
				const index = isPaper.length === 1 ? 0 : 1;

				this.createDialog(
					index === 0 ? message.hasPaperRegister : message.otherPaperRegister
				);
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				this.modalDocument.show = false;
				this.modalDocument.loading = false;
				notification(RequestStatusEnum.ERROR, 'Erro ao consultar ');
			}
		},
	},
});
</script>
