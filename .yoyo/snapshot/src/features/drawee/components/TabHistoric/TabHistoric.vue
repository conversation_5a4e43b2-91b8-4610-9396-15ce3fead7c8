<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12">
				<TitlePageForm noPipe label="Histórico de" value="Importações" />
			</farm-col>
		</farm-row>
		<farm-row class="mb-4 d-flex space-between align-end" v-if="!isError">
			<farm-col cols="12" sm="12" md="5" class="d-flex space-between">
				<div>
					<farm-label for="form-filtro-status"> Selecionar Status </farm-label>
					<farm-select-auto-complete
						id="form-filtro-status"
						v-model="selectFilter.status"
						:items="statusList"
						item-text="status"
						item-value="id"
					/>
				</div>
				<div>
					<farm-btn-confirm class="mt-8 ml-4" title="Buscar" @click="apply">
						Buscar
					</farm-btn-confirm>
				</div>
			</farm-col>
			<farm-col cols="12" sm="12" md="3"></farm-col>
			<farm-col cols="12" sm="12" md="4" align="end">
				<farm-select-auto-complete
					id="form-filtro-status"
					v-model="selectFilter.order"
					:items="orderList"
					item-text="label"
					item-value="value"
					@change="changeOrderBy"
				/>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isError && dataImportHistoric.length > 0" y-grid-gutters>
			<farm-col
				v-for="item in dataImportHistoric"
				cols="12"
				md="6"
				:key="`data_import_${item.id}`"
			>
				<HistoricCard :data="item" @handleCardItem="handleCardItem" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isListEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>
		<farm-row extra-decrease v-if="isPagination">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<ModalFeedbackImportHistoric
			v-if="isModalFeedbackImport"
			v-model="isModalFeedbackImport"
			:data="dataImportHistoricSelected"
			@onClose="onCloseModal"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { pageable, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import TitlePageForm from '@/components/TitlePageForm';
import ModalFeedbackImportHistoric from '@/components/ModalFeedbackImportHistoric';

import HistoricCard from '../HistoricCard';

import { sortHistory as sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		HistoricCard,
		TitlePageForm,
		ModalFeedbackImportHistoric,
	},
	mixins: [pageable],
	data() {
		return {
			dataHistory: {
				status: 'ATIVO',
			},
			lastSearchFilters: {},
			filters: {
				page: 0,
				limit: 10,
				status: 0,
			},
			selectFilter: {
				status: 0,
				order: 'createdAt_DESC',
			},
			hasSort: {
				orderby: 'createdAt',
				order: 'DESC',
			},
			totalPages: 1,
			statusList: [],
			dataImportHistoric: [],
			dataImportHistoricSelected: null,
			orderList: sort,
			isModalFeedbackImport: false,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			importHistoricDataRequestStatus: 'importHistoricDataRequestStatus',
			importHistoricData: 'importHistoricData',
			importHistoricStatusDataRequestStatus: 'importHistoricStatusDataRequestStatus',
			importHistoricStatusData: 'importHistoricStatusData',
			importHistoricDataByIdRequestStatus: 'importHistoricDataByIdRequestStatus',
			importHistoricDataById: 'importHistoricDataById',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.importHistoricStatusDataRequestStatus,
				this.importHistoricDataRequestStatus,
				this.importHistoricDataByIdRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.importHistoricStatusDataRequestStatus.type,
				this.importHistoricDataRequestStatus.type,
				this.importHistoricDataByIdRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		isPagination(): boolean {
			return !this.isError && this.dataImportHistoric.length > 0;
		},
		isListEmpty(): boolean {
			return !this.isError && this.dataImportHistoric.length === 0;
		},
	},
	mounted(): void {
		this.getImportHistoricStatus();
	},
	methods: {
		...mapActions('cadastros', {
			getImportHistoricStatus: 'getImportHistoricStatus',
			getImportHistoric: 'getImportHistoric',
			getImportHistoricById: 'getImportHistoricById',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			this.getImportHistoric({
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		reload(): void {
			this.doSearch();
		},
		apply(): void {
			this.filters = {
				...this.filters,
				status: this.selectFilter.status,
			};
			this.doSearch();
		},
		changeOrderBy(): void {
			const [orderby, order] = this.selectFilter.order.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.doSearch();
		},
		handleCardItem(data): void {
			this.getImportHistoricById({ id: data.id });
		},
		onCloseModal(): void {
			this.isModalFeedbackImport = false;
		},
		updatedHistoricStatus(): void {
			this.statusList = [
				{ id: 0, status: 'Todos' },
				...this.importHistoricStatusData.content,
			];
		},
		updatedHistoricData(): void {
			this.dataImportHistoric = [...this.importHistoricData.content];
			this.totalPages = this.importHistoricData.totalPages;
		},
	},
	watch: {
		importHistoricStatusDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHistoricStatus();
				this.doSearch();
			}
		},
		importHistoricDataRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHistoricData();
			}
		},
		importHistoricDataByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataImportHistoricSelected = {
					...this.importHistoricDataById.content,
				};
				this.isModalFeedbackImport = true;
			}
		},
	},
});
</script>
