<template>
	<farm-form v-model="disabledButtonFooter">
		<farm-row v-if="!isError">
			<farm-col cols="12">
				<farm-collapsible open title="Informações Gerais" icon="clipboard-text">
					<GeneralInfoFormPF v-model="form.generalInfo" />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12" v-if="!isLoading" class="mt-4">
				<farm-collapsible title="Renda" icon="currency-usd">
					<BillingForm v-model="form.income" title="Renda Mensal Estimada" />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12">
				<farm-collapsible title="Endereço" icon="map-marker" class="mt-4">
					<AddressForm v-model="form.address" />
				</farm-collapsible>
			</farm-col>
			<farm-col cols="12">
				<farm-collapsible title="Contato Principal" icon="contacts" class="my-4">
					<ContactForm v-model="form.contact" :isFromFormPJ="false" />
				</farm-collapsible>
			</farm-col>
		</farm-row>
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="isEdit"
			:hiddenButton="!DATA"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-form>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum, notification, stripTags } from '@farm-investimentos/front-mfe-libs-ts';

import FooterForm from '@/components/FooterForm';
import GeneralInfoFormPF, {
	GeneralInfoTypes,
	generalInfoModel,
} from '@/components/GeneralInfoFormPF';
import AddressForm, { addressModel, AddressTypes } from '@/components/AddressForm';
import ContactForm, { ContactTypes, contactModel } from '@/components/ContactForm';

import BillingForm, { receivedValueModel, receivedValueTypes } from '@/components/BillingForm';

import { createObject } from '@/helpers/createObject';
import { format } from '@/helpers/formatUpdateUser';
import { checkOriginURL } from '@/helpers/checkOriginURL';
import { addMaskInput } from '@/helpers/addMaskInput';

import { PF, EDIT, DATA } from '@/constants';
import storage from '@/helpers/storage';
import updatePFDraweeFormVariables from '@/helpers/builders/updatePFDraweeFormVariables';
import createPFDraweeFormPayload from '@/helpers/builders/createPFDraweeFormPayload';

export default defineComponent({
	components: {
		FooterForm,
		GeneralInfoFormPF,
		AddressForm,
		ContactForm,
		BillingForm,
	},
	props: {
		type: {
			type: String,
			required: true,
		},
	},
	data() {
		return {
			disabledButtonFooter: false,
			dataFooterForm: {},
			form: {
				generalInfo: createObject<GeneralInfoTypes>(generalInfoModel),
				income: createObject<receivedValueTypes>(receivedValueModel),
				address: createObject<AddressTypes>(addressModel),
				contact: createObject<ContactTypes>(contactModel),
			},
			formType: PF,
			labelButton: 'Cadastrar',
			DATA,
		};
	},
	mounted(): void {
		this.getDraweeHeaders({
			document: this.currentDocument,
		});
		if (this.isEdit) {
			this.labelButton = 'Salvar';
			this.getDraweeById({
				type: PF,
				document: this.currentDocument,
			});
			return;
		}
		const document = addMaskInput({
			value: this.currentDocument,
			mask: '###.###.###-##',
			quantMin: 11,
		});
		this.form.generalInfo.document = document;
		if (storage.get('branch')) {
			this.load();
		}
	},
	computed: {
		...mapGetters('cadastros', {
			draweeById: 'draweeById',
			draweeByIdRequestStatus: 'draweeByIdRequestStatus',
			saveDraweeRequestStatus: 'saveDraweeRequestStatus',
			draweeDataHeader: 'draweeDataHeader',
			draweeDataHeaderRequestStatus: 'draweeDataHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.draweeByIdRequestStatus,
				this.saveDraweeRequestStatus,
				this.draweeDataHeaderRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [this.draweeByIdRequestStatus.type];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		isEdit(): boolean {
			return this.type === EDIT;
		},
		currentDocument(): number {
			return this.$route.params.document;
		},
		hasOriginURL(): string {
			return this.$route.query.origin || '';
		},
		currentTypeForm(): string {
			return this.$route.params.typeForm;
		},
	},
	methods: {
		...mapActions('cadastros', {
			saveDrawee: 'saveDrawee',
			getDraweeById: 'getDraweeById',
			getDraweeHeaders: 'getDraweeHeaders',
		}),

		load(): void {
			this.getDraweeById({
				type: PF,
				document: this.currentDocument,
			});
		},
		reload(): void {
			this.load();
		},
		onCancel(): void {
			let path = checkOriginURL(this.hasOriginURL, `/admin/cadastros/sacados`);
			this.$router.push({
				path,
			});
		},
		onSave(): void {
			const payload = createPFDraweeFormPayload(this.form);
			if (this.type === 'new') delete payload.id;
			this.saveDrawee({ payload, type: this.type });
		},
		onUpdateDataUser(data): void {
			const updatedData = format(data);
			this.dataFooterForm = updatedData;
		},
		updatedHeaderHome(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [data.id, 'Física', [data.document, data.document]],
			});
		},
		goToEdit() {
			this.$router.push({
				path: `/admin/cadastros/sacados/pf/${this.currentDocument}/editar?path=dados`,
			});

			this.$router.go(0);
		},
		createDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.goToEdit();
				})
				.catch(() => {
					this.onCancel();
				});
		},
		editDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.reload();
				})
				.catch(() => {
					this.onCancel();
				});
		},
		createMessage(type: 'ERROR' | 'SUCCESS'): string {
			const message = {
				ERROR: this.isEdit ? 'Erro ao cadastrar o sacado.' : 'Erro ao atualizar o sacado.',
				SUCCESS: this.isEdit
					? `Dados do Sacado atualizado com sucesso. Deseja continuar editando?`
					: `Sacado cadastrado com sucesso. Deseja continuar para a edição?`,
			};

			return message[type];
		},
	},
	watch: {
		saveDraweeRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				if (this.isEdit) {
					this.editDialog(this.createMessage('SUCCESS'));
					return;
				}
				this.createDialog(this.createMessage('SUCCESS'));
			} else if (newValue === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, this.createMessage('ERROR'));
			}
		},
		draweeByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.form = {
					...updatePFDraweeFormVariables(this.draweeById.content),
				};
				this.onUpdateDataUser(this.draweeById.meta);
			}
		},
		draweeDataHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeaderHome(this.draweeDataHeader.content);
			}
		},
	},
});
</script>
