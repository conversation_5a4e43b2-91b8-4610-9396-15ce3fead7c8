<template>
	<farm-box>
		<farm-form class="mb-6">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-status"> Status </farm-label>
					<farm-select-auto-complete
						id="form-filtro-status"
						v-model="filters.status"
						:items="statusList"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-typePerson"> Tipo de Pessoa </farm-label>
					<farm-select-auto-complete
						id="form-filtro-typePerson"
						v-model="filters.type"
						:items="typePerson"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-createdAtRange">
						Data de Cadastro (Início/fim)</farm-label
					>
					<farm-input-rangedatepicker
						ref="datepickerCreateDate"
						inputId="form-filtro-createdAtRange"
						v-model="createdAtRange"
						:max="getEndDate"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-updatedAtRange">
						Data de Atualização (Início/fim)</farm-label
					>
					<farm-input-rangedatepicker
						ref="datepickerUpdateDate"
						inputId="form-filtro-updatedAtRange"
						v-model="updatedAtRange"
						:max="getEndDate"
					/>
				</farm-col>
			</farm-row>
			<farm-row class="mt-0" v-if="isFeatureEnabled('drawee.missingContactsFilter')">
				<farm-col cols="12" md="3">
					<farm-label class="mb-2">Exibir apenas Sacados sem Celular e/ou E-mail?</farm-label>
					<div class="d-flex align-center">
						<farm-switcher
							id="onlyMissingEmailOrPhone"
							v-model="filters.onlyMissingEmailOrPhone" />
						<span class="ml-3">{{ filters.onlyMissingEmailOrPhone ? 'Sim' : 'Não' }}</span>
					</div>
				</farm-col>
			</farm-row>
			<farm-row class="mt-6 mb-8">
				<farm-col>
					<farm-btn-confirm
						outlined
						class="farm-btn--responsive"
						title="Aplicar Filtros"
						@click="apply"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="farm-btn--responsive ml-0 ml-sm-4 mt-2 mt-sm-0"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

import { RegistersStatusValueEnum } from '@/constants';
import { formatDateRangeSetKeys } from '@farm-investimentos/front-mfe-libs-ts';

import { typePerson } from '@/constants';
import validateRangeOfDates from '@/helpers/validators/validateRangeOfDates';

export default defineComponent({
	inject: ['isFeatureEnabled'],
	data() {
		return {
			filters: {
				status: null,
				type: null,
				onlyMissingEmailOrPhone: false,
			},
			createdAtRange: [],
			updatedAtRange: [],
			typePerson,
		};
	},
	mounted() {
		if (this.isFeatureEnabled('drawee.missingContactsFilter')) {
			this.filters.onlyMissingEmailOrPhone = false;
		}
	},
	computed: {
		statusList() {
			return Object.keys(RegistersStatusValueEnum)
				.filter(el => isNaN(Number(el)))
				.map(key => ({
					id: RegistersStatusValueEnum[key],
					label: key,
				}))
				.sort((x, y) => x.label.localeCompare(y.label));
		},
		getEndDate() {
			const endDate = new Date();
			return endDate.toISOString();
		},
	},
	methods: {
		apply(): void {
			let filters = {
				...this.filters,
			};

			this.onInvalidDate();
			this.$emit('onApply', { ...filters });
		},
		onFilterClear(): void {
			this.$refs.datepickerCreateDate.clear();
			this.$refs.datepickerUpdateDate.clear();
			this.filters = {
				status: null,
				type: null,
				onlyMissingEmailOrPhone: false,
			};
			this.createdAtRange = [];
			this.updatedAtRange = [];

			this.onInvalidDate();
			this.$emit('onApply', { ...this.filters });
		},
		onInvalidDate() {
			let isCreatedAtRangeValid;
			let isUpdatedAtRangeValid;
			let createdAtRangeObject = {
				biggest: null,
				lowest: null,
			};
			let updatedAtRangeObject = {
				biggest: null,
				lowest: null,
			};

			if (this.createdAtRange.length > 0) {
				const createdArray = Array.from(this.createdAtRange);
				isCreatedAtRangeValid = createdArray.reduce((date, cur) =>
					validateRangeOfDates(createdAtRangeObject, { date, cur })
				);
			}

			if (this.updatedAtRange.length > 0) {
				const updatedArray = Array.from(this.updatedAtRange);
				isUpdatedAtRangeValid = updatedArray.reduce((date, cur) =>
					validateRangeOfDates(updatedAtRangeObject, { date, cur })
				);
			}

			this.$emit('onValidate', {
				values: [isCreatedAtRangeValid, isUpdatedAtRangeValid],
				createdAtRangeObject,
				updatedAtRangeObject,
			});
		},
	},
	watch: {
		updatedAtRange(newValue) {
			if (!newValue) return;
			formatDateRangeSetKeys(newValue, 'updatedAtRange', this, ['Inicio', 'Fim']);
		},
		createdAtRange(newValue) {
			if (!newValue) return;
			formatDateRangeSetKeys(newValue, 'createdAtRange', this, ['Inicio', 'Fim']);
		},
		'filters.onlyMissingEmailOrPhone'(newVal) {
			if (this.isFeatureEnabled('drawee.missingContactsFilter')) {
				localStorage.setItem('onlyMissingEmailOrPhone', JSON.stringify(newVal));
			}
		}
	},
});
</script>
