<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				hide-default-footer
				:class="{
					'elevation-0 mt-0 table-cadastros-assignors-list': true,
					'mb-8': data.length === 0,
				}"
				item-key="key"
				:items="data"
				:headers="headers"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader()"
				:options.sync="options"
				:header-props="headerProps"
			>
				<template v-slot:[`item.missingEmailOrPhone`]="{ item }">
					<v-tooltip
						top
						v-if="
							item.missingEmailOrPhone !== '0' &&
							isFeatureEnabled('drawee.missingContactsFilter')
						"
					>
						<template v-slot:activator="{ on, attrs }">
							<div
								class="d-inline-flex rounded-circle pa-1"
								style="background-color: rgba(255, 0, 0, 0.1)"
								v-bind="attrs"
								v-on="on"
							>
								<img
									alt="imagem referente a erro na simulação ou cessão"
									class="page-error-img"
									src="@/assets/icons/phone-disabled.svg"
									:style="{ width: '16px' }"
								/>
							</div>
						</template>
						<span class="custom-tooltip">
							<div class="mb-2 mt-2 mr-2 ml-2">
								<v-icon small color="#FFF" class="mr-1">mdi-alert</v-icon>
								<strong style="font-size: 14px"
									>Sacado sem Celular e/ou E-mail</strong
								>
							</div>
							<div
								class="mb-2 mt-2 mr-2 ml-2"
								style="font-size: 12px; font-family: 'Manrope', sans-serif"
							>
								Este sacado não possui Celular e/ou E-mail cadastrado<br />
								em nossa base.
							</div>
						</span>
					</v-tooltip>
				</template>
				<template slot="no-data">
					<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" />
				</template>

				<template v-slot:header="{ props: { headers } }" v-if="showCustomHeader()">
					<farm-datatable-header
						firstSelected
						:headers="headers"
						:sortClick="sortClicked"
						:selectedIndex="6"
						@onClickSort="onSort"
					/>
				</template>

				<template v-slot:[`item.status`]="{ item }">
					<StatusActiveAndInactive :status="item.status" />
				</template>

				<template v-slot:[`item.createdAt`]="{ item }">
					{{ defaultDateFormat(item.createdAt) }}
				</template>
				<template v-slot:[`item.updatedAt`]="{ item }">
					{{ defaultDateFormat(item.updatedAt) }}
				</template>

				<template v-slot:[`item.infos`]="{ item }">
					<farm-context-menu :items="contextMenuItems(item)" @edit="editItem(item)" />
				</template>

				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="data.length > 0"
						class="mt-6 mb-n6"
						:page="paginationPageActive"
						:totalPages="paginationTotalPages"
						@onChangePage="onChangePageTable"
						@onChangeLimitPerPage="onChangeLimitPerPageTable"
					/>
				</template>
			</v-data-table>
		</farm-box>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { edit as editOption, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import { PF, PJ } from '@/constants';

import { headers } from '../../configurations/headers';

export default defineComponent({
	components: {
		StatusActiveAndInactive,
	},
	inject: ['isFeatureEnabled'],
	props: {
		data: {
			type: Array,
			require: true,
		},
		paginationTotalPages: {
			type: Number,
			require: true,
		},
		paginationPageActive: {
			type: Number,
			default: 1,
		},
		filterCurrent: {
			type: Object,
			require: true,
		},
	},
	data() {
		return {
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
			sortClicked: [],
			headerProps: {
				sortByText: 'Ordenar por',
			},
			options: {},
		};
	},
	computed: {
		breakpoint(): string {
			return this.$vuetify.breakpoint.name;
		},
		headers() {
			// Remove a coluna de alerta se a feature não estiver habilitada
			if (!this.isFeatureEnabled('drawee.missingContactsFilter')) {
				return headers.filter(header => header.value !== 'missingEmailOrPhone');
			}
			return headers;
		},
	},
	methods: {
		onSort(data): void {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			const filtersActive = {
				...this.filterCurrent,
				search: this.filterCurrent.search || '',
				orderby: data.field,
				order: data.descending,
			};
			this.$emit('onRequest', filtersActive);
		},
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			return [editOption];
		},
		redirect(url: string): void {
			this.$router.push({
				path: `/admin/cadastros/sacados/${url}`,
			});
		},
		isTypeForm(type: string): string {
			const result = type === PJ ? PJ : PF;
			return result.toLowerCase();
		},
		editItem(item): void {
			const typeFormNew = this.isTypeForm(item.type);
			this.redirect(`/${typeFormNew}/${item.document}/editar?path=dados`);
		},
		defaultDateFormat,
		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		onChangePageTable(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			this.filterCurrent.page = pageActive;
			this.$emit('onRequest', { ...this.filterCurrent, page: pageActive });
		},
		onChangeLimitPerPageTable(limit: number): void {
			this.filterCurrent.limit = limit;
			this.$emit('onRequest', { ...this.filterCurrent, page: 0, limit: limit });
		},
	},
});
</script>
<style scoped>
.v-tooltip__content {
	background: rgba(97, 97, 97, 1);
}
</style>
<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-cadastros-assignors-list', 1, (0));
</style>
