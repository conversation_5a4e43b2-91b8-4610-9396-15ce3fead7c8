<template>
    <farm-box>
        <PageForm 
            v-if="!isError" 
            domain="sacados" 
            :type="type" 
            :header="headerConfig"
            :peopleId="peopleId"
        />
        <farm-loader mode="overlay" v-if="isLoading" />
        <div v-if="isError" class="my-10 d-flex justify-center">
            <farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
        </div>
    </farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import PageForm from '@/modules/documents/components/PageForm';
import { PF } from '@/constants';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';

import { headersPages } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		PageForm,
	},
	props: {
		type: {
			type: String,
		},
	},
    data() {
		return {
            headerConfig: {},
            peopleId: 0
        };
    },
    computed: {
		...mapGetters('cadastros', {
			draweeById: 'draweeById',
			draweeByIdRequestStatus: 'draweeByIdRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.draweeByIdRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.draweeByIdRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
		currentTypeForm(): string {
			return this.$route.params.typeForm;
		},
    },
    methods: {
        ...mapActions('cadastros', {
			getDraweeById:'getDraweeById',
		}),
        doSearch(): void {
			this.getDraweeById({
				type: this.currentTypeForm.toUpperCase(),
				document: this.currentDocument 
			});
		},
        reload(): void {
            this.doSearch();
        },
        updatedHeader(data): void {
			this.peopleId = data.peopleId;
			const isPeopleTypePF = this.currentTypeForm.toUpperCase() === PF;
			const name = isPeopleTypePF ? data.fullName : data.acronym; 
			const raiz = isPeopleTypePF ? data.document : data.raiz;   
			const header = {
				title: name,
				listIcons: [
					data.peopleId,
					isPeopleTypePF ? 'Física' : 'Jurídica',
					[data.document, raiz]],
			};
			this.headerConfig = parseDataHeader(header, headersPages);
		},
    },
    mounted(): void {
        this.doSearch();
	},
    watch: {
        draweeByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				const { content } = this.draweeById;
				const key = this.currentTypeForm.toUpperCase() === PF ? 'generalInfoPF':'generalInfoPJ';
				this.updatedHeader(content[key]);
			}
		},
    }
});
</script>
