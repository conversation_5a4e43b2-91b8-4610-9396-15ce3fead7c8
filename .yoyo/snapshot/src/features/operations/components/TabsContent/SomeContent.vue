<template>
	<div>
		<farm-row justify="space-between">
			<farm-col cols="12" class="mb-4">
				<farm-idcaption icon="pencil-outline" copyText="">
					<template v-slot:title>
						<farm-heading :type="6">
							Parametrize as opções de dias e horário para as operações
						</farm-heading>
					</template>
					<template v-slot:subtitle>
						Os dias úteis e o horário de corte definem a regra que será aplicada para a
						seleção de datas na criação da operação.
					</template>
				</farm-idcaption>
			</farm-col>
			<farm-col cols="12">
				<farm-form v-model="valid" v-if="!isError">
					<farm-col>
						<img
							src="@/assets/icons/currency_exchange.svg"
							alt="imagem referente a dinheiro"
						/>
						<farm-bodytext> Modalidade de Cessão </farm-bodytext>
					</farm-col>
					<farm-row>
						<farm-col cols="12" md="4">
							<farm-label for="form-workingDays" required>
								<PERSON><PERSON>
								<farm-tooltip>
									<farm-caption variation="semiBold" color="white">
										<PERSON><PERSON>
									</farm-caption>
									<farm-caption variation="regular" color="white">
										Quantidade de Dias Úteis que serão permitidos para seleção
										da Data de Desembolso.
									</farm-caption>
									<template v-slot:activator>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-label>
							<farm-textfield-v2
								v-model="workingDays"
								mask="##"
								id="form-workingDays"
								:rules="[required]"
							/>
						</farm-col>
						<farm-col cols="12" md="4">
							<farm-label for="form-cuttingTime" required>
								Horário de Corte
								<farm-tooltip>
									<farm-caption variation="semiBold" color="white">
										Horário de Corte
									</farm-caption>
									<farm-caption variation="regular" color="white">
										Horário de corte para a realização de cessões em D+1.
									</farm-caption>
									<template v-slot:activator>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-label>
							<farm-select-auto-complete
								id="form-cuttingTime"
								v-model="cuttingTime"
								itemText="label"
								itemValue="value"
								:items="hours"
								:rules="[requiredSelect]"
							/>
						</farm-col>
					</farm-row>
				</farm-form>
			</farm-col>

			<!-- Modalidade de crédito ponte -->
			<farm-col cols="12">
				<farm-form v-model="valid" v-if="!isError">
					<farm-col>
						<img
							src="@/assets/icons/currency_exchange.svg"
							alt="imagem referente a dinheiro"
						/>
						<farm-bodytext> Modalidade de Crédito ponte </farm-bodytext>
					</farm-col>
					<farm-row>
						<farm-col cols="12" md="4">
							<farm-label for="form-workingDays" required>
								Dias Úteis
								<farm-tooltip>
									<farm-caption variation="semiBold" color="white">
										Dias Úteis
									</farm-caption>
									<farm-caption variation="regular" color="white">
										Quantidade de Dias Úteis que serão permitidos para seleção
										da Data de Desembolso.
									</farm-caption>
									<template v-slot:activator>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-label>
							<farm-textfield-v2
								v-model="workingDays"
								mask="##"
								id="form-workingDays"
								:rules="[required]"
							/>
						</farm-col>
						<farm-col cols="12" md="4">
							<farm-label for="form-cuttingTime" required> D+ </farm-label>
							<farm-textfield-v2
								v-model="workingDays"
								mask="##"
								id="form-workingDays"
								:rules="[required]"
							/>
						</farm-col>
						<farm-col cols="12" md="4">
							<farm-label for="form-cuttingTime" required>
								Horário de Corte
								<farm-tooltip>
									<farm-caption variation="semiBold" color="white">
										Horário de Corte
									</farm-caption>
									<farm-caption variation="regular" color="white">
										Horário de corte para a realização de cessões em D+1.
									</farm-caption>
									<template v-slot:activator>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-label>
							<farm-select-auto-complete
								id="form-cuttingTime"
								v-model="cuttingTime"
								itemText="label"
								itemValue="value"
								:items="hours"
								:rules="[requiredSelect]"
							/>
						</farm-col>
					</farm-row>
				</farm-form>
			</farm-col>

                <!-- Modalidade de Originação -->
                <farm-col cols="12">
				<farm-form v-model="valid" v-if="!isError">
					<farm-col>
						<img
							src="@/assets/icons/currency_exchange.svg"
							alt="imagem referente a dinheiro"
						/>
						<farm-bodytext> Modalidade de Crédito ponte </farm-bodytext>
					</farm-col>
					<farm-row>
						<farm-col cols="12" md="4">
							<farm-label for="form-workingDays" required>
								Dias Úteis
								<farm-tooltip>
									<farm-caption variation="semiBold" color="white">
										Dias Úteis
									</farm-caption>
									<farm-caption variation="regular" color="white">
										Quantidade de Dias Úteis que serão permitidos para seleção
										da Data de Desembolso.
									</farm-caption>
									<template v-slot:activator>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-label>
							<farm-textfield-v2
								v-model="workingDays"
								mask="##"
								id="form-workingDays"
								:rules="[required]"
							/>
						</farm-col>
						<farm-col cols="12" md="4">
							<farm-label for="form-cuttingTime" required> D+ </farm-label>
							<farm-textfield-v2
								v-model="workingDays"
								mask="##"
								id="form-workingDays"
								:rules="[required]"
							/>
						</farm-col>
						<farm-col cols="12" md="4">
							<farm-label for="form-cuttingTime" required>
								Horário de Corte
								<farm-tooltip>
									<farm-caption variation="semiBold" color="white">
										Horário de Corte
									</farm-caption>
									<farm-caption variation="regular" color="white">
										Horário de corte para a realização de cessões em D+1.
									</farm-caption>
									<template v-slot:activator>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-label>
							<farm-select-auto-complete
								id="form-cuttingTime"
								v-model="cuttingTime"
								itemText="label"
								itemValue="value"
								:items="hours"
								:rules="[requiredSelect]"
							/>
						</farm-col>
					</farm-row>
				</farm-form>
			</farm-col>

		</farm-row>
		<farm-row extra-decrease>
			<farm-container-footer noTop>
				<section class="d-flex">
					<div v-if="dataFooter" class="pt-3">
						<farm-resource-metainfo :infos="dataFooter" />
					</div>
					<farm-box align="end">
						<farm-btn
							title="Fechar"
							class="farm-btn--responsive mt-8"
							:disabled="isDisabledButton"
							@click="onChangeDateAndHour"
						>
							Salvar
						</farm-btn>
					</farm-box>
				</section>
			</farm-container-footer>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch, computed } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { hours } from '../TabDate/hours';

import { useEditDateAndHour } from '../TabDate/composables/useEditDateAndHour';
import { useGetDateAndHour } from '../TabDate/composables/useGetDateAndHour';

export default defineComponent({
	name: 'tab-date',
	setup() {
		const { isErrorDateAndHour, getDateAndHour } = useGetDateAndHour();

		const { editDateAndHour, isLoadingEditDateAndHour } = useEditDateAndHour();

		const workingDays = ref(null);
		const cuttingTime = ref(null);
		const isDisabledButton = ref(true);
		const valid = ref(null);
		const dataFooter = ref(null);
		const loadCustom = ref(false);

		const isLoading = computed(() => {
			return loadCustom.value || isLoadingEditDateAndHour.value;
		});

		const isError = computed(() => {
			return isErrorDateAndHour.value;
		});

		function required(value: string): boolean | string {
			if (!value || value.length === 0) {
				return 'Campo inválido';
			}
			if (parseInt(value) === 0) {
				return 'O campo Dias Úteis deve ser maior que 0.';
			}
			return true;
		}

		function requiredSelect(value: string): boolean | string {
			if (parseInt(value) > 0) {
				return true;
			}
			return 'Campo inválido';
		}

		function updatedEdit(): void {
			load();
		}

		function onChangeDateAndHour(): void {
			editDateAndHour(
				{
					day: parseInt(workingDays.value, 10),
					hour: parseInt(cuttingTime.value, 10),
				},
				updatedEdit
			);
		}

		function updatedForm(data): void {
			workingDays.value = data.day;
			cuttingTime.value = data.hour;
		}

		function updatedFooter(data): void {
			const updatedAt = defaultDateFormat(data.updatedAt.split('T')[0]) || 'N/A';
			const updatedHours = data.updatedAt.split('T')[1] || 'N/A';
			dataFooter.value = {
				createdAt: null,
				updatedAt,
				username: data.updatedBy,
				updatedHours,
			};
		}

		function updatedPage(data, error: boolean): void {
			if (error) {
				return;
			}
			updatedForm(data);
			updatedFooter(data);
			loadCustom.value = false;
			isDisabledButton.value = false;
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			loadCustom.value = true;
			getDateAndHour(updatedPage);
		}

		onMounted(() => {
			load();
		});

		watch(workingDays, newValue => {
			if (newValue && parseInt(newValue, 10) > 0) {
				isDisabledButton.value = false;
				return;
			}
			isDisabledButton.value = true;
		});

		return {
			isLoading,
			isError,
			isDisabledButton,
			workingDays,
			cuttingTime,
			valid,
			hours,
			dataFooter,
			required,
			requiredSelect,
			onReload,
			onChangeDateAndHour,
		};
	},
});
</script>
