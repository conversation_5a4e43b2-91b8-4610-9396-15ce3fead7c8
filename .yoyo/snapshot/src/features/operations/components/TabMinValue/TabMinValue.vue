<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" class="mb-4">
				<farm-idcaption icon="cog-outline" copyText="">
					<template v-slot:title>
						<farm-heading :type="6">
							Configurar valores mínimos para cada tipo de operação habilitada nas
							modalidades do Produto Comercial
						</farm-heading>
					</template>
					<template v-slot:subtitle>
						A configuração corresponde ao valor mínimo necessário para criar as operações dentro das
						modalidades associadas ao Produto Comercial.
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					:hasExtraFilters="false"
					label="Buscar Produto Comercial"
					@onInputChange="onInputChangeMainFilter"
				>
					<div class="d-flex">
						<farm-label class="mb-0 mr-2">Buscar Produto Comercial</farm-label>
						<counted
							v-if="isShowResultsLabel"
							pluralText="selecionados"
							singularText="selecionado"
							:value="totalFilter"
						/>
					</div>
				</farm-form-mainfilter>
			</farm-col>
			<farm-col cols="12" md="4" align="end">
				<farm-select
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="onSortSelect"
				/>
			</farm-col>
		</farm-row>
		<commercial-product-list
			v-if="!isError"
			:data="commercialProducts"
		/>
		<farm-row extra-decrease v-if="!isError">
			<farm-box>
				<farm-datatable-paginator
					v-if="commercialProducts.length > 0"
					class="mt-6 mb-n6"
					:page="filterCurrent.page + 1"
					:totalPages="commercialProductsPagination.totalPages"
					:initialLimitPerPage="filterCurrent.limit"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>
		<footer-form
			class="mt-4"
			labelButton="Salvar"
			:data="dataFooterForm"
			:isDisabledButton="isDisabledButton"
			:showLayoutData="true"
			:hiddenButtonCancel="true"
			@onSave="onSave"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-4 mb-8 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, ref } from 'vue';

import { usePageable } from '@/composibles/usePageable';

import Counted from '@/components/Counted';
import FooterForm from '@/components/FooterForm';

import CommercialProductList from './components/CommercialProductList';
import { useGetMinValues } from './composables/useGetMinValues';
import { useMinValueCache } from './composables/useMinValueCache';
import { useMinValueRedirect } from './composables/useMinValueRedirect';
import { useMinValueMessage } from './composables/useMinValueMessage';
import { useUpdatedMinValues } from './composables/useUpdatedMinValues';

export default defineComponent({
	name: 'tab-min-value',
	components: {
		CommercialProductList,
		Counted,
		FooterForm
	},
	setup() {
		const {
			commercialProducts,
			commercialProductsPagination,
			getCommercialProducts,
			isLoadingCommercialProducts
		} = useGetMinValues();
		const { isLoadingUpdatedMinValues, updated } = useUpdatedMinValues();
		const { reload } = useMinValueRedirect();
		const { notificationError, notificationSuccess } = useMinValueMessage();

		const {
			isFilterCounter,
			onInputChangeMainFilter
		} = usePageable(
			{
				calbackFn: params => {
					filterCurrent.value = {
						...params,
						page: params.page || 0,
						limit: params.page.limit || 10
					};
					getCommercialProducts(filterCurrent.value, updatedPage);
				},
				filters: {},
				keyInputSearch: 'search',
				sort: {
					order: 'ASC',
					orderby: 'name',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			commercialProductsPagination.value
		);
		const { minValueCache, hasMinimumValueZero } = useMinValueCache();

		const isLoading = computed(() => {
			return(
				isLoadingCommercialProducts.value ||
				isLoadingUpdatedMinValues.value
			);
		});
		const isDisabledButton = computed(() => {
			if(minValueCache.value === null) {
				return false;
			}
			return minValueCache.value.length > 0 && !hasMinimumValueZero();
		});

		const isError = ref(false);
		const isShowResultsLabel = ref(false);
		const totalFilter = ref(0);
		const sortModel = ref('name_ASC');
		const sortOptions = ref([
			{ label: 'Alfabética A-Z', value: 'name_ASC' },
			{ label: 'Alfabética Z-A', value: 'name_DESC' }
		]);
		const filterCurrent = ref({
			page: 0,
			limit: 10,
			order: 'ASC',
			orderby: 'name',
		});
		const dataFooterForm = ref(null);

		function onSortSelect(value): void {
			const [orderby, order] = value.split('_');
			sortModel.value = value;
			filterCurrent.value = {
				...filterCurrent.value,
				page: 0,
				orderby,
				order,
			};
			getCommercialProducts(filterCurrent.value, updatedPage);
		}

		function onChangePageLimit(value): void {
			filterCurrent.value = {
				...filterCurrent.value,
				page: 0,
				limit: value,
			};
			getCommercialProducts(filterCurrent.value, updatedPage);
		}

		function onChangePage(value): void {
			filterCurrent.value = {
				...filterCurrent.value,
				page: value - 1,
			};
			getCommercialProducts(filterCurrent.value, updatedPage);
		}

		function updatedSave(hasError){
			if(hasError){
				notificationError();
				return;
			}
			notificationSuccess();
			setTimeout(() => {
				reload();
			}, 2000);
		}

		function onSave(): void {
			const payload = {
				products:[...minValueCache.value]
			};
			updated(payload, updatedSave);
		}

		function updatedPage(hasError, {total, meta}): void {
			if(hasError){
				isError.value = true;
				return;
			}
			isError.value = false;
			isShowResultsLabel.value = false;
			if(isFilterCounter.value) {
				totalFilter.value = total;
				isShowResultsLabel.value = true;
			}
			dataFooterForm.value = meta;
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			const payload = {
				page: 0,
				limit: 10,
				order: 'ASC',
				orderby: 'name',
			};
			getCommercialProducts(payload, updatedPage);
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			isShowResultsLabel,
			isDisabledButton,
			commercialProducts,
			commercialProductsPagination,
			filterCurrent,
			sortModel,
			sortOptions,
			totalFilter,
			dataFooterForm,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onInputChangeMainFilter,
			onReload,
			onSave
		};
	},
});
</script>
