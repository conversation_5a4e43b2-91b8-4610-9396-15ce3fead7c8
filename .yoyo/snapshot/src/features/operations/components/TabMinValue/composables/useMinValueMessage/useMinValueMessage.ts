import { notification, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

type UseMinValueMessage = {
	notificationSuccess: Function;
	notificationError: Function;
};

export function useMinValueMessage(): UseMinValueMessage {

	function notificationSuccess(): void {
		notification(
			RequestStatusEnum.SUCCESS,
			'Valores Mínimos atualizados com sucesso!'
		);
	}

	function notificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			'Erro ao tentar atualizar os Valores Mínimos. Por favor tente mais tarde!'
		);
	}

	return {
		notificationSuccess,
		notificationError,
	};
}



