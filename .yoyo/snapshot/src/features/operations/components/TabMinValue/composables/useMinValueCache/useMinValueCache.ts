import { computed } from 'vue';

import { useGetter, useStore } from '@/composibles';

import { removeMaskMoney } from '@/helpers/removeMask';

export function useMinValueCache() {
	const store = useStore();

	const minValueCache = computed(useGetter('operations', 'minValueData'));

	function updatedMinValueCache(data): void {
		store.dispatch('operations/updatedMinValues', data);
	}

	function hasMinimumValueZero(): boolean {
		return minValueCache.value.some((product) =>{
			return product.modalities.some((modality) => {
				return modality.settlementTypes.some((settlementType) => {
					return settlementType.minimumValue === 0;
				});
			});
		});
	}

	function createSettlementType(data) {
		return {
			id: data.typeId,
			minimumValue: removeMaskMoney(data.valueMin),
		};
	}

	function createModality(data) {
		return {
			id: data.modalityId,
			settlementTypes: [createSettlementType(data)],
		};
	}

	function createCommercialProduct(data) {
		return {
			id: data.commercialProductId,
			modalities: [createModality(data)],
		};
	}

	function hasUpdatedSettlementType(data): boolean {
		return minValueCache.value.some((commercialProduct) => {
			if (commercialProduct.id !== data.commercialProductId) {
				return false;
			}
			const modality = commercialProduct.modalities.find((modality) => {
				return modality.id === data.modalityId;
			});
			if (!modality) {
				return false;
			}
			return modality.settlementTypes.some((settlementType) => {
				return settlementType.id === data.typeId;
			});
		});
	}

	function hasUpdatedModalities(data): boolean {
		return minValueCache.value.filter((item) => {
			return item.id === data.commercialProductId;
		}).some((item) => {
			return item.modalities.some((modality) => modality.id === data.modalityId);
		});
	}

	function hasUpdatedCommercialProduct(data): boolean {
		return minValueCache.value.some((item) => {
			return item.id === data.commercialProductId;
		});
	}

	function createCacheSettlementType(data): void {
		const newPayload = minValueCache.value.map((item) => {
			if(item.id === data.commercialProductId){
				return {
					...item,
					modalities: item.modalities.map((modality) => {
						if(modality.id === data.modalityId){
							return {
								...modality,
								settlementTypes: [
									...modality.settlementTypes,
									createSettlementType(data)
								]
							};
						}
						return modality;
					})
				};
			}
			return item;
		});
		updatedMinValueCache(newPayload);
	}

	function updatedCacheSettlementType(data): void {
		const newPayload = minValueCache.value.map((item) => {
			if(item.id === data.commercialProductId){
				return {
					...item,
					modalities: item.modalities.map((modality) => {
						if(modality.id === data.modalityId){
							return {
								...modality,
								settlementTypes: modality.settlementTypes.map((settlementType) => {
									if(settlementType.id === data.typeId){
										return {
											id: data.typeId,
											minimumValue: removeMaskMoney(data.valueMin),
										};
									}
									return settlementType;
								})
							};
						}
						return modality;
					})
				};
			}
			return item;
		});
		updatedMinValueCache(newPayload);
	}


	function updatedCacheModalities(data): void {
		if(hasUpdatedSettlementType(data)){
			updatedCacheSettlementType(data);
			return;
		}
		createCacheSettlementType(data);
	}

	function createCacheModalities(data): void {
		const newPayload = minValueCache.value.map((item) => {
			if(item.id === data.commercialProductId){
				return {
					...item,
					modalities:[
						...item.modalities,
						createModality(data)
					]
				};
			}
			return item;
		});
		updatedMinValueCache(newPayload);
	}

	function updatedCacheCommercialProduct(data): void {
		if(hasUpdatedModalities(data)){
			updatedCacheModalities(data);
			return;
		}
		createCacheModalities(data);
	}

	function createCacheCommercialProduct(data): void {
		const newCommercialProduct =  createCommercialProduct(data);
		const newPayload = [
			...minValueCache.value,
			newCommercialProduct
		];
		updatedMinValueCache(newPayload);
	}

	function addFirstNewCommercialProduct(data): void {
		const cache = createCommercialProduct(data);
		updatedMinValueCache([cache]);
	}

	function verifyCommercialProduct(data): void {
		if(hasUpdatedCommercialProduct(data)){
			updatedCacheCommercialProduct(data);
			return;
		}
		createCacheCommercialProduct(data);
	}

	function addValueMin(data): void {
		if(minValueCache.value === null){
			addFirstNewCommercialProduct(data);
			return;
		}
		verifyCommercialProduct(data);
	}

	return {
		minValueCache,
		updatedMinValueCache,
		addValueMin,
		hasMinimumValueZero
	};
}
