

import { computed } from 'vue';
import type { ComputedRef } from 'vue/types';
import { useMutation } from '@tanstack/vue-query';

import { updatedMinValues } from '@/features/operations/services';

type UseUpdatedMinValues = {
	isLoadingUpdatedMinValues: ComputedRef<boolean>;
	isErrorUpdatedMinValues: ComputedRef<boolean>;
	updated: Function;
};

export function useUpdatedMinValues(): UseUpdatedMinValues {

	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => updatedMinValues(params),
		onSuccess: (response) => {
			if(callFunc) callFunc(false);
		},
		onError: (error) => {
			if(callFunc) callFunc(true);
		},
	});

	const isLoadingUpdatedMinValues = computed(() => {
		return isLoading.value;
	});

	const isErrorUpdatedMinValues = computed(() => {
		return isError.value;
	});

	function updated(data, callback: Function) {
		mutate(data);
		callFunc = callback;
	}

	return {
		isLoadingUpdatedMinValues,
		isErrorUpdatedMinValues,
		updated,
	};
}
