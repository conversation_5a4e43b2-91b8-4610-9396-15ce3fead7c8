<template>
	<div class="header">
		<div class="field-id">
			<farm-caption variation="semiBold" tag="span">
				ID: {{ data.id }}
			</farm-caption>
		</div>
		<div class="header-line"></div>
		<div>
			<farm-heading :type="6">
				{{ data.name }}
			</farm-heading>
		</div>
		<div class="header-line" v-if="!isDataEmpty"></div>
		<div class="modalitys" v-if="!isDataEmpty">
			<div class="modalitys-items" v-for="(item, index) in data.modalities" :key="item.key">
				<div>
					<farm-bodysmall variation="bold" tag="span">
						{{ item.name }}
					</farm-bodysmall>
				</div>
				<div class="ml-4">
					<farm-icon color="primary" size="16px">
						check-circle-outline
					</farm-icon>
				</div>
				<div class="header-line" v-if="(index +1) < data.modalities.length"></div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

export default defineComponent({
	name: 'commercial-product-card-header',
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	setup(props) {
		const { data } = toRefs(props);

		const isDataEmpty = computed(() => {
			return data.value.modalities.length === 0;
		});

		return {
			data,
			isDataEmpty
		};
	}
});
</script>
<style lang="scss" scoped>
@import './CommercialProductCardHeader';
</style>

