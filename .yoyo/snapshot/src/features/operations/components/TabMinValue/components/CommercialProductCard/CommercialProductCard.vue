<template>
	<farm-collapsible
		custom
		title=""
		:textChip="data.statusName"
		:colorChip="data.status ? 'success' : 'neutral'"
		:showChip="true"
	>
		<template #custom>
			<commercial-product-card-header :data="data" />
		</template>
		<farm-line noSpacing />
		<modality-list :data="data.forms" :commercialProductId="data.id" />
	</farm-collapsible>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';

import CommercialProductCardHeader from '../CommercialProductCardHeader';
import ModalityList from '../ModalityList';

export default defineComponent({
	name: 'commercial-product-card',
	components:{
		CommercialProductCardHeader,
		ModalityList
	},
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	setup(props) {
		const { data } = toRefs(props);
		return {
			data
		};
	}
});
</script>
