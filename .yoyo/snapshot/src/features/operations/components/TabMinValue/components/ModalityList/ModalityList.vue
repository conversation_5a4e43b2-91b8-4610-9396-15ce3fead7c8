<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" v-for="item in data" :key="item.key">
				<ModalityCard :data="item" :commercialProductId="commercialProductId" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

import ModalityCard from '../ModalityCard';

export default defineComponent({
	name: 'modality-list',
	components: {
		ModalityCard
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
		commercialProductId:{
			type: Number,
			required: true
		}
	},
	setup(props) {

		const { data } = toRefs(props);

		const isDataEmpty = computed(() => data.value.length === 0);

		return {
			isDataEmpty
		};
	},
});
</script>
