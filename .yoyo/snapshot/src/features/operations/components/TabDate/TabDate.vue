<template>
	<div>
		<farm-row justify="space-between">
			<farm-col cols="12" class="mb-4">
				<farm-idcaption icon="pencil-outline" copyText="">
					<template v-slot:title>
						<farm-heading :type="6">
							Parametrize as opções de dias e horário para as operações
						</farm-heading>
					</template>
					<template v-slot:subtitle>
						Os dias úteis e o horário de corte definem a regra que será aplicada para a
						seleção de datas na criação da operação.
					</template>
				</farm-idcaption>
			</farm-col>
			<farm-loader mode="overlay" v-if="isLoading" />
			<ModalidadeCessao
				v-if="modalitiesData.cessao"
				:is-error="isError"
				:workingDays="modalitiesData.cessao.day"
				:cutting-time="modalitiesData.cessao.hour"
				@onChange="handleCessaoChange"
			/>

			<ModalidadeCreditoPonte
				v-if="modalitiesData.creditoPonte"
				:is-error="isError"
				:workingDays="modalitiesData.creditoPonte.day"
				:dplus="modalitiesData.creditoPonte.daysPlus"
				:cutting-time="modalitiesData.creditoPonte.hour"
				@onChange="handleCreditoPonteChange"
			/>

			<ModalidadeOriginacao
				v-if="modalitiesData.originacao"
				:is-error="isError"
				:workingDays="modalitiesData.originacao.day"
				:dplus="modalitiesData.originacao.daysPlus"
				:cutting-time="modalitiesData.originacao.hour"
				@onChange="handleOriginationChange"
			/>
		</farm-row>

		<farm-row extra-decrease>
			<farm-container-footer noTop>
				<section class="d-flex">
					<div v-if="dataFooter" class="pt-7">
						<farm-resource-metainfo :infos="dataFooter" />
					</div>
					<farm-box align="end">
						<farm-btn
							title="Fechar"
							class="farm-btn--responsive mt-8"
							@click="save"
							:disabled="!isFormValid"
						>
							Salvar
						</farm-btn>
					</farm-box>
				</section>
			</farm-container-footer>
		</farm-row>
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';
import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';
import ModalidadeCessao from './components/Cessão.vue';
import ModalidadeCreditoPonte from './components/Credit.vue';
import ModalidadeOriginacao from './components/Origination.vue';
import { useEditDateAndHour } from './composables/useEditDateAndHour';
import { useGetDateAndHour } from './composables/useGetDateAndHour';
import { ModalitiesData } from './types';
import { areAllFieldsValid } from '../../helpers/validateTabsFields';

export default defineComponent({
	name: 'TabDate',
	components: {
		ModalidadeCessao,
		ModalidadeCreditoPonte,
		ModalidadeOriginacao,
	},
	setup() {
		const { isErrorDateAndHour, getDateAndHour } = useGetDateAndHour();
		const { editDateAndHour } = useEditDateAndHour();

		const modalitiesData = ref<ModalitiesData>({
			cessao: null,
			creditoPonte: null,
			originacao: null,
		});

		const isFormValid = ref(false);

		const dataFooter = ref(null);
		const loadCustom = ref(false);

		const isLoading = computed(() => {
			return loadCustom.value;
		});

		const isError = computed(() => {
			return isErrorDateAndHour.value;
		});

		function updatedFooter(data): void {
			const updatedAt = defaultDateFormat(data.updatedAt.split('T')[0]) || 'N/A';
			const updatedHours = data.updatedAt.split('T')[1] || 'N/A';
			dataFooter.value = {
				createdAt: null,
				updatedAt,
				username: data.updatedBy,
				updatedHours,
			};
		}

		const handleCreditoPonteChange = values => {
			if (modalitiesData.value.creditoPonte) {
				modalitiesData.value.creditoPonte = {
					...modalitiesData.value.creditoPonte,
					day: values.workingDays,
					daysPlus: values.dplus,
					hour: values.cuttingTime,
				};
			}
			isFormValid.value = areAllFieldsValid(modalitiesData.value);
		};

		const handleOriginationChange = values => {
			if (modalitiesData.value.originacao) {
				modalitiesData.value.originacao = {
					...modalitiesData.value.originacao,
					day: values.workingDays,
					daysPlus: values.dplus,
					hour: values.cuttingTime,
				};
			}
			isFormValid.value = areAllFieldsValid(modalitiesData.value);
		};

		const handleCessaoChange = values => {
			if (modalitiesData.value.cessao) {
				modalitiesData.value.cessao = {
					...modalitiesData.value.cessao,
					day: values.workingDays,
					hour: values.cuttingTime,
				};
			}
			isFormValid.value = areAllFieldsValid(modalitiesData.value);
		};

		function updatedPage(data, error: boolean): void {
			if (error) {
				loadCustom.value = true;
				return;
			}
			if (data?.data?.parameters) {
				modalitiesData.value = {
					cessao: data.data.parameters.find(item => item.operationType === 1),
					originacao: data.data.parameters.find(item => item.operationType === 2),
					creditoPonte: data.data.parameters.find(item => item.operationType === 3),
				};
				updatedFooter(data.data);
				loadCustom.value = false;
			}
		}

		function save(): void {
			const filteredData = [
				{
					name: 'Cessão',
					id: modalitiesData.value.cessao?.id,
					operationType: 1,
					day: modalitiesData.value.cessao?.day,
					daysPlus: modalitiesData.value.cessao?.daysPlus,
					hour: modalitiesData.value.cessao?.hour,
				},
				{
					name: 'Originacao',
					id: modalitiesData.value.originacao?.id,
					operationType: 2,
					day: modalitiesData.value.originacao?.day,
					daysPlus: modalitiesData.value.originacao?.daysPlus,
					hour: modalitiesData.value.originacao?.hour,
				},
				{
					name: 'CreditoPonte',
					id: modalitiesData.value.creditoPonte?.id,
					operationType: 3,
					day: modalitiesData.value.creditoPonte?.day,
					daysPlus: modalitiesData.value.creditoPonte?.daysPlus,
					hour: modalitiesData.value.creditoPonte?.hour,
				},
			];

			editDateAndHour(filteredData);

			onReload();
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			loadCustom.value = true;
			getDateAndHour(updatedPage);
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			isFormValid: computed(() => areAllFieldsValid(modalitiesData.value)),
			dataFooter,
			modalitiesData,
			onReload,
			handleCreditoPonteChange,
			handleOriginationChange,
			handleCessaoChange,
			save,
		};
	},
});
</script>
