<template>
	<farm-col cols="12">
		<farm-form v-model="valid" v-if="!isError">
			<farm-col class="d-flex my-2" :style="{ gap: '0.5rem', paddingLeft: '0px'}">
				<img src="@/assets/icons/currency_exchange.svg" alt="imagem referente a dinheiro" />
				<farm-bodytext variation="bold"> Modalidades de Crédito Ponte </farm-bodytext>
			</farm-col>
			<farm-row>
				<farm-col cols="12" md="4">
					<farm-label for="form-workingDays" required>
						Dias Úteis
						<farm-tooltip>
							<farm-caption variation="semiBold" color="white">
								Dias Úteis
							</farm-caption>
							<farm-caption variation="regular" color="white">
								Quantidade de Dias Úteis que serão permitidos para seleção da Data
								de Desembolso.
							</farm-caption>
							<template v-slot:activator>
								<farm-icon size="sm" color="gray">help-circle</farm-icon>
							</template>
						</farm-tooltip>
					</farm-label>
					<farm-textfield-v2
						v-model="workingDaysLocal"
						mask="##"
						id="form-workingDays"
						:rules="[required]"
					/>
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-label for="form-dplus" required>
						D+
						<farm-tooltip>
							<farm-caption variation="semiBold" color="white"> D + </farm-caption>
							<farm-caption variation="regular" color="white">
								Dias acrescidos para a primeira data de desembolso
							</farm-caption>
							<template v-slot:activator>
								<farm-icon size="sm" color="gray">help-circle</farm-icon>
							</template>
						</farm-tooltip>
					</farm-label>
					<farm-textfield-v2
						v-model="dplusLocal"
						mask="##"
						id="form-dplus"
						:rules="[requiredDayPlus]"
					/>
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-label for="form-cuttingTime" required>
						Horário de Corte
						<farm-tooltip>
							<farm-caption variation="semiBold" color="white">
								Horário de Corte
							</farm-caption>
							<farm-caption variation="regular" color="white">
								Horário de corte para a realização de cessões em D+1.
							</farm-caption>
							<template v-slot:activator>
								<farm-icon size="sm" color="gray">help-circle</farm-icon>
							</template>
						</farm-tooltip>
					</farm-label>
					<farm-select-auto-complete
						id="form-cuttingTime"
						v-model="cuttingTimeLocal"
						itemText="label"
						itemValue="value"
						:items="hours"
						:rules="[requiredSelect]"
					/>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-col>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { hours } from '../hours';

export default defineComponent({
	name: 'ModalidadeCreditoPonte',
	props: {
		isError: {
			type: Boolean,
			required: true,
		},
		workingDays: {
			type: Number,
			required: false,
		},
		cuttingTime: {
			type: Number,
			required: false,
		},
		dplus: {
			type: Number,
			required: false,
		},
	},
	emits: ['update:workingDays', 'update:dplus', 'update:cuttingTime', 'onChange'],
	setup(props, { emit }) {
		const workingDaysLocal = ref(props.workingDays);
		const dplusLocal = ref(props.dplus);
		const cuttingTimeLocal = ref(props.cuttingTime);
		const valid = ref(null);

		function required(value: string): boolean | string {
			if (!value || value.length === 0) {
				return 'Este campo é obrigatório.';
			}
			if (parseInt(value) === 0) {
				return 'O campo Dias Úteis deve ser maior que 0.';
			}
			return true;
		}
		
		function requiredDayPlus(value: string): boolean | string {
			if (!value || value.length === 0) {
				return 'Este campo é obrigatório.';
			}
			if (parseInt(value) === 0) {
				return 'O campo D+ deve ser maior que 0.';
			}
			return true;
		}

		function requiredSelect(value: string): boolean | string {
			if (parseInt(value) > 0) {
				return true;
			}
			return 'Campo inválido';
		}

		const emitChange = () => {
			emit('onChange', {
				workingDays: parseInt(workingDaysLocal.value as any),
				dplus: parseInt(dplusLocal.value as any),
				cuttingTime: parseInt(cuttingTimeLocal.value as any),
			});
		};

		watch(workingDaysLocal, newValue => {
			emit('update:workingDays', parseInt(newValue as any));
			emitChange();
		});

		watch(dplusLocal, newValue => {
			emit('update:dplus', parseInt(newValue as any));
			emitChange();
		});

		watch(cuttingTimeLocal, newValue => {
			emit('update:cuttingTime', parseInt(newValue as any));
			emitChange();
		});

		return {
			workingDaysLocal,
			dplusLocal,
			cuttingTimeLocal,
			valid,
			hours,
			required,
			requiredDayPlus,
			requiredSelect,
		};
	},
});
</script>
