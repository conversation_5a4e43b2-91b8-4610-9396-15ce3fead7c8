import {
	RequestStatusEnum,
	errorBuilder,
	queryString,
} from '@farm-investimentos/front-mfe-libs-ts';
import {
	getUsers, getUsersOptions,
	putUserVehicleFinancePermissions,
	getCommunicationSettings,
	putCommunicationSsettings
} from '@/services/dashboard';
import { getProductsResale } from '@/services/registerV3';
import { PRODUCT_TYPE } from '@/constants/constants';

export default {

	async fetchUsers({ commit }, { idProduct, filters }) {
		commit('setUsersRequestStatus', RequestStatusEnum.START);
		try {
			const params = queryString(filters, {});
			const { data } = await getUsers(idProduct, params);
			commit('setUsers', data);
			commit('setUsersRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setUsersRequestStatus', errorBuilder(error));
		}
	},

	async fetchCommunicationSsettings({ commit }, { idProduct, filters }) {
		commit('setCommunicationSettingsRequestStatus', RequestStatusEnum.START);
		try {
			const params = queryString(filters, {});
			const { data } = await getCommunicationSettings(idProduct, params);

			commit('setCommunicationSettings', data);
			commit('setCommunicationSettingsRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setCommunicationSettingsRequestStatus', errorBuilder(error));
		}
	},

	async fetchUsersOptions({ commit }, { idProduct }) {
		commit('setUsersOptionsRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getUsersOptions(idProduct);
			commit('setUsersOptions', data);
			commit('setUsersOptionsRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setUsersOptionsRequestStatus', errorBuilder(error));
		}
	},

	async fetchProductsOptions({ commit }, { idProduct }) {
		commit('setProductsOptionsRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getProductsResale(idProduct);
			const filteredData = data.content.filter(item => item.type === PRODUCT_TYPE.VAREJO.label);
			commit('setProductsOptions', { ...data, content: filteredData });
			commit('setProductsOptionsRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setProductsOptionsRequestStatus', errorBuilder(error));
		}
	},

	async updateFinancialVehicleUserSettings({ commit }, { idProduct, payload }) {
		commit('setFinancialVehicleUserSettingsRequestStatus', RequestStatusEnum.START);
		try {
			await putUserVehicleFinancePermissions(idProduct, payload);
			commit('setFinancialVehicleUserSettingsRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setFinancialVehicleUserSettingsRequestStatus', errorBuilder(error));
		}
	},

	async updateCommunicationSsettings({ commit }, { payload, idProduct }) {
		commit('setUpdateCommunicationsSettingsRequestStatus', RequestStatusEnum.START);
		try {
			await putCommunicationSsettings(idProduct, payload);
			commit('setUpdateCommunicationsSettingsRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setUpdateCommunicationsSettingsRequestStatus', errorBuilder(error));
		}
	}
};
