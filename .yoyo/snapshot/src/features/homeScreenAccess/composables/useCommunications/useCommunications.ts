import { computed, ref, watch } from 'vue';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import { useStore, useSelectedProductId, useGetter, useIsLoading } from '@/composibles';


export default function useCommunications() {

	const { dispatch } = useStore();
	const currentProductId = useSelectedProductId();
	const productsOptions = computed(useGetter('homeScreenAccess', 'productsOptionsListResult'));
	const usersOptions = computed(useGetter('homeScreenAccess', 'usersOptionsListResult'));
	const communicationSettingsResult = computed(useGetter('homeScreenAccess', 'communicationSettingsResult'));
	const communicationSettings = computed(useGetter('homeScreenAccess', 'communicationSettings'));
	const communicationSettingsRequestStatus = computed(useGetter('homeScreenAccess', 'communicationSettingsRequestStatus'));
	const updateCommunicationsSettingsRequestStatus = computed(
		useGetter('homeScreenAccess', 'updateCommunicationsSettingsRequestStatus')
	);
	const selectedProduct = ref();
	const selectedUser = ref(null);
	const selectedItems = ref([]);
	const modalTableItems = ref([]);
	const page = ref(1);
	const toggleModal = ref(false);
	const filters = ref({
		userId: null,
		page: 0,
		size: 5,
	});

	const isLoading = useIsLoading([communicationSettingsRequestStatus, updateCommunicationsSettingsRequestStatus]);
	const isError = computed(() => {
		return communicationSettingsRequestStatus.value.type === RequestStatusEnum.ERROR;
	});

	const fetchProductsOptions = () => {
		dispatch('homeScreenAccess/fetchProductsOptions', {
			idProduct: currentProductId.value,
		});
	};

	const fetchUsersOptions = id => {
		dispatch('homeScreenAccess/fetchUsersOptions', {
			idProduct: id,
		});
	};

	const fetchCommunicationSsettings = () => {
		dispatch('homeScreenAccess/fetchCommunicationSsettings', {
			idProduct: selectedProduct.value,
			filters: { ...filters.value },
		});
	};

	const updateCommunicationSsettings = (payload) => {
		dispatch('homeScreenAccess/updateCommunicationSsettings', {
			idProduct: selectedProduct.value,
			payload
		});
	};

	const onSelectProduct = id => {
		selectedUser.value = null;
		selectedItems.value = [];
		filters.value = {
			...filters.value,
			userId: null,
		};
		fetchCommunicationSsettings();
		fetchUsersOptions(id);
	};

	const onSelectUser = data => {
		filters.value = {
			...filters.value,
			userId: data,
		};
		selectedItems.value = [];
		fetchCommunicationSsettings();
	};

	const onInputSelectUser = data => {
		filters.value = {
			...filters.value,
			userId: data,
		};
		fetchCommunicationSsettings();
	};

	const onChangeLimitPerPage = limit => {
		filters.value = {
			...filters.value,
			size: limit,
			page: 0,
		};
		page.value = 1;
		fetchCommunicationSsettings();
	};

	const onChangePage = currentPage => {
		page.value = currentPage;
		filters.value = {
			...filters.value,
			page: currentPage - 1,
		};
		fetchCommunicationSsettings();
	};

	const onClickCofinguration = () => {
		toggleModal.value = !toggleModal.value;
		modalTableItems.value = [];
		const communicationsSettings = selectedItems.value[0].data;
		if (selectedItems.value.length === 1) {
			modalTableItems.value = communicationsSettings.map((item, index) => {

				return {
					enabled: item.enabled,
					name: item.type,
					communicationId: item.id,
					id: index,
				};
			});
		} else {
			modalTableItems.value = communicationsSettings.map((item, index) => {
				return {
					enabled: false,
					name: item.type,
					communicationId: item.id,
					id: index,
				};
			});
		}
	};

	const onConfirmConfiguration = () => {
		toggleModal.value = false;

		const payload = { settings: [] };
		modalTableItems.value.forEach(communication => {
			selectedItems.value.forEach(user => {
				payload.settings.push({
					enabled: communication.enabled,
					communicationId: communication.communicationId,
					userId: user.userId,
				});
			});
		});
		updateCommunicationSsettings(payload);
		selectedItems.value = [];
		toggleModal.value = false;
	};

	watch(productsOptions, (newState, prevState) => {
		const firstPosition = newState[0].value;
		selectedProduct.value = firstPosition;
		onSelectProduct(firstPosition);
	});


	watch(updateCommunicationsSettingsRequestStatus, newValue => {
		if (newValue === RequestStatusEnum.SUCCESS) {
			fetchCommunicationSsettings();
			notification(RequestStatusEnum.SUCCESS, 'Configuração realizada com sucesso');
		}
		if (newValue.type === RequestStatusEnum.ERROR) {
			notification(RequestStatusEnum.ERROR, 'Erro ao realizar configuração');
		}
	});


	return {
		page,
		isLoading,
		isError,
		productsOptions,
		selectedProduct,
		communicationSettings,
		selectedItems,
		usersOptions,
		selectedUser,
		communicationSettingsResult,
		toggleModal,
		modalTableItems,
		fetchProductsOptions,
		onSelectProduct,
		onSelectUser,
		onInputSelectUser,
		onChangeLimitPerPage,
		onChangePage,
		onClickCofinguration,
		onConfirmConfiguration,

	};
}
