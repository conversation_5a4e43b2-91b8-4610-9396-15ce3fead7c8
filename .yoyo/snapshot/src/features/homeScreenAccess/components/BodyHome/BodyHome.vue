<template>
	<div class="wrapper">
		<farm-modal
			size="md"
			v-model="showModal"
			:offsetTop="48"
			:offsetBottom="80"
			:persistent="true"
		>
			<template v-slot:header>
				<farm-dialog-header
					title="Configurar Limites"
					@onClose="() => (showModal = false)"
				/>
			</template>
			<template v-slot:content>
				<farm-caption class="mb-4" v-if="selectedItems.length > 1">
					Ao selecionar múltiplos usuários é necessário definir os programas novamente. As
					definições anteriores não serão consideradas.
				</farm-caption>
				<v-data-table
					hide-default-footer
					id="v-data-table--modal"
					:headers="modalHeaders"
					:items="modalTableItems"
					:server-items-length="modalTableItems.length"
				>
					<template slot="no-data">
						<farm-emptywrapper subtitle="Nenhum programa com limite pré aprovado" />
					</template>

					<template v-slot:[`item.enabled`]="{ item }">
						<farm-row justify="center">
							<farm-switcher v-model="item.enabled" block />
						</farm-row>
					</template>
				</v-data-table>
			</template>

			<template v-slot:footer>
				<farm-dialog-footer
					@onConfirm="confirmConfiguration"
					@onClose="() => (showModal = false)"
					confirmLabel="Salvar"
				/>
			</template>
		</farm-modal>

		<farm-container>
			<farm-box>
				<farm-tabs
					:forceUppercase="false"
					:showCounter="false"
					:tabs="tabs"
					@update="updateTab"
				/>
			</farm-box>
			<div v-if="currentTab === LIMITS" class="py-6 px-6">
				<farm-row>
					<farm-col cols="3" md="2" lg="4">
						<div>
							<farm-label>Produto</farm-label>
							<farm-select-auto-complete
								v-model="selectedProduct"
								id="select_product"
								item-text="text"
								item-value="value"
								:items="productsOptions"
								@change="onSelectProduct"
							/>
						</div>
					</farm-col>

					<farm-col cols="3" md="2" lg="3">
						<farm-label for="select_user">Usuário</farm-label>
						<farm-select-auto-complete
							v-model="selectedUser"
							id="select_user"
							:disabled="selectedProduct == null"
							item-text="text"
							item-value="value"
							:items="usersOptions"
							@input="onInputSelectUser"
							@change="onSelectUser"
						/>
					</farm-col>

					<farm-col cols="6" md="4" lg="5" alignSelf="center">
						<div class="d-flex justify-end">
							<ConfirmButton
								:disabled="selectedItems.length === 0"
								:icon="true"
								customIcon="cog"
								@click="handleClickSettings"
								>Configurar</ConfirmButton
							>
						</div>
					</farm-col>
				</farm-row>

				<farm-row v-if="!isError" extra-decrease>
					<farm-box>
						<v-data-table
							hide-default-footer
							v-model="selectedItems"
							id="limits-table"
							:headers="headers"
							:items="users.items"
							:server-items-length="users.items.length"
							show-select
						>
							<template slot="no-data">
								<farm-emptywrapper
									subtitle="Tente filtrar novamente sua pesquisa"
								/>
							</template>
							<template v-slot:footer>
								<farm-datatable-paginator
									class="mt-6 mb-n6"
									:initialLimitPerPage="5"
									:page="page"
									:totalPages="users.totalPages"
									@onChangePage="onChangePage"
									@onChangeLimitPerPage="onChangeLimitPerPage"
								/>
							</template>
						</v-data-table>
					</farm-box>
				</farm-row>
				<farm-loader mode="overlay" v-if="isLoading" />
				<div v-if="isError" class="my-10 d-flex justify-center">
					<farm-alert-reload label="Ocorreu um erro" @onClick="fetchUsers" />
				</div>
			</div>
			<div class="py-6 px-6" v-if="currentTab !== LIMITS">
				<Communications />
			</div>
		</farm-container>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch } from 'vue';
import { LIMITS, COMUNICATIONS } from '@/constants';
import { TabTypes } from '@/types';

import { headers, modalHeaders } from '../../configurations/headers';
import { tabDefault } from '../../configurations/tabs';
import { useLimits } from '../../composables';
import Communications from '../Communications';

export default defineComponent({
	components: {
		Communications,
	},
	setup() {
		const {
			users,
			usersOptions,
			isLoading,
			page,
			selectedUser,
			productsOptions,
			selectedProduct,
			selectedItems,
			isError,
			onChangePage,
			onChangeLimitPerPage,
			onSelectUser,
			onInputSelectUser,
			fetchProductsOptions,
			onSelectProduct,
			updateFinancialVehicleUserSettings,
			fetchUsers,
		} = useLimits();

		const tabLimits = ref(true);
		const currentTab = ref(LIMITS);
		const tabs = ref(tabDefault);
		const showModal = ref(false);
		const modalTableItems = ref([]);

		const updateTab = (item: TabTypes): any => {
			if (!item) {
				return;
			}
			currentTab.value = item.name;
		};

		const handleClickSettings = () => {
			showModal.value = true;

			const { financialVehicleSettings } = selectedItems.value[0].data;

			if (selectedItems.value.length === 1) {
				modalTableItems.value = financialVehicleSettings.map(item => {
					return {
						enabled: item.enabled,
						programName: item.programName,
						financialVehicleName: item.financialVehicleName,
						id: item.productFinancialVehicleId,
					};
				});
			} else {
				modalTableItems.value = financialVehicleSettings.map(item => {
					return {
						enabled: false,
						programName: item.programName,
						financialVehicleName: item.financialVehicleName,
						id: item.productFinancialVehicleId,
					};
				});
			}
		};

		const confirmConfiguration = () => {
			let payload = { financialVehicleSettings: [] };
			modalTableItems.value.forEach(program => {
				selectedItems.value.forEach(user => {
					payload.financialVehicleSettings.push({
						enabled: program.enabled,
						financialVehicleName: program.financialVehicleName,
						productFinancialVehicleId: program.id,
						programName: program.programName,
						userId: user.userId,
						userName: user.userName,
					});
				});
			});

			updateFinancialVehicleUserSettings(payload);
			selectedItems.value = [];
			fetchUsers();
			showModal.value = false;
		};

		watch(productsOptions, (newState, prevState) => {
			if (prevState.length !== 0) return;
			selectedProduct.value = newState[0].value;
			onSelectProduct(newState[0].value);
		});

		onMounted(() => {
			fetchProductsOptions();
			tabs.value = [
				...tabs.value,
				{
					name: 'Comunicações',
					path: COMUNICATIONS,
				},
			];
		});

		return {
			users,
			usersOptions,
			page,
			headers,
			isLoading,
			selectedUser,
			selectedProduct,
			productsOptions,
			selectedItems,
			isError,
			tabLimits,
			tabs,
			currentTab,
			LIMITS,
			showModal,
			modalTableItems,
			modalHeaders,
			updateTab,
			handleClickSettings,
			confirmConfiguration,

			onChangePage,
			onChangeLimitPerPage,
			onSelectUser,
			onInputSelectUser,
			fetchProductsOptions,
			fetchUsers,
			onSelectProduct,
		};
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#limits-table', 1, (0));
@include stickytable('#v-data-table--modal', 1, (0));
@import './BodyHome.scss';
</style>
