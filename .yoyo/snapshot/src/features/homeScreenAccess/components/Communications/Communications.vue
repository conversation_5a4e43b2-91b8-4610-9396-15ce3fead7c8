<template>
	<farm-box>
		<farm-row>
			<farm-col cols="3" md="2" lg="4">
				<div>
					<farm-label>Produto</farm-label>
					<farm-select-auto-complete
						v-model="selectedProduct"
						id="select_product"
						item-text="text"
						item-value="value"
						:items="productsOptions"
						@change="onSelectProduct"
					/>
				</div>
			</farm-col>

			<farm-col cols="3" md="2" lg="3">
				<farm-label for="select_user">Usuário </farm-label>
				<farm-select-auto-complete
					v-model="selectedUser"
					id="select_user"
					item-text="text"
					item-value="value"
					:items="usersOptions"
					@input="onInputSelectUser"
					@change="onSelectUser"
				/>
			</farm-col>

			<farm-col cols="6" md="4" lg="5" alignSelf="center">
				<div class="d-flex justify-end">
					<ConfirmButton
						:disabled="selectedItems.length === 0"
						:icon="true"
						customIcon="cog"
						@click="onClickCofinguration"
					>
						Configurar
					</ConfirmButton>
				</div>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isError" extra-decrease>
			<farm-box>
				<v-data-table
					hide-default-footer
					v-model="selectedItems"
					id="communication-table"
					:headers="headersCommunications"
					:items="communicationSettingsResult.items"
					:server-items-length="communicationSettingsResult.items.length"
					show-select
				>
					<template slot="no-data">
						<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" />
					</template>
					<template v-slot:footer>
						<farm-datatable-paginator
							class="mt-6 mb-n6"
							:initialLimitPerPage="5"
							:page="page"
							:totalPages="communicationSettingsResult.totalPages"
							@onChangePage="onChangePage"
							@onChangeLimitPerPage="onChangeLimitPerPage"
						/>
					</template>
				</v-data-table>
			</farm-box>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<ModalCommunications
			v-model="toggleModal"
			:showMessage="selectedItems.length > 1"
			:items="modalTableItems"
			@onClose="toggleModal = false"
			@onConfirm="onConfirmConfiguration"
		/>
	</farm-box>
</template>
<script>
import { defineComponent, onMounted } from 'vue';
import { headersCommunications } from '../../configurations/headers';
import { useCommunications } from '../../composables';
import ModalCommunications from '../ModalCommunications';

export default defineComponent({
	components: {
		ModalCommunications,
	},
	setup() {
		const {
			page,
			isLoading,
			isError,
			productsOptions,
			selectedProduct,
			selectedUser,
			communicationSettings,
			selectedItems,
			usersOptions,
			communicationSettingsResult,
			toggleModal,
			modalTableItems,
			fetchProductsOptions,
			onSelectProduct,
			onSelectUser,
			onInputSelectUser,
			onChangeLimitPerPage,
			onChangePage,
			onClickCofinguration,
			onConfirmConfiguration,
		} = useCommunications();

		onMounted(() => {
			fetchProductsOptions();
		});

		return {
			page,
			toggleModal,
			productsOptions,
			selectedProduct,
			selectedUser,
			isLoading,
			isError,
			communicationSettings,
			selectedItems,
			usersOptions,
			headersCommunications,
			communicationSettingsResult,
			modalTableItems,
			onSelectProduct,
			onSelectUser,
			onInputSelectUser,
			onChangeLimitPerPage,
			onChangePage,
			onClickCofinguration,
			onConfirmConfiguration,
		};
	},
});
</script>

<style lang="scss">
@import './Communications.scss';
</style>
