<template>
	<farm-container v-if="campaignDetail" class="campaign-detail-page">
		<header class="d-flex flex-column justify-space-between flex-sm-row">
			<div class="mb-2 mb-sm-0">
				<farm-caption variation="semiBold" tag="span">Campanha</farm-caption>:
				{{ campaignDetail.name }}
				<farm-chip
					:color="campaignDetail.status ? 'primary' : 'neutral'"
					dense
					class="ml-2"
				>
					{{ campaignDetail.status ? 'Ativa' : 'Inativa' }}
				</farm-chip>
			</div>
			<div v-if="campaignDetail.validity && campaignDetail.validity.length">
				<farm-caption variation="semiBold" tag="span">
					Vigência disponível para o usuário</farm-caption
				>: {{ defaultDateFormat(campaignDetail.validity[0]) }} até
				{{ defaultDateFormat(campaignDetail.validity[1]) }}
			</div>
		</header>

		<farm-line no-spacing class="my-6" />

		<CampaignCommercialProductDetailCard
			v-for="(commercialProduct, index) in campaignDetail.commercialProducts"
			:key="commercialProduct.id"
			:commercial-product="commercialProduct"
		>
			<template #line v-if="isLastCommercialProduct(index)">
				<farm-line no-spacing class="my-6" />
			</template>
		</CampaignCommercialProductDetailCard>

		<div class="mx-n6 mb-6">
			<farm-line />
		</div>

		<div class="d-flex align-center justify-end">
			<farm-btn :to="$router.resolve({ name: 'CampaignsList' }).href">Voltar</farm-btn>
		</div>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { useStore, useRouter } from '@/composibles';

import CampaignCommercialProductDetailCard from '../components/CampaignCommercialProductDetailCard';

import { useCampaign } from '../composables';

export default defineComponent({
	components: {
		CampaignCommercialProductDetailCard,
	},
	setup() {
		const store = useStore();
		const router = useRouter();
		const { campaignDetail, fetchCampaignDetail } = useCampaign();

		const isLastCommercialProduct = (index: number) =>	index < campaignDetail.value.commercialProducts.length - 1;
	
		onBeforeMount(() => {
			store.commit('campaigns/setCampaignDetail', null);
			fetchCampaignDetail(router.currentRoute.params.id);
		});

		return {
			campaignDetail,
			isLastCommercialProduct,
			defaultDateFormat,
		};
	},
});
</script>

<style lang="scss" scoped>
.campaign-detail-page {
	&:deep(.farm-chip.farm-chip--dense) {
		width: auto;
	}
}
</style>
