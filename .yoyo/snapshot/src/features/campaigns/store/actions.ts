import {
	RequestStatusEnum,
	errorBuilder,
	fetchDefaultParser,
	fetchDefaultParserPagination,
} from '@farm-investimentos/front-mfe-libs-ts';

import { getCommercialProducts, getFinancialVehicle } from '@/services/registerV3';
import campaignToAPI from '@/helpers/dtos/campaignToAPI';
import campaignFromAPI from '@/helpers/dtos/campaignFromAPI';

import {
	createOrUpdateCampaign as createOrUpdateCampaignService,
	getCampaignList as getCampaignListService,
	getCampaignDetail as getCampaignDetailService,
	getCampaignHistory as getCampaignHistoryService,
	getCampaignTimeline as getCampaignTimelineService,
} from '../services';

import type {
	GetCampaignListRequest,
	GetCampaignDetailRequest,
	GetCampaignHistoryRequest,
	GetFinancialVehiclesRequest,
	GetCommercialProductsRequest,
	CreateOrUpdateCampaignRequest,
	GetCampaignTimelineRequest,
} from '../services/types';
import type { Campaign } from '../types';

export default {
	async getCampaignList({ commit }, request: GetCampaignListRequest) {
		commit('setCampaignListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getCampaignListService(request);

			fetchDefaultParserPagination(commit, data, campaignFromAPI, 'CampaignList');
			commit('setCampaignListPageable', data.data.pageable);
		} catch (error) {
			commit('setCampaignListRequestStatus', errorBuilder(error));
		}
	},

	async getCampaignDetail({ commit }, request: GetCampaignDetailRequest) {
		commit('setCampaignDetailRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getCampaignDetailService(request);

			fetchDefaultParser(commit, data, campaignFromAPI, 'CampaignDetail');
		} catch (error) {
			commit('setCampaignDetailRequestStatus', errorBuilder(error));
		}
	},

	async getCampaignHistory({ commit }, request: GetCampaignHistoryRequest) {
		commit('setCampaignHistoryListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getCampaignHistoryService(request);

			fetchDefaultParserPagination(commit, data, campaignFromAPI, 'CampaignHistoryList');
			commit('setCampaignHistoryListPageable', data.data.pageable);
		} catch (error) {
			commit('setCampaignHistoryListRequestStatus', errorBuilder(error));
		}
	},

	async getCampaignTimeline({ commit }, request: GetCampaignTimelineRequest) {
		commit('setCampaignTimelineRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getCampaignTimelineService(request);

			commit('setCampaignTimeline', data.data.content);
			commit('setCampaignTimelineRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setCampaignTimelineRequestStatus', errorBuilder(error));
		}
	},

	async createOrUpdateCampaign({ commit }, request: CreateOrUpdateCampaignRequest) {
		commit('setCreateOrUpdateCampaignRequestStatus', RequestStatusEnum.START);
		try {
			request.payload = campaignToAPI(request.payload as Campaign);
			await createOrUpdateCampaignService(request);
			commit('setCreateOrUpdateCampaignRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setCreateOrUpdateCampaignRequestStatus', errorBuilder(error));
		}
	},

	async getVehicleList({ commit }, request: GetFinancialVehiclesRequest) {
		commit('setVehicleListRequestStatus', RequestStatusEnum.START);
		try {
			const response = await getFinancialVehicle(request.query);
			fetchDefaultParserPagination(commit, response, null, 'VehicleList');
		} catch (error) {
			commit('setVehicleListRequestStatus', errorBuilder(error));
		}
	},
	async getCommercialProductsList({ commit }, request: GetCommercialProductsRequest) {
		commit('setCommercialProductsListRequestStatus', RequestStatusEnum.START);
		try {
			const response = await getCommercialProducts(request.query);
			fetchDefaultParserPagination(commit, response, null, 'CommercialProductsList');
		} catch (error) {
			commit('setCommercialProductsListRequestStatus', errorBuilder(error));
		}
	},
};
