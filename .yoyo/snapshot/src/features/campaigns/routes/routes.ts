export default [
	{
		path: 'configuracoes/campanhas',
		name: 'CampaignsList',
		component: () => import('./../views/CampaignTabs.vue'),
		meta: {
			title: 'Campanhas',
			icon: 'text',
			roleKey: 'configuracoes.campanhas',
		},
	},
	{
		path: 'configuracoes/campanhas/nova',
		name: 'CampaignsCreate',
		component: () => import('./../views/Create.vue'),
		meta: {
			title: 'Nova Campanha',
			icon: 'plus',
			roleKey: 'configuracoes.campanhas',
		},
	},
	{
		path: 'configuracoes/campanhas/detalhe/:id(\\d+)',
		name: 'CampaignsDetail',
		component: () => import('./../views/Detail.vue'),
		meta: {
			title: 'Campanhas',
			icon: 'text',
			roleKey: 'configuracoes.campanhas',
		},
	},
	{
		path: 'configuracoes/campanhas/:id(\\d+)',
		name: 'CampaignsEdit',
		component: () => import('./../views/Edit.vue'),
		meta: {
			title: 'Editar Campanha',
			icon: 'pencil',
			roleKey: 'configuracoes.campanhas',
		},
	},
];
