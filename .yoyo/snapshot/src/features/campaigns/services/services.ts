import configurationClient from '@/configurations/services/configurations';
import type {
	CreateOrUpdateCampaignRequest,
	GetCampaignListRequest,
	GetCampaignDetailRequest,
	GetCampaignHistoryRequest,
	UpdateCampaignStatusRequest,
	GetCampaignTimelineRequest,
} from './types';

export const getCampaignList = (request: GetCampaignListRequest) => {
	return configurationClient.get('/v1/products/campaign/list', {
		params: request.query,
	});
};

export const getCampaignDetail = (request: GetCampaignDetailRequest) => {
	return configurationClient.get(`/v1/products/campaign/detail/${request.params.id}`);
};

export const getCampaignHistory = (request: GetCampaignHistoryRequest) => {
	return configurationClient.get('/v1/products/campaign/history', {
		params: request.query,
	});
};

export const getCampaignTimeline = (request: GetCampaignTimelineRequest) => {
	return configurationClient.get('/v1/products/campaign/dashboard', {
		params: request.query,
		paramsSerializer: params => {
			const searchParams = new URLSearchParams();

			Object.entries(params).forEach(([key, value]) => {
				if (Array.isArray(value)) {
					value.forEach(item => {
						if (item !== null && item !== undefined) {
							searchParams.append(key, item.toString());
						}
					});
				} else if (value !== null && value !== undefined) {
					searchParams.append(key, value.toString());
				}
			});

			return searchParams.toString();
		},
	});
};

export const createOrUpdateCampaign = (request: CreateOrUpdateCampaignRequest) => {
	return configurationClient.post('/v1/products/campaign/save', request.payload);
};

export const updateCampaignStatus = (request: UpdateCampaignStatusRequest) => {
	return configurationClient.patch(`/v1/campaign/${request.params.campaignId}`, request.payload);
};
