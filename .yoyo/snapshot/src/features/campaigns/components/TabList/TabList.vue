<template>
	<farm-box>
		<farm-form class="mb-6" ref="filterFormComponent">
			<farm-row>
				<farm-col md="6">
					<farm-form-mainfilter
						label="Buscar Campanha ou Produto Comercial"
						:show-filters="isShowingFilters"
						@onClick="toggleFilters"
						@onInputChange="updateNameFilter"
					/>
				</farm-col>

				<farm-col class="d-md-flex justify-end mt-7" md="6">
					<farm-btn :to="$router.resolve({ name: 'CampaignsCreate' }).href">
						<farm-icon>plus</farm-icon>
						Nova campanha
					</farm-btn>
				</farm-col>
			</farm-row>
			<farm-row v-show="isShowingFilters">
				<farm-col cols="12" md="3">
					<farm-label>Status Campanha</farm-label>
					<farm-select v-model="formFilters.campaignStatus" :items="FORM_STATUSES" />
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label>Status Produto Comercials</farm-label>
					<farm-select
						v-model="formFilters.campaignCommercialProductStatus"
						:items="FORM_STATUSES"
					/>
				</farm-col>

				<farm-col cols="12">
					<farm-btn outlined @click="updateListFilters"> Aplicar Filtros </farm-btn>
					<farm-btn plain @click="clearFilters">Limpar Filtros</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>

		<CampaignListCard
			v-for="(campaign, index) in campaignList"
			:key="campaign.id"
			:campaign="campaign"
			:class="{ 'mb-4': index !== campaignList.length - 1 }"
			@open-modal="toggleCampaignModal"
		/>

		<farm-datatable-paginator
			v-if="campaignListPageable"
			class="mt-12"
			:has-gutter="false"
			:page="presentableFilterValues.pageNumber"
			:total-pages="filters.totalPages"
			:initial-limit-per-page="filters.pageSize"
			@onChangePage="onChangePage"
			@onChangeLimitPerPage="onChangePageLimit"
		/>

		<CampaignHistoryModal
			v-if="campaignModals.history.status"
			:value="campaignModals.history.status"
			@on-close="toggleCampaignModal('history')"
		/>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, defineAsyncComponent, onBeforeMount, computed, watch } from 'vue';
import { useIsLoading } from '@/composibles';

import { usePageable } from '@/features/pricing/composables';

const CampaignHistoryModal = defineAsyncComponent(
	() => import('../CampaignHistoryModal')
);
import CampaignListCard from '../CampaignListCard';
import { useCampaign, useCampaignListFilters } from '../../composables';
import { FORM_STATUSES } from '../../constants';

export default defineComponent({
	components: {
		CampaignHistoryModal,
		CampaignListCard,
	},
	setup() {
		const {
			campaignModals,
			campaignList,
			campaignListPageable,
			campaignListRequestStatus,
			campaignDetailRequestStatus,
			campaignHistoryListRequestStatus,
			fetchCampaignList,
			toggleCampaignModal,
		} = useCampaign();
		const {
			formFilters,
			campaignFilters,
			filterFormComponent,
			isShowingFilters,
			toggleFilters,
		} = useCampaignListFilters();

		const allFilters = computed(() => {
			return new URLSearchParams({
				page: filters.value.pageNumber.toString(),
				limit: filters.value.pageSize.toString(),
				...campaignFilters.value,
			});
		});

		const isLoading = useIsLoading([
			campaignListRequestStatus,
			campaignDetailRequestStatus,
			campaignHistoryListRequestStatus,
		]);

		const {
			filters,
			presentableFilterValues,
			setPageableFilters,
			onChangePage,
			onChangePageLimit,
		} = usePageable(filters => {
			filters.delete('orderby');

			fetchCampaignList({
				...Object.fromEntries(allFilters.value),
				...Object.fromEntries(filters),
			});
		}, campaignListPageable);

		const updateListFilters = () => {
			fetchCampaignList(allFilters.value);
		};
		const updateNameFilter = (value: string) => {
			formFilters.value.busca = value;

			fetchCampaignList(allFilters.value);
		};
		const clearFilters = () => {
			formFilters.value.busca = '';
			filterFormComponent.value.reset();
			fetchCampaignList(allFilters.value);
		};

		onBeforeMount(() => {
			fetchCampaignList();
		});

		watch(campaignListPageable, newValue => {
			setPageableFilters(newValue);
		});

		return {
			FORM_STATUSES,
			campaignList,
			campaignListPageable,
			campaignModals,
			formFilters,
			filterFormComponent,
			isLoading,
			isShowingFilters,
			filters,
			presentableFilterValues,
			updateListFilters,
			onChangePage,
			onChangePageLimit,
			toggleCampaignModal,
			updateNameFilter,
			fetchCampaignList,
			clearFilters,
			toggleFilters,
		};
	},
});
</script>