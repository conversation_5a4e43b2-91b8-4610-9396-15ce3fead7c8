<template>
	<farm-card tag="article" class="campaign-commercial-product-list-card">
		<header class="d-flex justify-space-between align-center pa-4">
			<div>
				<div class="d-flex mb-2">
					<farm-bodytext variation="bold">{{ campaign.name }}</farm-bodytext>
					<farm-chip dense class="ml-2" :color="campaign.status ? 'success' : 'neutral'">
						{{ campaign.status ? 'Ativa' : 'Inativa' }}
					</farm-chip>
				</div>
				<farm-caption color="black" color-variation="30">
					Vigência disponível para o usuário:
					{{ defaultDateFormat(campaign.validity[0]) }} até
					{{ defaultDateFormat(campaign.validity[1]) }}
				</farm-caption>
			</div>

			<farm-contextmenu v-model="isContextMenuOpen">
				<farm-list class="commercial-product-list-card-context-menu">
					<farm-listitem class="commercial-product-list-card-context-menu__list-item">
						<farm-btn
							plain
							color="black"
							variation="50"
							:to="
								$router.resolve({
									name: 'CampaignsDetail',
									params: { id: campaign.id },
								}).href
							"
						>
							<farm-icon size="16" color="black" variation="50">
								open-in-new
							</farm-icon>
							<farm-caption color="black" color-variation="50">
								Ver detalhes
							</farm-caption>
						</farm-btn>
					</farm-listitem>
					<farm-listitem v-if="canWrite">
						<farm-btn
							plain
							class="commercial-product-list-card-context-menu__button"
							color="black"
							variation="50"
							:to="
								$router.resolve({
									name: 'CampaignsEdit',
									params: { id: campaign.id },
								}).href
							"
						>
							<farm-icon size="16" color="black" variation="50">
								pencil-outline
							</farm-icon>
							<farm-caption color="black" color-variation="50">
								Editar Campanha
							</farm-caption>
						</farm-btn>
					</farm-listitem>
					<farm-listitem>
						<farm-btn
							plain
							class="commercial-product-list-card-context-menu__button"
							color="black"
							variation="50"
							@click="$emit('open-modal', 'history', campaign.id)"
						>
							<farm-icon size="16" color="black" variation="50"> history </farm-icon>
							<farm-caption color="black" color-variation="50">
								Histórico
							</farm-caption>
						</farm-btn>
					</farm-listitem>
				</farm-list>

				<template v-slot:activator="{ on, attrs }">
					<farm-btn icon v-bind="attrs" v-on="on">
						<farm-icon>dots-horizontal</farm-icon>
					</farm-btn>
				</template>
			</farm-contextmenu>
		</header>

		<farm-line no-spacing />

		<div class="pa-4">
			<DashedCard
				v-for="(commercialProduct, index) in campaign.commercialProducts"
				class="pa-4"
				ref="DOMCommercialProductCard"
				:key="commercialProduct.id"
				:class="{ 'mb-4': index !== campaign.commercialProducts.length - 1 }"
			>
				<div class="dashed-card__item">
					<farm-idcaption copy-text="">
						<template v-slot:title>Produto Comercial</template>
						<template v-slot:subtitle>
							{{ commercialProduct.name }}
						</template>
					</farm-idcaption>
				</div>
				<div class="dashed-card__item">
					<farm-idcaption copy-text="">
						<template v-slot:title>Taxa Cliente (a.m.)</template>
						<template v-slot:subtitle>{{ commercialProduct.baseTax }}%</template>
					</farm-idcaption>
				</div>
				<div class="dashed-card__item">
					<farm-idcaption copy-text="">
						<template v-slot:title>Taxa Fundo (a.m.)</template>
						<template v-slot:subtitle
							>{{ commercialProduct.commercialProductTax }}%</template
						>
					</farm-idcaption>
				</div>
				<div class="dashed-card__item">
					<farm-idcaption copy-text="">
						<template v-slot:title>Vigência disponível para o usuário</template>
						<template v-slot:subtitle>
							{{ defaultDateFormat(commercialProduct.validity[0]) }} até
							{{ defaultDateFormat(commercialProduct.validity[1]) }}
						</template>
					</farm-idcaption>
				</div>

				<template #right>
					<farm-chip
						dense
						class="ml-2"
						:color="commercialProduct.status ? 'success' : 'neutral'"
					>
						{{ commercialProduct.status ? 'Ativa' : 'Inativa' }}
					</farm-chip>
				</template>
			</DashedCard>
		</div>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import DashedCard from '@/components/DashedCard';

import { useCampaign } from '../../composables';

import type { Campaign } from '../../types';
import type { PropType } from 'vue';

export default defineComponent({
	components: {
		DashedCard,
	},
	props: {
		campaign: {
			type: Object as PropType<Campaign>,
			required: true,
		},
	},
	setup() {
		const DOMCommercialProductCard = ref([]);
		const { toggleCampaignModal } = useCampaign();

		const isContextMenuOpen = ref(false);

		const toggleContextMenu = () => (isContextMenuOpen.value = !isContextMenuOpen.value);
		const setWidthByWiderItem = (collection: any[], selector: string) => {
			if (collection.length <= 0) return;

			const nodes = collection.map(({ $el }) => $el.querySelector(selector));

			if (nodes.length === 1) return;

			const widerItemWidth = nodes.reduce((accumulator, node) => {
				if (!node) {
					return accumulator;
				}

				const { width } = node.getBoundingClientRect();

				return (accumulator = width >= accumulator ? width : accumulator);
			}, 0);

			nodes.forEach(node => {
				if (node) {
					node.style.minWidth = `${widerItemWidth}px`;
				}
			});
		};

		onMounted(() => {
			setWidthByWiderItem(
				Array.from(DOMCommercialProductCard.value),
				'.campaign-dashed-card--left .campaign-dashed-card__item:first-of-type'
			);
		});

		return {
			DOMCommercialProductCard,
			isContextMenuOpen,
			toggleContextMenu,
			defaultDateFormat,
			toggleCampaignModal,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './CampaignListCard.scss';
</style>
