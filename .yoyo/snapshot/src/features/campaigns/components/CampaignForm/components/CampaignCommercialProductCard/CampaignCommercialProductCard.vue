<template>
	<farm-card class="campaign-commercial-product-creation-card pa-4" tag="fieldset">
		<farm-row class="mb-4">
			<farm-col cols="6">
				<farm-subtitle tag="h5"> {{ title }} </farm-subtitle>
			</farm-col>
			<farm-col v-if="hasDeleteButton" cols="6" class="d-flex flex-column align-end">
				<button type="button" @click="$emit('remove-commercial-product-card')">
					<farm-icon>delete-outline</farm-icon>
				</button>
			</farm-col>
		</farm-row>

		<farm-line class="mb-4" />

		<farm-row>
			<farm-col cols="12" md="3" lg="3">
				<farm-label :for="`commercial-product-id-${id}`" required>Produto Comercial</farm-label>
				<farm-select
					v-model="commercialProduct.id"
					:id="`commercial-product-id-${id}`"
					:items="commercialProductsToBeSelected"
					:rules="[rules.required, rules.uniqueCommercialProduct]"
					@input="updateCommercialProduct"
				/>
			</farm-col>
			<farm-col cols="12" md="3" lg="3">
				<farm-row>
					<farm-col cols="12" sm="6">
						<farm-label :for="`commercial-product-base-tax-${id}`" required
							>Taxa Cliente (a.m.)</farm-label
						>
						<farm-textfield-v2
							v-model="commercialProduct.baseTax"
							mask="#.####%"
							:id="`commercial-product-base-tax-${id}`"
							:rules="[rules.required]"
						/>
					</farm-col>
					<farm-col cols="12" sm="6">
						<farm-label :for="`commercial-product-base-tax-${id}`" required
							>Taxa Fundo (a.m.)</farm-label
						>
						<farm-textfield-v2
							v-model="commercialProduct.commercialProductTax"
							mask="#.####%"
							:id="`commercial-product-base-tax-${id}`"
							:rules="[rules.required]"
						/>
					</farm-col>
				</farm-row>
			</farm-col>
			<farm-col cols="12" md="3" lg="3">
				<farm-label :for="`commercial-product-validity-${id}`" required
					>Vigência disponível para o usuário</farm-label
				>
				<farm-input-rangedatepicker
					v-model="commercialProduct.validity"
					required
					:input-id="`commercial-product-validity-${id}`"
					:min="campaignValidityStart"
					:max="campaignValidityEnd"
					:disabled="!hasValidCampaignValidity"
					:rules="[rules.required]"
					:validate-input="true"
					out-of-range-message="O período selecionado precisa estar no range da vigência da campanha"
				/>
			</farm-col>
			<farm-col cols="12" md="3" lg="3" align-self="center">
				<farm-label :for="`commercial-product-status-${id}`" required>Status do produto</farm-label>
				<farm-switcher v-model="commercialProduct.status" :id="`commercial-product-status-${id}`" />
			</farm-col>
		</farm-row>

		<CampaignOperationRow
			v-for="(operation, index) in commercialProduct.operations"
			:key="index"
			:commercialProductId="id"
			:operations="operationsToBeSelected"
			:operation="operation"
			:index="index"
		>
			<farm-col cols="12" md="4" lg="3" align-self="end">
				<farm-btn
					v-if="hasMoreOperationsToAdd && isLastOperation(index)"
					plain
					class="mb-7 ml-n2 pa-0"
					@click="addOperationRow"
				>
					<farm-icon>plus</farm-icon> Operação
				</farm-btn>
				<farm-btn
					v-else
					plain
					class="remove-operation-btn mb-7 ml-n2 pa-0"
					@click="removeOperationRow(index)"
				>
					<farm-icon>delete-outline</farm-icon>
				</farm-btn>
			</farm-col>
		</CampaignOperationRow>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

import { OPERATION_TYPES } from '@/constants';

import CampaignOperationRow from '../CampaignOperationRow';

import { DEFAULT_OPERATION_STRUCTURE, DEFAULT_COMMERCIAL_PRODUCT_CARD_STRUCTURE } from '../../constants';
import { useCampaignForm } from '../../composables';

import { useCampaign } from '../../../../composables';

import type { PropType } from 'vue';

export default defineComponent({
	components: {
		CampaignOperationRow,
	},
	props: {
		id: {
			type: Number,
			required: true,
		},
		commercialProduct: {
			type: Object as PropType<typeof DEFAULT_COMMERCIAL_PRODUCT_CARD_STRUCTURE>,
			required: true,
		},
		title: {
			type: String,
			required: true,
		},
		hasDeleteButton: {
			type: Boolean,
			default: true,
		},
		campaignValidity: {
			type: Array as PropType<string[]>,
			default: () => [],
		},
	},
	setup(props) {
		const { commercialProductsList } = useCampaign();
		const { formComponent, rules, commercialProductsToBeSelected } = useCampaignForm();

		const selectedOperations = computed(() =>
			props.commercialProduct.operations.flatMap(({ id }) => id || [])
		);
		const hasMoreOperationsToAdd = computed(
			() => props.commercialProduct.operations.length < OPERATION_TYPES.length
		);
		const operationsToBeSelected = computed(() =>
			OPERATION_TYPES.map(({ id, name }) => ({
				value: id,
				text: name,
				disabled: selectedOperations.value.includes(id),
			}))
		);

		const addOperationRow = () => {
			props.commercialProduct.operations.push({ ...DEFAULT_OPERATION_STRUCTURE });
			formComponent.value.restart();
		};
		const removeOperationRow = (index: number) => {
			props.commercialProduct.operations.splice(index, 1);
			formComponent.value.restart();
		};
		const isLastOperation = (index: number) => {
			return index === props.commercialProduct.operations.length - 1;
		};
		const updateCommercialProduct = commercialProductId => {
			const commercialProduct = commercialProductsList.value.find(commercialProduct => commercialProduct.id === commercialProductId);
			props.commercialProduct.name = commercialProduct?.name || null;
		};

		const campaignValidityStart = computed(() => props.campaignValidity && props.campaignValidity[0]);
		const campaignValidityEnd = computed(() => props.campaignValidity && props.campaignValidity[1]);
		const hasValidCampaignValidity = computed(() => Array.isArray(props.campaignValidity) && props.campaignValidity.length > 0);

		return {
			commercialProductsToBeSelected,
			rules,
			hasMoreOperationsToAdd,
			selectedOperations,
			operationsToBeSelected,
			updateCommercialProduct,
			isLastOperation,
			removeOperationRow,
			addOperationRow,
			campaignValidityStart,
			campaignValidityEnd,
			hasValidCampaignValidity,
		};
	},
});
</script>

<style lang="scss">
@import 'CampaignCommercialProductCard';
</style>
