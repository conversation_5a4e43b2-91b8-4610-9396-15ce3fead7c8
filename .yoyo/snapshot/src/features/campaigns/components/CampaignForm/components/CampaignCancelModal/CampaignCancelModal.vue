<template>
	<farm-modal size="sm" :value="value" :offset-top="48" :offset-bottom="80">
		<template #header>
			<farm-dialog-header title="Tem certeza que deseja cancelar?" :has-close-icon="false" />
		</template>
		<template #content>
			<farm-bodytext type="2">
				<PERSON><PERSON> cancelar, os dados preenchidos serão perdidos.
			</farm-bodytext>
		</template>
		<template #footer>
			<farm-line no-spacing />
			<div class="d-flex justify-end pa-4">
				<farm-btn outlined class="mr-2" @click="$emit('on-close')">Voltar</farm-btn>
				<farm-btn :to="$router.resolve({ name: 'CampaignsList' }).href">Cancelar</farm-btn>
			</div>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup() {
		return {};
	},
});
</script>
