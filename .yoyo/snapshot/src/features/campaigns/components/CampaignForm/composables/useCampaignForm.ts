import { computed, ref } from 'vue';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { useGetter, useStore, useRouter } from '@/composibles';

import type { Campaign } from '../../../types';
import type { CreateOrUpdateCampaignRequest } from '../../../services/types';

const formComponent = ref();
const form = ref<Campaign>();
const isValidForm = ref(false);

export default function useCampaignForm(campaignForm?: Campaign) {
	if (campaignForm) {
		form.value = campaignForm;

	}

	const store = useStore();
	const router = useRouter();

	const isCancellingOperation = ref(false);


	const commercialProductsList = computed(useGetter('campaigns', 'commercialProductsList'));


	const createOrUpdateCampaignRequestStatus = computed(
		useGetter('campaigns', 'createOrUpdateCampaignRequestStatus')
	);
	const selectedCommercialProducts = computed(() =>
		form.value.commercialProducts.flatMap(commercialProduct => commercialProduct.id || [])
	);

	const duplicatedSelectedCommercialProducts = computed(() =>
		selectedCommercialProducts.value.filter(
			(item, index) => selectedCommercialProducts.value.indexOf(item) !== index
		)
	);
	const hasMoreCommercialProductsToAdd = computed( () => form.value.commercialProducts.length < commercialProductsList.value?.length);


	const commercialProductsToBeSelected = computed(() =>
		commercialProductsList.value.map(({ id, name }) => ({
			value: id,
			text: name,
			disabled: selectedCommercialProducts.value.includes(id),
		}))
	);
	const isEditPage = computed(() => router.currentRoute.name === 'CampaignsEdit');

	const rules = {
		required: (value: any) => !!value || 'Campo obrigatório',
		uniqueCommercialProduct: (value: any) =>
			!duplicatedSelectedCommercialProducts.value.includes(value) ||
			'Produto Comercial já cadastrado.',
	};
	const createOrUpdateCampaign = async dialog => {
		const request: CreateOrUpdateCampaignRequest = {
			payload: form.value,
		};
		await store.dispatch('campaigns/createOrUpdateCampaign', request);

		if (createOrUpdateCampaignRequestStatus.value === RequestStatusEnum.SUCCESS) {
			setTimeout(() => {
				router.push({ name: 'CampaignsList' });
				store.commit(
					'campaigns/setCreateOrUpdateCampaignRequestStatus',
					RequestStatusEnum.IDLE
				);
			}, 3000);

			return;
		}

		dialog.alert(
			{
				title: 'Erro',
				body: createOrUpdateCampaignRequestStatus.value.message,
			},
			{
				html: true,
				okText: 'Voltar',
			}
		);
	};
	const toggleCancelModal = () => {
		isCancellingOperation.value = !isCancellingOperation.value;
	};



	return {
		form,
		formComponent,
		rules,
		commercialProductsList,
		isValidForm,
		selectedCommercialProducts,
		hasMoreCommercialProductsToAdd,
		isEditPage,
		isCancellingOperation,
		commercialProductsToBeSelected,
		createOrUpdateCampaign,
		toggleCancelModal,
	};
}
