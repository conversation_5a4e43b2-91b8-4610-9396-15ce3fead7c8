import type { Campaign, CampaignOperation, CampaignCommercialProduct } from '../../types';

export const DEFAULT_OPERATION_STRUCTURE: CampaignOperation = {
	baseId: null,
	id: null,
	interval: null,
	disbursement: null,
};

export const DEFAULT_COMMERCIAL_PRODUCT_CARD_STRUCTURE: CampaignCommercialProduct = {
	baseId: null,
	id: null,
	baseTax: null,
	commercialProductTax: null,
	validity: null,
	status: true,
	operations: [JSON.parse(JSON.stringify(DEFAULT_OPERATION_STRUCTURE))],
};
const FORM: Campaign = {
	name: '',
	validity: null,
	status: true,
	updatedAt: '',
	updatedBy: '',
	updatedByName: '',
	commercialProducts: [JSON.parse(JSON.stringify(DEFAULT_COMMERCIAL_PRODUCT_CARD_STRUCTURE))] as CampaignCommercialProduct[],
};

export const DEFAULT_FORM_STRUCTURE = JSON.parse(JSON.stringify(FORM)) as Campaign;
