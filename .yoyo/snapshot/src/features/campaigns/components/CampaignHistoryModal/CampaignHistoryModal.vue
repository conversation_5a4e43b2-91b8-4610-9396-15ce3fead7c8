<template>
	<farm-modal
		class="campaign-history-modal"
		:value="value"
		:offsetTop="52"
		:offsetBottom="69"
		@input="$emit('on-close')"
	>
		<template v-slot:header>
			<farm-dialog-header title="Histórico" @onClose="$emit('on-close')" />
		</template>

		<template v-slot:content v-if="campaignHistoryList.length">
			<farm-collapsible
				v-for="campaign in campaignHistoryList"
				:key="`${campaign.updatedAt}-${campaign.updatedByName}`"
				class="mb-4 pa-2"
				title=""
				custom
			>
				<template #custom>
					<farm-caption color="default" color-variation="lighten">
						Atualização feita por <b>{{ campaign.updatedByName }}</b
						>, dia <b>{{ defaultDateFormat(campaign.updatedAt) }}</b> às
						<b>{{ formatHour(campaign.updatedAt) }}</b>
					</farm-caption>
				</template>

				<farm-line no-spacing class="my-4" />

				<div class="d-flex flex-column justify-space-between flex-sm-row">
					<div class="mb-2 mb-sm-0">
						<farm-caption variation="semiBold" tag="span">Campanha</farm-caption>:
						{{ campaign.name }}
						<farm-chip
							class="ml-2"
							dense
							:color="campaign.status ? 'primary' : 'neutral'"
						>
							{{ campaign.status ? 'Ativa' : 'Inativa' }}
						</farm-chip>
					</div>
					<div>
						<farm-caption variation="semiBold" tag="span"
							>Vigência disponível para o usuário</farm-caption
						>: {{ defaultDateFormat(campaign.validity[0]) || 'Não aferido' }} até
						{{ defaultDateFormat(campaign.validity[1]) || 'Não aferido' }}
					</div>
				</div>

				<farm-line no-spacing class="my-4" />

				<CampaignCommercialProductDetailCard
					v-for="(commercialProduct, index) in campaign.commercialProducts"
					:key="commercialProduct.id"
					:commercialProduct="commercialProduct"
					:last-operation-has-margin="false"
				>
					<template
						#line
						v-if="isLastCommercialProduct(campaign.commercialProducts, index)"
					>
						<farm-line no-spacing class="my-6" />
					</template>
				</CampaignCommercialProductDetailCard>
			</farm-collapsible>

			<farm-datatable-paginator
				v-if="campaignHistoryListPageable"
				class="mt-4"
				:has-gutter="false"
				:page="presentableFilterValues.pageNumber"
				:total-pages="filters.totalPages"
				:initial-limit-per-page="filters.pageSize"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
			/>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				confirm-label="Voltar"
				:has-cancel="false"
				@onConfirm="$emit('on-close')"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, watch } from 'vue';

import { defaultDateFormat, formatHour } from '@farm-investimentos/front-mfe-libs-ts';

import CampaignCommercialProductDetailCard from '../CampaignCommercialProductDetailCard';

import { useCampaign } from '../../composables';
import { usePageable } from '@/features/pricing/composables';

export default defineComponent({
	components: {
		CampaignCommercialProductDetailCard,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup() {
		const {
			campaignHistoryList,
			campaignHistoryListPageable,
			campaignModals,
			fetchCampaignHistoryList,
		} = useCampaign();

		const isLastCommercialProduct = (commercialProducts: any[], index: number) => {
			return index < commercialProducts.length - 1;
		};

		const {
			filters,
			presentableFilterValues,
			setPageableFilters,
			onChangePage,
			onChangePageLimit,
		} = usePageable(filters => {
			filters.delete('orderby');
			fetchCampaignHistoryList(campaignModals.value.history.id, filters);
		}, campaignHistoryListPageable);

		watch(campaignHistoryListPageable, newValue => {
			setPageableFilters(newValue);
		});

		onBeforeMount(() => fetchCampaignHistoryList(campaignModals.value.history.id));

		return {
			campaignHistoryList,
			filters,
			presentableFilterValues,
			campaignHistoryListPageable,
			onChangePage,
			onChangePageLimit,
			isLastCommercialProduct,
			defaultDateFormat,
			formatHour,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './CampaignHistoryModal.scss';
</style>
