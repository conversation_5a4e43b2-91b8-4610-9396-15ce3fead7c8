<template>
	<farm-box>
		<farm-row no-default-gutters class="main-layout">
			<farm-col cols="3" class="campaigns-sidebar">
				<farm-bodytext type="1" variation="bold" color="primary" class="mb-4">
					Campanhas Filtradas
				</farm-bodytext>
				<div class="campaign-card">
					<farm-typography size="md" weight="500" >
						Texto do retângulo
					</farm-typography>
					<farm-switcher
						:value="true"
						@input="(value) => console.log('Switcher changed:', value)"
					/>
				</div>
			</farm-col>

			<farm-col cols="9" no-gutters class="content-area">
				<farm-row no-default-gutters class="content-layout">
					<farm-col cols="12" class="filters-section">
						<farm-bodytext type="1" variation="bold"  class="mb-4">
							Preencha os campos abaixo para popular a timeline:
						</farm-bodytext>

						<div class="filters-container">
							<div class="filter-group commercial-product-group">
								<farm-label for="commercial-product">Produto Comercial</farm-label>
								<farm-select
									id="commercial-product"
									class="commercial-product-select"
									v-model="formFilters.commercial_prd_id"
									item-text="text"
									item-value="value"
									:items="commercialProductsForSelect"
									placeholder="Selecionar produto"
								/>
							</div>
							<div class="filter-group">
								<farm-label for="operation-type">Tipo de Operação</farm-label>
								<farm-select
									id="operation-type"
									v-model="formFilters.operation_type"
									item-text="text"
									item-value="value"
									:items="operationTypesForSelect"
									placeholder="Selecionar operação"
								/>
							</div>
							<div class="filter-group">
								<farm-label for="campaign-status">Status da Campanha</farm-label>
								<farm-select
									id="campaign-status"
									v-model="formFilters.campaign_status"
									item-text="text"
									item-value="value"
									:items="FORM_STATUSES"
									placeholder="Selecionar status"
								/>
							</div>
							<div class="buttons-group">
								<farm-btn outlined @click="aplicarFiltros">
									Aplicar Filtros
								</farm-btn>
								<farm-btn plain @click="limparFiltros">
									Limpar Filtros
								</farm-btn>
							</div>
						</div>
					</farm-col>

					<farm-col cols="12" class="chart-section">
			
						<div v-if="isLoading" class="chart-loading">
							<farm-typography>Carregando dados do gráfico...</farm-typography>
						</div>

						<div v-else-if="!campaignTimeline || campaignTimeline.length === 0" class="chart-empty">
							<farm-typography color="black" color-variation="50">
								Nenhum dado encontrado. Aplique os filtros para visualizar o gráfico.
							</farm-typography>
						</div>

						<div v-else class="chart-container">
							<farm-gantt-chart :data="ganttChartData" />
						</div>
					</farm-col>
				</farm-row>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onBeforeMount, computed } from 'vue';
import { Switcher } from '@farm-investimentos/front-mfe-components/src/components/Switcher';
import { GanttChart } from '@farm-investimentos/front-mfe-components/src/components/GanttChart';
import { useIsLoading } from '@/composibles';
import { OPERATION_TYPES } from '@/constants';
import { useCampaign } from '../../composables';
import { FORM_STATUSES } from '../../constants';
import type { CampaignTimelineItem } from '../../types';
import type { GanttData, GanttGroup, GanttBar } from '@farm-investimentos/front-mfe-components/src/components/GanttChart/types';

interface CampaignData {
	id: number;
	name: string;
	isActive: boolean;
}

export default defineComponent({
	name: 'TabCustomFilter',
	components: {
		'farm-switcher': Switcher,
		'farm-gantt-chart': GanttChart
	},
	setup() {
		const {
			campaignTimeline,
			campaignTimelineRequestStatus,
			commercialProductsList,
			commercialProductsListRequestStatus,
			fetchCommercialProducts,
			fetchCampaignTimeline,
		} = useCampaign();


		const campaigns = ref<CampaignData[]>([
			{ id: 1, name: 'Campanha Black Friday', isActive: true },
			{ id: 2, name: 'Campanha Natal 2024', isActive: false },
			{ id: 3, name: 'Campanha Ano Novo', isActive: true },
			{ id: 4, name: 'Campanha Carnaval', isActive: false }
		]);

		const formFilters = ref({
			commercial_prd_id: null,
			operation_type: null,
			campaign_status: null,
		});

		const isLoading = useIsLoading([
			campaignTimelineRequestStatus,
			commercialProductsListRequestStatus,
		]);

		/**
		 * Transform campaign timeline data into GanttChart format
		 * Creates 4 bars per campaign group as specified in requirements
		 */
		const transformCampaignTimelineToGanttData = (timelineData: CampaignTimelineItem[]): GanttData => {
			if (!timelineData || timelineData.length === 0) {
				return { groups: [] };
			}

			const groups: GanttGroup[] = timelineData.map((item) => {
				const { campaign, commercialProduct } = item;

				const bars: GanttBar[] = [
					{
						id: `campaign-${campaign.id}-validity`,
						label: 'Vigência da Campanha',
						start: campaign.startDate,
						end: campaign.endDate,
						color: '#7BC4F7',
						tooltipData: {
							'Taxa': `${campaign.tax}%`,
							'Status': campaign.status ? 'Ativo' : 'Inativo',
							'Período': `${campaign.startDate} a ${campaign.endDate}`
						}
					},
					{
						id: `campaign-${campaign.id}-product-${commercialProduct.id}-validity`,
						label: 'Vigência do Produto Comercial',
						start: commercialProduct.startDate,
						end: commercialProduct.endDate,
						color: '#8BB455',
						tooltipData: {
							'Produto': commercialProduct.name,
							'Status': commercialProduct.status ? 'Ativo' : 'Inativo',
							'Período': `${commercialProduct.startDate} a ${commercialProduct.endDate}`
						}
					},
					{
						id: `campaign-${campaign.id}-product-${commercialProduct.id}-disbursement`,
						label: 'Período de Desembolso',
						start: commercialProduct.startDisbursementDate,
						end: commercialProduct.endDisbursementDate,
						color: '#FFB84D',
						tooltipData: {
							'Período': `${commercialProduct.startDisbursementDate} a ${commercialProduct.endDisbursementDate}`
						}
					},
					{
						id: `campaign-${campaign.id}-product-${commercialProduct.id}-due`,
						label: 'Intervalo Vencimento',
						start: commercialProduct.startDueDate,
						end: commercialProduct.endDueDate,
						color: '#F7857F',
						tooltipData: {
							'Período': `${commercialProduct.startDueDate} a ${commercialProduct.endDueDate}`
						}
					}
				];

				return {
					title: campaign.name,
					bars
				};
			});

			return { groups };
		};


		const commercialProductsForSelect = computed(() =>
			commercialProductsList.value
				?.filter(product => product.enabled === 1)
				?.map(({ id, name }) => ({
					value: id,
					text: name
				})) || []
		);


		const operationTypesForSelect = computed(() =>
			OPERATION_TYPES.map(({ id, name }) => ({
				value: id,
				text: name
			}))
		);


		const isFormValid = computed(() => {
			return formFilters.value.commercial_prd_id !== null &&
			formFilters.value.operation_type !== null &&
			formFilters.value.campaign_status !== null;
		});

		const ganttChartData = computed(() => {
			return transformCampaignTimelineToGanttData(campaignTimeline.value || []);
		});

		const onToggleCampaign = (campaignId: number, value: boolean) => {
			const campaign = campaigns.value.find(c => c.id === campaignId);
			if (campaign) {
				campaign.isActive = value;
			}
		};

		const aplicarFiltros = () => {
			const query = {
				commercial_prd_id: formFilters.value.commercial_prd_id,
				operation_type: formFilters.value.operation_type,
				campaign_status: formFilters.value.campaign_status,
			};

			fetchCampaignTimeline({ query });
		};

		const limparFiltros = () => {
			formFilters.value = {
				commercial_prd_id: null,
				operation_type: null,
				campaign_status: [],
			};
		};


		onBeforeMount(() => {
			fetchCommercialProducts();
		});

		return {
			campaigns,
			formFilters,
			campaignTimeline,
			campaignTimelineRequestStatus,
			commercialProductsList,
			commercialProductsForSelect,
			operationTypesForSelect,
			FORM_STATUSES,
			isFormValid,
			isLoading,
			ganttChartData,
			onToggleCampaign,
			aplicarFiltros,
			limparFiltros,
		};
	}
});
</script>

<style lang="scss" scoped>
@import './TabCustomFilter.scss';
</style>

