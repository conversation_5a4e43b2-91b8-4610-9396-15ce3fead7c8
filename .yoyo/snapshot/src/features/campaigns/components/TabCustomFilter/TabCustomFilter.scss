
.main-layout {
	height: 700px; /* Altura fixa para visualização */
	margin: -24px; /* Compensa o padding do container pai em todas as direções */
}


.campaigns-sidebar {
	background-color: #D5E7BB29; /* A<PERSON>l claro */
	padding: 16px;
	
	height: 100%;

	h3 {
		margin: 0;
		color: #1976D2;
		font-size: 16px;
		font-weight: bold;
	}
}

.content-area {
	height: 100%;
}

.content-layout {
	height: 100%;
	flex-direction: column;
}

.filters-section {
	padding: 16px;
	flex: 0 0 30%; 

	h3 {
		margin: 0;
		color: #388E3C;
		font-size: 16px;
		font-weight: bold;
	}
}

.chart-section {
	padding: 16px;
	flex: 1; /* Takes remaining 70% of the content area height */

	h3 {
		margin: 0;
		color: #C2185B;
		font-size: 16px;
		font-weight: bold;
	}
}

.chart-container {
	width: 100%;
	height: 400px;
	overflow-x: auto;
	overflow-y: hidden;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 16px;
	background-color: #fafafa;
}

.chart-loading,
.chart-empty {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 200px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	background-color: #f9f9f9;
}



.campaign-card {
	background-color: #ECF6DC;
	border-radius: 5px;
	padding: 16px;
	margin-bottom: 8px;
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.filters-container {
	display: flex;
	flex-wrap: wrap;
	gap: 12px;
}

.filter-group {
	flex: 1 1 auto;
	min-width: 200px;

	.farm-label {
		display: block;
		margin-bottom: 8px;
	}

	.farm-select {
		width: 100%;
	}
}

.commercial-product-group {
	min-width: 280px;
	flex: 1.5 1 280px;
}

.buttons-group {
	display: flex;
	flex-shrink: 0;
	align-items: center;
	align-self: center;
}

// Responsividade
@media (max-width: 1024px) {
	.filter-group {
		min-width: 180px;
	}

	.commercial-product-group {
		min-width: 250px;
		flex: 1 1 250px;
	}
}

@media (max-width: 768px) {
	.filters-container {
		flex-direction: column;
		align-items: stretch;
		gap: 16px;
	}

	.filter-group {
		min-width: auto;
		width: 100%;
		flex: 1 1 auto;
		.farm-select {
			width: 100%;
		}
	}


	.commercial-product-group {
		min-width: 100%;

		.commercial-product-select {
			width: 100%;
		}
	}

	.buttons-group {
		width: 100%;
		justify-content: flex-start;

	}
}

@media (max-width: 480px) {
	.buttons-group {
		flex-direction: column;

		.farm-btn {
			width: 100%;
			min-width: auto;
		}
	}
}
