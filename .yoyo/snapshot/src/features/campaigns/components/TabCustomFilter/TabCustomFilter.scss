// Main layout container
.main-layout {
	height: 700px; /* Altura fixa para visualização */
	margin: -24px; /* Compensa o padding do container pai em todas as direções */
}

// Sidebar styling
.campaigns-sidebar {
	background-color: #D5E7BB29; /* A<PERSON>l claro */
	padding: 16px;
	
	height: 100%;

	h3 {
		margin: 0;
		color: #1976D2;
		font-size: 16px;
		font-weight: bold;
	}
}

// Content area layout
.content-area {
	height: 100%;
}

.content-layout {
	height: 100%;
	flex-direction: column;
}

// Filters section styling
.filters-section {
	padding: 16px;
	flex: 0 0 30%; /* Takes 30% of the content area height */

	h3 {
		margin: 0;
		color: #388E3C;
		font-size: 16px;
		font-weight: bold;
	}
}

// Chart section styling
.chart-section {
	padding: 16px;
	flex: 1; /* Takes remaining 70% of the content area height */

	h3 {
		margin: 0;
		color: #C2185B;
		font-size: 16px;
		font-weight: bold;
	}
}



.campaign-card {
	background-color: #ECF6DC;
	border-radius: 5px;
	padding: 16px;
	margin-bottom: 8px;
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.filters-container {
	display: flex;
	flex-wrap: wrap;
	gap: 12px;
	align-items: center
}

.filter-group {
	flex: 1 1 auto;
	min-width: 200px; // Increased minimum width for better content display

	.farm-label {
		display: block;
		margin-bottom: 8px;
	}

	// Ensure select components maintain consistent sizing
	.farm-select {
		width: 100%;
		min-width: 100%;
	}
}

// Estilo específico para o select de Produto Comercial
.commercial-product-group {
	min-width: 280px; // Increased for better content display
	flex: 1.5 1 280px; // Give more space preference to this field

	.commercial-product-select {
		min-width: 100%;
		width: 100%;
	}

	// Specific handling for commercial product select
	.commercial-product-select {
		width: 100%;
		min-width: 100%;
	}
}

// Operation Type select styling
.filter-group:has(.farm-select[id="operation-type"]) {
	min-width: 180px;
	flex: 1 1 180px;
}

// Campaign Status select styling
.filter-group:has(.farm-select[id="campaign-status"]) {
	min-width: 200px;
	flex: 1 1 200px;
}

.buttons-group {
	display: flex;
	gap: 8px;
	flex-shrink: 0;
	align-items: center;
	// Align buttons to the bottom to match the select input height
	align-self: flex-center;
	// Add margin to account for the label height and align with input field
	margin-bottom: 4px; // Matches the margin-bottom from farm-textfield--input

	.farm-btn {
		white-space: nowrap;
		height: 36px; // Match the height of farm-textfield--input
	}
}

// Responsividade para telas menores
@media (max-width: 1024px) {
	.filters-container {
		gap: 8px;
	}

	.filter-group {
		min-width: 180px;
	}

	.commercial-product-group {
		min-width: 250px;
		flex: 1 1 250px;
	}
}

@media (max-width: 768px) {
	.filters-container {
		flex-direction: column;
		align-items: stretch;
		gap: 16px;
	}

	.filter-group {
		min-width: auto;
		width: 100%;
		flex: 1 1 auto;

		// Ensure full width on mobile
		.farm-select {
			width: 100%;
		}
	}

	// Mantém largura mínima do produto comercial mesmo em mobile
	.commercial-product-group {
		min-width: 100%;

		.commercial-product-select {
			width: 100%;
		}
	}

	.buttons-group {
		width: 100%;
		justify-content: flex-start;
		margin-top: 8px;
		margin-bottom: 0; // Reset margin-bottom on mobile
		gap: 8px;
		align-self: stretch; // Reset align-self on mobile

		.farm-btn {
			flex: 1;
			min-width: 120px;
			height: auto; // Reset height on mobile for better touch targets
		}
	}
}

// Additional breakpoint for very small screens
@media (max-width: 480px) {
	.buttons-group {
		flex-direction: column;

		.farm-btn {
			width: 100%;
			min-width: auto;
		}
	}
}
