<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" class="mt-3 mb-4">
				<farm-idcaption icon="history" copyText="">
					<template v-slot:title>
						<farm-heading :type="6">
							Histórico de Atualizações
						</farm-heading>
					</template>
					<template v-slot:subtitle>
						Visualize todas integralizações e amortizações feitas ao longo do tempo para o veículo financeiro.
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>
		<farm-row justify="space-between">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Atualização"
					:showFilters="isOpenFilter"
					@onClick="onClickMainFilter"
					@onInputChange="onInputChangeMainFilter"
				/>
			</farm-col>
			<farm-col cols="12" md="3" class="mt-8">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortHistory"
					@change="onSortSelect"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<NetWorthHistoryFilter
				key="networthfilters"
				v-show="isOpenFilter"
				@onApply="onApplyFilter"
			/>
		</collapse-transition>

		<NetWorthHistoryCardList v-if="!isLoading && netWorthHistory" :items="netWorthHistory" />

		<farm-emptywrapper v-if="isListEmpty" subtitle="Tente filtrar novamente sua pesquisa" />

		<farm-row extra-decrease v-if="pagination && !isEmpty">
			<farm-box>
				<farm-datatable-paginator
					:page="page"
					:total-pages="pagination.totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>

		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="load" />
		</div>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { useIsLoading, useRoute } from '@/composibles';
import { format } from '@/helpers/formatUpdateUser';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import { computed, defineComponent, onMounted, ref, watch } from 'vue';
import { useFinancialVehicleHeaders, useNetWorth, usePageable } from '../../composables';
import { sortHistory } from '../../configurations';
import { NetWorthHistoryCardList } from '../NetWorthHistoryCardList';
import { NetWorthHistoryFilter } from '../NetWorthHistoryFilter';

export default defineComponent({
	components: {
		NetWorthHistoryCardList,
		NetWorthHistoryFilter,
	},
	setup(_, { emit }) {
		const route = useRoute();
		const filters = ref({
			search: null,
			type: null,
		});
		const paginationFilters = ref({
			totalItems: null,
			totalPages: null,
			limit: 10,
			page: 0,
		});
		const sortModel = ref('createdAt_DESC');

		const sort = ref({
			orderby: 'createdAt',
			order: 'DESC',
		});

		onMounted(() => {
			load();
		});

		const {
			getNetWorthHistory,
			netWorthHistory,
			netWorthHistoryRequestStatus,
			getNetWorthHistoryUsers,
			netWorthHistoryUsers,
			netWorthHistoryUsersRequestStatus,
		} = useNetWorth();

		const { getFinancialVehicleHeader, financialVehicleHeaderRequestStatus } =
			useFinancialVehicleHeaders(emit);

		const isLoading = useIsLoading([
			netWorthHistoryRequestStatus,
			financialVehicleHeaderRequestStatus,
			netWorthHistoryUsersRequestStatus,
		]);
		const vehicleId = computed(() => route.params.vehicleId);
		const isListEmpty = computed(
			() => !isLoading.value && !netWorthHistory.value?.content.length
		);
		const isError = computed(() => {
			const values = [
				financialVehicleHeaderRequestStatus.value,
				netWorthHistoryRequestStatus.value,
				netWorthHistoryUsersRequestStatus.value,
			];
			return values.includes(RequestStatusEnum.ERROR);
		});

		const {
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onInputChangeMainFilter,
			onApplyFilter,
			isOpenFilter,
			onClickMainFilter,
			isEmpty,
		} = usePageable(
			{
				sort,
				lowercaseSort: true,
				keyInputSearch: 'search',
				filters,
				charInputSearch: 1,
				calbackFn: filters => {
					getNetWorthHistory(vehicleId.value, filters);
				},
			},
			paginationFilters,
			netWorthHistoryRequestStatus,
			netWorthHistory
		);

		function updateFooterHome(data) {
			const filterData = { ...data, createdAt: null, createdBy: null };
			const updatedData = format(filterData);
			emit('onUpdateFooterFormData', updatedData);
		}

		function load() {
			const initialFilters = { ...sort.value, ...paginationFilters.value };
			getNetWorthHistory(vehicleId.value, initialFilters);
			getFinancialVehicleHeader(vehicleId.value);
			getNetWorthHistoryUsers(vehicleId.value);
		}

		watch(netWorthHistoryRequestStatus, newValue => {
			if (newValue == RequestStatusEnum.SUCCESS) {
				updateFooterHome(netWorthHistory.value.meta);
			}
		});

		return {
			sortHistory,
			sortModel,
			filters,
			updateFooterHome,
			load,
			sort,
			onChangePageLimit,
			netWorthHistory,
			isListEmpty,
			isError,
			page,
			netWorthHistoryUsers,
			pagination,
			onChangePage,
			onSortSelect,
			onInputChangeMainFilter,
			onApplyFilter,
			isOpenFilter,
			onClickMainFilter,
			isLoading,
			isEmpty,
		};
	},
});
</script>
