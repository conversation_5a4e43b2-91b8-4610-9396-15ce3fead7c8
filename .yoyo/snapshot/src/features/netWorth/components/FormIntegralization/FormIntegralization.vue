<template>
	<farm-form v-model="valid">
		<farm-row>
			<farm-col cols="12" md="4">
				<farm-label for="form-networth-data-name" required>
					Nome <PERSON> Cota {{ type }}
				</farm-label>
				<farm-textfield-v2
					uppercase
					v-model="data.name"
					id="form-networth-data-name"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="8">
				<farm-label for="form-networth-data-obs"> Observações ({{ type }}) </farm-label>
				<farm-textfield-v2 uppercase v-model="data.obs" id="form-networth-data-obs" />
			</farm-col>
		</farm-row>
		<farm-row>
			<farm-col cols="12" md="4">
				<farm-label class="d-flex" for="form-networth-data-quantity" required>
					Quantidade de Cota {{ type }}
					<farm-caption class="pl-1" variation="regular">
						(Atual: {{ copyValueQuota.quantity }})
					</farm-caption>
				</farm-label>
				<farm-textfield-v2
					mask="##########"
					uppercase
					v-model="data.quantityFix"
					id="form-networth-data-quantity"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label class="d-flex" for="form-networth-data-value" required>
					Valor de Cota {{ type }}
					<farm-caption class="pl-1" variation="regular">
						(Atual: {{ formatMoneyFourDecimal(copyValueQuota.value) }})
					</farm-caption>
				</farm-label>
				<farm-textfield-v2
					uppercase
					v-model="valueInput"
					id="form-networth-data-value"
					:mask="plMask"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label class="d-flex" for="form-product-data-inicial">
					PL Inicial {{ type }}
					<farm-caption class="pl-1" variation="regular">
						(Atual: {{ formatMoneyFourDecimal(cleanMaskMoney(copyValueQuota.initialNetWorth)) }})
					</farm-caption>
				</farm-label>
				<farm-textfield-v2
					id="form-product-data-inicial"
					disabled
					uppercase
					:mask="plMask"
					v-model="initialPL"
				/>
			</farm-col>
		</farm-row>
	</farm-form>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, toRefs, watch } from 'vue';

import { plMask, formatMoneyFourDecimal, cleanMaskMoney } from '@/helpers/masks';

import { useForm } from '../../composables';
import { formatPlSum } from '../../configurations';

export default defineComponent({
	props: {
		data: {
			type: Object,
		},
		type: {
			type: String,
			default: '1',
		},
		index: {
			type: Number,
		}
	},
	setup(props, { emit }) {
		const { data, type, index } = toRefs(props);
		const copyValueQuota = {
			...data.value,
			value: cleanMaskMoney(data.value.value)
		};
		const { formStatus } = useForm();
		const rules = ref({
			required: val => {
				return  !!val || 'Campo obrigatório';
			},
		});
		const valid = ref(false);

		const initialPL = computed({
			get() {
				return formatPlSum(data.value.quantity, data.value.value, data.value.quantityFix);
			},
			set(newValue) {
				data.value.initialNetWorth = newValue;
				return newValue;
			},
		});
		const valueInput = computed({
			get() {
				return data.value.valueDataInput || '0';
			},
			set(newValue) {
				data.value.value = newValue;
				return newValue;
			},
		});
		const currentType = computed(() => type.value.toLowerCase()!);

		function getKey(): string {
			return currentType.value === 'única' ? 'unique': currentType.value;
		}

		watch(valid, val => {
			formStatus.value[getKey()].status = val;
		});

		watch(data.value, newValue => {
			emit('onUpdatedForm', {
				data: newValue,
				key: getKey(),
				index: index.value
			});
		});



		onMounted(() => {
			formStatus.value[getKey()].status = valid;
		});

		return {
			rules,
			initialPL,
			copyValueQuota,
			valid,
			valueInput,
			plMask,
			formatMoneyFourDecimal,
			cleanMaskMoney
		};
	},
});
</script>
