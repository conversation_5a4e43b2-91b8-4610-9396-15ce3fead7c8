<template>
	<farm-row>
		<farm-col class="with-line">
			<farm-caption
				variation="semiBold">
				Quantidades {{labelTypeQuota}} Cotas {{ labelType }} {{ quota.name.toUpperCase() }}
			</farm-caption>
			{{ quota.quantity }}
		</farm-col>
		<farm-col class="with-line">
			<farm-caption variation="semiBold"
				>{{labelTypeQuotaTwo}} Cota {{ labelType }} {{ quota.name.toUpperCase() }}</farm-caption
			>
			<farm-caption variation="medium" class="my-1">{{
				formatMoney(quota.value)
			}}</farm-caption>
		</farm-col>

		<farm-col class="with-line">
			<farm-caption variation="semiBold"
				>PL Base {{ labelType }} {{ quota.name.toUpperCase() }}</farm-caption
			>

			<farm-caption variation="medium" class="my-1">{{
				formatMoney(quota.netWorth)
			}}</farm-caption>
		</farm-col>

		<farm-col>
			<farm-caption variation="semiBold"
				>Observações ({{ labelType }} {{ quota.name.toUpperCase() }})</farm-caption
			>
			<farm-caption variation="medium" class="my-1">{{
				quota.obs.toUpperCase()
			}}</farm-caption>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

import CardListTextHeader from '@/components/CardListTextHeader';

import { formatMoneyFourDecimal } from '@/helpers/masks';


export default defineComponent({
	components: { CardListTextHeader },
	props: {
		quota: {
			type: Object,
			required: true,
		},
		labelType: {
			type: String,
			required: true,
		},
		index: {
			type: Number,
		},
		type: {
			type: String,
			required: true,
		},
	},
	setup(props) {
		const {type} = toRefs(props);
		const labelTypeQuota = computed(()=>{
			if(type.value === 'AMORTIZAÇÃO'){
				return 'Amortizadas da';
			}
			return'de';
		});

		const labelTypeQuotaTwo = computed(()=>{
			if(type.value === 'AMORTIZAÇÃO'){
				return 'Valores Amortizados da';
			}
			return'Valor de';
		});

		function formatMoney(value){
			return formatMoneyFourDecimal(value);
		}

		return {
			labelTypeQuota,
			labelTypeQuotaTwo,
			formatMoney,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './HistoryQuotaItem.scss';
</style>
