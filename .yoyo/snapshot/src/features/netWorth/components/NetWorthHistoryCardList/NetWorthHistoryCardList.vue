<template>
	<farm-box>
		<NetWorthHistoryCard
			v-for="(item, index) in items.content"
			:key="`${item.id}::${index}`"
			:items="item"
			:headers="headers[index]"
		/>
	</farm-box>
</template>

<script lang="ts">
import CardListTextHeader from '@/components/CardListTextHeader';
import { computed, defineComponent, toRefs } from 'vue';

import { formatHeader } from '../../utils';
import { NetWorthHistoryCard } from '../NetWorthHistoryCard';

export default defineComponent({
	components: {
		CardListTextHeader,
		NetWorthHistoryCard,
	},
	props: {
		items: {
			type: Object,
			required: true,
		},
	},
	setup(props) {
		const { items } = toRefs(props);
		const headers = computed(() => formatHeader(items.value.content));
		return {
			headers,
		};
	},
});
</script>
