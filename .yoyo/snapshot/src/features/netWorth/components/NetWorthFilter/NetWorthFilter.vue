<template>
	<farm-box>
		<farm-form class="mb-6" v-model="valid" v-if="!isLoading">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-type">Tipo</farm-label>
					<farm-select-auto-complete
						id="form-filtro-type"
						v-model="filters.type"
						:items="vehicleTypes"
						item-value="value"
						item-text="label"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-quota">PL Base/Inicial</farm-label>
					<farm-select-auto-complete
						id="form-filtro-total-quota"
						v-model="rangeFilters.initial"
						:items="filterBaseValues"
						item-text="label"
						item-value="id"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-in">PL Base/Atual</farm-label>
					<farm-select-auto-complete
						id="form-filtro-baseQuotaValue"
						v-model="rangeFilters.current"
						:items="filterBaseValues"
						item-text="label"
						item-value="id"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filtro-real">PL Real da Carteira</farm-label>
					<farm-select-auto-complete
						id="form-filtro-baseQuotaValue"
						v-model="rangeFilters.real"
						:items="filterBaseValues"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col cols="12">
					<farm-btn-confirm outlined title="Aplicar Filtros" @click="apply">
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="ml-0 ml-sm-2"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>
<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from 'vue';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import { useIsLoading, useRouter } from '@/composibles';
import { useNetWorth } from '../../composables';
import { filterBaseValues } from '../../configurations/filterValues';
import { buildFilterRangeValue } from '../../utils/buildFilterRangeValue';

type FiltersType = {
	real: number[];
	initial: number[];
	current: number[];
};

export default defineComponent({
	setup(_, { emit }) {
		const router = useRouter();
		const valid = ref(false);

		const {
			financialVehicleTypes,
			financialVehicleTypesRequestStatus,
			getFinancialVehicleTypes,
		} = useNetWorth();

		const vehicleTypes = ref(null);

		const isLoading = useIsLoading([financialVehicleTypesRequestStatus]);

		const filters = ref({
			type: null,
		});

		const rangeFilters = ref<FiltersType>({
			real: null,
			initial: null,
			current: null,
		});

		const currentId = computed(() => Number(router.params.id));

		const apply = () => {
			const formattedRangeFilters = buildFilterRangeValue(rangeFilters.value, 'NetWorth');
			const filtersPayload = Object.assign(
				{},
				{ ...filters.value },
				...formattedRangeFilters
			);
			emit('onApply', filtersPayload);
		};
		const onFilterClear = () => {
			filters.value = { type: null };
			rangeFilters.value = { real: null, initial: null, current: null };
			const filtersPayload = { ...rangeFilters.value, ...filters.value };
			emit('onApply', filtersPayload);
		};

		watch(financialVehicleTypesRequestStatus, newValue => {
			if (newValue === RequestStatusEnum.SUCCESS) {
				vehicleTypes.value = financialVehicleTypes.value;
			}
		});

		onMounted(() => {
			getFinancialVehicleTypes();
		});

		return {
			router,
			valid,
			filters,
			currentId,
			apply,
			onFilterClear,

			filterBaseValues,
			rangeFilters,
			isLoading,
			financialVehicleTypes,
			vehicleTypes,
		};
	},
});
</script>
