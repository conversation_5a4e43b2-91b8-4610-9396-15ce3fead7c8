<template>
	<Cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="9">
					<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
					<CardListTextHeader :data="listIdAndType" noSpacing />
				</farm-col>
				<farm-col cols="3" align="end">
					<div class="d-flex justify-end align-center">
						<farm-context-menu
							:items="contextMenuItems()"
							@edit="handleEdit(data)"
							@historic="handleHistory(data)"
							@details="handleDetails(data)"
						/>
					</div>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="4">
					<CardTextBody
						label="PL Base/Inicial"
						:value="formatMoneyFourDecimalOrNA(data.initialNetWorth)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="PL Base/Atual"
						:value="formatMoneyFourDecimalOrNA(data.currentNetWorth)"
					/>
				</farm-col>
				<farm-col cols="4">
					<CardTextBody
						label="PL Real da Carteira"
						:value="formatMoneyFourDecimalOrNA(data.realNetWorth)"
					/>
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import {
	edit as editOption,
	details as detailsOption,
	historic as historicOption,
} from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import {
	formatDateOrNA,
	formatMandateSignature,
	formatYesOrNo,
	formatMoneyOrNA,
formatMoneyFourDecimalOrNA
} from '@/helpers/formatCards';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
	},
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const listIdAndType = ref([
			{ id: '1', label: 'ID', value: props.data.id, copyText: '' },
			{ id: '2', label: 'Tipo', value: props.data.type, copyText: '' },
			{
				id: '3',
				label: 'CNPJ',
				value: props.data.document,
				copyText: props.data.document,
			},
		]);

		const contextMenuItems = () => {
			detailsOption.label = 'Detalhes';
			editOption.label = 'Editar PL Base';
			return [detailsOption, editOption, historicOption];
		};

		const handleHistory = data => {
			emit('history', data);
		};
		const handleDetails = data => {
			emit('details', data);
		};

		const handleEdit = data => {
			emit('edit', data);
		};

		return {
			formatDateOrNA,
			formatMandateSignature,
			formatYesOrNo,
			handleHistory,
			handleDetails,
			handleEdit,
			contextMenuItems,
			listIdAndType,
			formatMoneyOrNA,
			formatMoneyFourDecimalOrNA
		};
	},
});
</script>
