<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" class="mt-3 mb-4">
				<farm-idcaption icon="plus-circle-outline" copyText="">
					<template v-slot:title>
						<farm-heading :type="6"> {{ label }} </farm-heading>
					</template>
					<template v-slot:subtitle>
						Faça a inclusão de cotas, seus nomes e valores considerando os diferentes tipos.
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>
		<QuotaRadioGroup v-model="subType" @radioGroupVal="handleSubType" class="mt-4 mb-4" />
		<template v-for="(item, i) in form">
			<farm-form
				v-model="formStatus[getTypeKey(item.subType).toLowerCase()].status"
				:ref="e => (formRef[item.subType] = e)"
				:key="i"
				v-show="subType === item.subType"
			>
				<farm-row>
					<farm-col cols="12" md="4">
						<farm-label for="form-product-data-start-date" required>
							Data de Ocorrência
						</farm-label>
						<farm-input-datepicker
							ref="datepickerdateOfOccurrence"
							inputId="form-networth-date-of-occurrence"
							v-model="dateOfOccurrence"
							:required="true"
							:rules="currentForm.quotas.length ? [rules.required] : []"
						/>
					</farm-col>
				</farm-row>
				<farm-row extra-decrease class="pb-4">
					<farm-line noSpacing />
				</farm-row>

				<farm-row v-for="(quota, index) in item.quotas" :key="index">
					<farm-col>
						<FormIntegralization
							:data="quota"
							:type="getTypeLabel(item.subType)"
							:index="index"
							@onUpdatedForm="onUpdatedForm"
						/>
						<farm-row
							extra-decrease
							:class="[i + 1 == currentForm.quotas.length ? 'pb-0' : 'pb-4']"
						>
							<farm-line noSpacing />
						</farm-row>
					</farm-col>
				</farm-row>
			</farm-form>
		</template>
		<farm-row justify="center" v-if="subType < '4'">
			<farm-btn
				class="d-flex align-center my-6"
				size="xl"
				full
				outlined
				@click="handleAddNewFormValues"
			>
				<farm-icon size="md">plus</farm-icon>
				<farm-bodytext variation="bold" color="primary">
					Adicionar Cota
					{{ getTypeLabel(subType) }}
				</farm-bodytext>
			</farm-btn>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from 'vue';
import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';

import { useIsLoading, useRoute, useRouter } from '@/composibles';
import { createObject } from '@/helpers/createObject';
import { format } from '@/helpers/formatUpdateUser';

import { useFinancialVehicleHeaders, useForm, useNetWorth } from '../../composables';
import { NetWorthFormTabModel, NetWorthFormTabQuotaTypes } from '../../types';
import { generateIntegralizationPayload, getTypeLabel, getTypeKey } from '../../utils';
import { FormIntegralization } from '../FormIntegralization';
import { QuotaRadioGroup } from '../QuotaRadioGroup';

export default defineComponent({
	props: {
		isEdit: {
			type: Boolean,
		},
		label: {
			type: String,
		},
	},
	components: {
		FormIntegralization,
		QuotaRadioGroup,
	},
	setup(_, { emit }) {
		const route = useRoute();
		const router = useRouter();
		const {
			form,
			formRef,
			formStatus
		} = useForm();
		const {
			netWorthAmortizationRequestStatus,
			netWorthEditDetailsRequestStatus,
			netWorthDetailsRequestStatus,
			patchNetWorthAmortization,
			netWorthDetails,
			getNetWorthDetails,
			getNetWorthEditDetails,
			netWorthEditDetails,
		} = useNetWorth();
		const {
			financialVehicleHeaderRequestStatus,
			getFinancialVehicleHeader
		} = useFinancialVehicleHeaders(emit);
		const isLoading = useIsLoading([
			netWorthDetailsRequestStatus,
			financialVehicleHeaderRequestStatus,
			netWorthEditDetailsRequestStatus,
			netWorthAmortizationRequestStatus,
		]);

		const valid = ref(false);
		const subType = ref(1);
		const formCache = ref(null);
		const dateOfOccurrence = ref('');
		const rules = ref({
			required: value => !!value || 'Campo obrigatório',
		});

		const currentForm = computed(() => form.value[subType.value - 1]);
		const vehicleId = computed(() => route.params.vehicleId);

		function handleAddNewFormValues(): void {
			const createQuotaObject = createObject<NetWorthFormTabQuotaTypes>(NetWorthFormTabModel);
			currentForm.value.quotas.push(createQuotaObject);
			formRef.value[subType.value].restart();
		}

		function getCountDataCache(key, index) {
			if(formCache.value !== null) {
				return formCache.value[key][index].quantityFix || 0;
			}
			return 0;
		}

		function formatQuotas(quotaType = 'jr'): void {
			const key = quotaType === 'única' ? 'unique' : quotaType;
			const quotas = netWorthEditDetails.value[key].map((item, index) => {
				return {
					...item,
					quantityFix: getCountDataCache(key, index),
					initialNetWorth: (item.quantity + 0) * parseFloat(item.value),
					value: parseFloat(item.value)
				};
			});
			if(quotaType === 'única' && quotas.length === 0){
				currentForm.value.quotas = [{
					id: null,
					name: '',
					obs: '',
					quantity: 0,
					quantityFix: 0,
					value: 0,
					initialNetWorth: 0
				}];
				updatedFormCache(currentForm.value.quotas, key);
				return;
			}
			const newData = updatedFormCache([...quotas], key);
			currentForm.value.quotas = [...newData];

		}

		function onSubmit(): void {
			const { payload } = generateIntegralizationPayload(form.value, dateOfOccurrence);
			patchNetWorthAmortization(vehicleId.value, payload);
		}

		function load(): void {
			getFinancialVehicleHeader(vehicleId.value);
			getNetWorthDetails(vehicleId.value);
			getNetWorthEditDetails(vehicleId.value);
			emit('onSubmit', onSubmit);
		}

		function updateFooterHome(data): void {
			const filterData = { ...data, createdAt: null, createdBy: null };
			const updatedData = format(filterData);
			emit('onUpdateFooterFormData', updatedData);
		}

		function createModelValid(size: number) {
			const dataValid = [];
			for(let i=0; i < size; i++){
				dataValid.push(false);
			}
			return [...dataValid];
		}

		function isValidFieldValue(value): boolean {
			if(!value) return true;
			if(value.length === 2) {
				return true;
			}
			const valueWithoutMask = parseFloat(value.toString().replace('R$', ''));
			if(valueWithoutMask === 0){
				return true;
			}
			return false;
		}

		function validFieldsForm(quotas, valid) {
			for(let i=0; i < quotas.length; i++){
				if(quotas[i].name.length === 0){
					valid[i] = false;
					break;
				}
				if(quotas[i].quantityFix.toString().length === 0){
					valid[i] = false;
					break;
				}
				if(isValidFieldValue(quotas[i].value)){
					valid[i] = false;
					break;
				}
				if(quotas[i].initialNetWorth.toString().length === 0){
					valid[i] = false;
					break;
				}
				valid[i] = true;
			}
			const hasFieldValid = valid.filter((item) => {
				return item === false;
			});
			return hasFieldValid.length === 0;
		}

		function validForm(): void {
			if(dateOfOccurrence.value.length === 0){
				emit('onDisabledButtonFooter', false);
				return;
			}
			const dataCurrentValid = createModelValid(currentForm.value.quotas.length);
			const isValid = validFieldsForm(currentForm.value.quotas, dataCurrentValid);
			emit('onDisabledButtonFooter', isValid);
		}

		function handleSubType(val): void {
			subType.value = val;
			validForm();
		}

		function updatedFormCache(value, key, index=0) {
			if(formCache.value === null){
				formCache.value = {
					jr: [],
					mz: [],
					sr: [],
					unique: []
				};
			}
			if(value.length === 0) {
				return formCache.value[key];
			}
			formCache.value[key][index] = value[0];
			return formCache.value[key];
		}

		function initFormCache(value): void {
			formCache.value = {
				jr: [...value.jr],
				mz: [...value.mz],
				sr: [...value.sr],
				unique: [...value.unique]
			};
		}

		function onUpdatedForm({data, key, index}){
			updatedFormCache([data], key, index);
			validForm();
		}

		watch(
			() => form.value[subType.value],
			() => {
				const quotaType = getTypeLabel(subType.value).toLowerCase();
				formatQuotas(quotaType);
			}
		);
		watch(netWorthAmortizationRequestStatus, newValue => {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(RequestStatusEnum.SUCCESS, 'Integralização Realizada com sucesso!');
				setTimeout(() => {
					router.go(0);
				}, 1000);
			}
		});
		watch(netWorthEditDetailsRequestStatus, newValue => {
			if (newValue == RequestStatusEnum.SUCCESS) {
				initFormCache(netWorthEditDetails.value);
				formatQuotas();
				updateFooterHome(netWorthEditDetails.value.meta);
			}
		});
		watch(currentForm.value, (newValue) => {
			const quotaType = getTypeLabel(subType.value).toLowerCase();
			updatedFormCache(newValue.quotas, quotaType);
			validForm();
		});
		watch(dateOfOccurrence, (newValue) => {
			if(newValue){
				validForm();
				return;
			}
			emit('onDisabledButtonFooter', false);
		});

		onMounted(() => {
			load();
		});

		return {
			form,
			valid,
			rules,
			handleAddNewFormValues,
			updateFooterHome,
			getTypeLabel,
			getTypeKey,
			isLoading,
			netWorthDetails,
			subType,
			dateOfOccurrence,
			handleSubType,
			currentForm,
			formRef,
			formStatus,
			onUpdatedForm
		};
	},
});
</script>
