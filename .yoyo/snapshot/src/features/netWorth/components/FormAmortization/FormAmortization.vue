<template>
	<farm-form v-model="valid">
		<farm-row>
			<farm-col cols="12" md="4">
				<farm-label for="form-networth-data-name" required>
					Nome da Cota {{ type }}
				</farm-label>
				<farm-textfield-v2
					disabled
					uppercase
					v-model="data.name"
					id="form-networth-data-name"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="8">
				<farm-label for="form-networth-data-obs"> Observações ({{ type }}) </farm-label>
				<farm-textfield-v2 uppercase v-model="data.obs" id="form-networth-data-obs" />
			</farm-col>
		</farm-row>
		<farm-row>
			<farm-col cols="12" md="4">
				<farm-label class="d-flex" for="form-networth-data-quantity" required>
					Quantidade Amortizada da Cota {{ type }}
					<farm-caption class="pl-1" variation="regular">
						(Atual: {{ copyValueQuota.quantity }})
					</farm-caption>
				</farm-label>
				<farm-textfield-v2
					mask="##########"
					v-model="data.quantityFix"
					id="form-networth-data-quantity"
					:rules="[rules.required, rules.validQuota]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label class="d-flex" for="form-networth-data-value" required>
					Valor Amortizado da Cota {{ type }}
					<farm-caption class="pl-1" variation="regular">
						(Atual: {{ formatMoneyFourDecimal(copyValueQuota.value) }})
					</farm-caption>
				</farm-label>
				<farm-textfield-v2
					uppercase
					v-model="modelInput"
					id="form-networth-data-value"
					:mask="plMask"
					:rules="[rules.required, rules.checkValueQuota]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label class="d-flex" for="form-product-data-inicial">
					PL Inicial {{ type }}
					<farm-caption class="pl-1" variation="regular">
						(Atual: {{ formatMoneyFourDecimal(cleanMaskMoney(copyValueQuota.initialNetWorth)) }})
					</farm-caption>
				</farm-label>
				<farm-textfield-v2
					id="form-product-data-inicial"
					disabled
					uppercase
					:mask="plMask"
					v-model="totalValueQuantity"
				/>
			</farm-col>
		</farm-row>
	</farm-form>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, toRefs, watch } from 'vue';

import { plMask, formatMoneyFourDecimal, cleanMaskMoney } from '@/helpers/masks';

import { useForm } from '../../composables';
import { formatPlSubtraction } from '../../configurations';

export default defineComponent({
	props: {
		data: {
			type: Object,
		},
		type: {
			type: String,
			default: '1',
		},
	},
	setup(props, { emit }) {
		const { data, type } = toRefs(props);

		const copyValueQuota = {
			...data.value,
			value: cleanMaskMoney(data.value.value)
		};
		const { formStatus } = useForm();

		const totalValueQuantity = computed({
			get() {
				return formatPlSubtraction(data.value.quantity, data.value.value, data.value.quantityFix, copyValueQuota.value);
			},
			set(newValue) {
				data.value.initialNetWorth = newValue;
				return newValue;
			},
		});
		const valueInput = computed({
			get() {
				return data.value.valueDataInput || '0';
			},
			set(newValue) {
				data.value.value = newValue;
				return newValue;
			},
		});
		const currentType = computed(() => type.value.toLowerCase()!);

		const modelInput = ref(data.value.valueDataInput || '0');
		const valid = ref(false);
		const rules = ref({
			required: val => !!val || 'Campo obrigatório',
			validQuota: val => {
				return (
					val <= data.value.quantity ||
					'Quantidade deve ser menor que a quantidade de cota atual'
				);
			},
			checkValueQuota: val => {
				const newValue = parseFloat(cleanMaskMoney(val));
				const valueCurrent = parseFloat(formatValueMoney(data.value.valueDataInput));
				return (
					(newValue <= valueCurrent) ||
					'O valor da amortização deve ser menor ou igual ao valor atual.'
				);
			},
		});

		function formatValueMoney(value): string {
			if(!value || value === null || value.length === 0){
				return '0';
			}
			return value.replace(',','.');
		}

		function getKey(): string {
			return currentType.value === 'única' ? 'unique': currentType.value;
		}

		watch(modelInput, newValue => {
			data.value.value = newValue;
			emit('validForm');
		});

		watch(data.value, () => {
			emit('validForm');
		});

		watch(valid, val => {
			formStatus.value[getKey()].status = val;
		});

		onMounted(() => {
			formStatus.value[getKey()].status = valid;
		});

		return {
			modelInput,
			rules,
			totalValueQuantity,
			copyValueQuota,
			valid,
			valueInput,
			plMask,
			formatMoneyFourDecimal,
			cleanMaskMoney
		};
	},
});
</script>
