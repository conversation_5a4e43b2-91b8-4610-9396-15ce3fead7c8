export function buildFilterRangeValue<T extends Record<string, number[]>>(
	filters: T,
	name = ''
): Record<string, number>[] {
	return Object.keys(filters)
		.map(x => {
			const filterVal = filters[x];
			const key = x.concat(`${name}Start`);
			const key2 = x.concat(`${name}End`);

			if (filterVal) {
				return {
					[key]: filterVal[0],
					[key2]: filterVal[1],
				};
			}
		})
		.filter(x => !!x);
}
