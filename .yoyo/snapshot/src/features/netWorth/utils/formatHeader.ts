import { HistoryHeaderItem } from './../types';
import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

const listHeaders = [
	{
		id: null,
		label: 'ID',
		value: null,
		copyText: '',
	},
	{
		id: null,
		label: 'Data',
		value: '',
		copyText: '',
	},
	{
		id: null,
		label: 'Usuário',
		value: null,
		copyText: '',
	},
];

function formatHeaderDate(date: string) {
	const dataSplit = date.split('T');
	const hoursSplit = dataSplit[1].split(':');
	const hoursFormatted = `${hoursSplit[0]}:${hoursSplit[1]}`;
	return `${defaultDateFormat(dataSplit[0])} ás ${hoursFormatted}`;
}

function checkIndexValue(index, item: HistoryHeaderItem) {
	if (index === 0 || index === 2) {
		return index ? item.createdBy : item.id;
	} else {
		return formatHeaderDate(item.createdAt);
	}
}

export const formatHeader = (items: HistoryHeaderItem[]) => {
	return items.map(item => {
		return listHeaders.map((x, index) => {
			return {
				...x,
				value: checkIndexValue(index, item),
			};
		});
	});
};
