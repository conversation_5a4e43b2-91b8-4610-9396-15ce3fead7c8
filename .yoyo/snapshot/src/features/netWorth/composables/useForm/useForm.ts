import { computed, ref } from 'vue';

const formStatus = ref({
	jr: {
		status: true,
		subType: 1,
	},
	mz: {
		status: true,
		subType: 2,
	},
	sr: {
		status: true,
		subType: 3,
	},
	unique: {
		status: true,
		subType: 4,
	},
});

const form = ref([
	{
		type: 1,
		subType: 1,
		dateOfOccurrence: '',
		quotas: [],
	},
	{
		type: 1,
		subType: 2,
		dateOfOccurrence: '',
		quotas: [],
	},
	{
		type: 1,
		subType: 3,
		dateOfOccurrence: '',
		quotas: [],
	},
	{
		type: 1,
		subType: 4,
		dateOfOccurrence: '',
		quotas: [],
	},
]);

const formRef = ref({});

export function useForm() {
	const isAllFormsValid = computed(() => {


		const formBeforeValidation = Object.values(form.value).filter(x => x.quotas.length);



		let isValid = false;
		for (let i = 0; i < formBeforeValidation.length; i++) {
			isValid = Object.values(formStatus.value).every(v => {
				return v.status;
			});
		}
		return isValid;
	});

	return { isAllFormsValid, formStatus, form, formRef };
}
