import { ComputedRef, UnwrapRef, computed, ref, watch } from 'vue';

export function usePaginationConstructor<T>(
	elements: ComputedRef<T[]>,
	limit: number = 10,
	hasLoading = true
) {
	const content = ref(elements.value);
	const isLoading = ref(false);
	const filters = ref({
		pageNumber: 0,
		pageSize: limit,
		sort: null,
		totalElements: 0,
		totalPages: 0,
	});

	const presentableFilterValues = computed(() => ({
		pageNumber: filters.value.pageNumber + 1,
	}));

	watch(elements, newValue => {
		const elementsLength = newValue?.length ?? 0;

		filters.value.totalPages = Math.ceil(elementsLength / filters.value.pageSize);
		filters.value.totalElements = elementsLength;
	});

	watch(
		filters,
		async newValue => {
			const start = newValue.pageNumber * newValue.pageSize;
			if (hasLoading) {
				isLoading.value = true;
				await new Promise(resolve => setTimeout(resolve, 750));
				isLoading.value = false;
			}
			content.value =
				(elements.value.slice(start, start + newValue.pageSize) as UnwrapRef<T[]>) ?? [];
		},
		{
			deep: true,
		}
	);

	const onChangePageLimit = async (newPageLimit: number) => {
		filters.value.totalPages = Math.ceil(elements.value.length / newPageLimit);
		filters.value.pageSize = newPageLimit;
		filters.value.pageNumber = 0;
	};

	const onChangePage = (newPage: number) => {
		filters.value.pageNumber = newPage - 1;
	};

	const setFilters = newFilters => {
		filters.value = {
			...filters.value,
			...newFilters,
		};
	};

	const resetPagination = () => {
		filters.value.pageNumber = 0;
	};

	return {
		filters,
		presentableFilterValues,
		content,
		isLoading,
		resetPagination,
		setFilters,
		onChangePage,
		onChangePageLimit,
	};
}
