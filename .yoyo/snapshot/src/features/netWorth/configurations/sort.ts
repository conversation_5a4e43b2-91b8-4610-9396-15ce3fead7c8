export const sortNetWorth = [
	{ label: 'Alfabético A-Z', value: 'name_ASC' },
	{ label: 'Alfabético Z-A', value: 'name_DESC' },
	{ label: 'PL Base/Inicial Maior - Menor', value: 'initialNetWorth_DESC' },
	{ label: 'PL Base/Inicial Menor - <PERSON>or', value: 'initialNetWorth_ASC' },
	{ label: 'PL Base/Atual Maior - Menor', value: 'currentNetWorth_DESC' },
	{ label: 'PL Base/Atual Menor - Maior', value: 'currentNetWorth_ASC' },
	{ label: 'PL Real da Carteira Maior - Menor', value: 'realNetWorth_DESC' },
	{ label: 'PL Real da Carteira Menor - Maior', value: 'realNetWorth_ASC' },
];

export const sortHistory = [
	{ label: '<PERSON><PERSON> - <PERSON>', value: 'createdAt_DESC' },
	{ label: '<PERSON><PERSON> - <PERSON>', value: 'createdAt_ASC' },
	{ label: 'Quantidade Total de Cotas Maior - Menor', value: 'totalQuotaQuantity_DESC' },
	{ label: 'Quantidade Total de Cotas Menor - Maior', value: 'totalQuotaQuantity_ASC' },
	{ label: 'PL Base/Total Maior - Menor', value: 'baseQuotaValue_DESC' },
	{ label: 'PL Base/Total Menor - Maior', value: 'baseQuotaValue_ASC' },
];
