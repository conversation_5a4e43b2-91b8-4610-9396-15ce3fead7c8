import { RequestStatusEnum, errorBuilder } from '@farm-investimentos/front-mfe-libs-ts';
import {
	fetchNetWorthDetails as fetchNetWorthDetailsService,
	fetchNetWorthEditDetailsService,
	fetchNetWorthHistory as fetchNetWorthHistoryService,
	fetchNetWorthHistoryUsers as fetchNetWorthHistoryUsersService,
	fetchNetWorthList as fetchNetWorthListService,
	patchNetWorthAmortization as patchNetWorthAmortizationService,
	postNetWorthIntegralization as postNetWorthIntegralizationService,
} from '../services';
import { NetWorthQuotaPayload } from '../types';

import { builderGetNetWorthDetails } from '../helpers/builderGetNetWorthDetails';
import { builderValueInput } from '../helpers/builderValueInput';

export default {
	async getNetWorthList({ commit }, { params }) {
		commit('setNetWorthListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await fetchNetWorthListService(params);
			commit('setNetWorthList', data);
			commit('setNetWorthListRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setNetWorthListRequestStatus', errorBuilder(error));
		}
	},

	async getNetWorthDetails({ commit }, { vehicleId }) {
		commit('setNetWorthDetailsRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await fetchNetWorthDetailsService(vehicleId);
			const newData = builderGetNetWorthDetails(data, ['currentNetWorth','initialNetWorth', 'realNetWorth']);
			commit('setNetWorthDetails', newData);
			commit('setNetWorthDetailsRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setNetWorthDetailsRequestStatus', errorBuilder(error));
		}
	},

	async postNetWorthIntegralization(
		{ commit },
		{ vehicleId, payload }: { vehicleId: number; payload: NetWorthQuotaPayload }
	) {
		commit('setNetWorthIntegralizationRequestStatus', RequestStatusEnum.START);
		try {
			await postNetWorthIntegralizationService(vehicleId, payload);
			commit('setNetWorthIntegralizationRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setNetWorthIntegralizationRequestStatus', errorBuilder(error));
		}
	},

	async getNetWorthHistory({ commit }, { vehicleId, params }) {
		commit('setNetWorthHistoryRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await fetchNetWorthHistoryService(vehicleId, params);
			commit('setNetWorthHistory', data);
			commit('setNetWorthHistoryRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setNetWorthHistoryRequestStatus', errorBuilder(error));
		}
	},

	async getNetWorthHistoryUsers({ commit }, { vehicleId }) {
		commit('setNetWorthHistoryUsersRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await fetchNetWorthHistoryUsersService(vehicleId);
			commit('setNetWorthHistoryUsers', data);
			commit('setNetWorthHistoryUsersRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setNetWorthHistoryUsersRequestStatus', errorBuilder(error));
		}
	},

	async getNetWorthEditDetails({ commit }, { vehicleId }) {
		commit('setNetWorthEditDetailsRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await fetchNetWorthEditDetailsService(vehicleId);
			commit('setNetWorthEditDetails', builderValueInput(data));
			commit('setNetWorthEditDetailsRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setNetWorthEditDetailsRequestStatus', errorBuilder(error));
		}
	},

	async patchNetWorthAmortization(
		{ commit },
		{ vehicleId, payload }: { vehicleId: number; payload: NetWorthQuotaPayload }
	) {
		commit('setNetWorthAmortizationRequestStatus', RequestStatusEnum.START);
		try {
			await patchNetWorthAmortizationService(vehicleId, payload);
			commit('setNetWorthAmortizationRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setNetWorthAmortizationRequestStatus', errorBuilder(error));
		}
	},
};
