<template>
	<farm-box>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Produto"
					:showFilters="filter"
					@onClick="showFilters"
					@onInputChange="filterInputChanged"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="end">
				<farm-btn-confirm
					v-if="canWrite"
					class="v-btn--responsive mt-8"
					customIcon="plus"
					title="Associar Produto"
					:to="`/admin/cadastros/originadores/${currentDocument}/associacao_produto/novo`"
					:icon="true"
				>
					Associar Produto
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300" v-if="!isError">
			<ProductsFilter
				v-show="filter"
				key="filters"
				:listType="productsType"
				@onApply="searchListener"
			/>
		</collapse-transition>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="8" md="8"></farm-col>
			<farm-col cols="12" md="4" align="end">
				<farm-select-auto-complete
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>
		<ProductsList
			v-if="!isError"
			:data="dataList"
			@handleEdit="handleEdit"
			@handleFinalizeAssociation="handleFinalizeAssociation"
			@handleReAssociateOption="handleReAssociateOption"
		/>
		<farm-row extra-decrease v-if="!isError && dataList.length > 0">
			<farm-box>
				<farm-datatable-paginator
					:page="currentPage"
					:totalPages="totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangeLimitPerPage"
				/>
			</farm-box>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<farm-prompt-user
			v-model="showModal"
			title="Finalizar Associação"
			subtitle=""
			match="REMOVER"
			confirmLabel="Sim"
			@onConfirm="onConfirm"
			@onClose="onClose"
		>
			<template v-slot:subtitle>
				<farm-typography size="md" class="mt-6">
					Deseja realmente finalizar a associação com o produto
				</farm-typography>
				<farm-typography bold size="md"> {{ dataSelected.name }}?</farm-typography>
				<farm-typography size="md" class="mt-3">
					Escreva no campo abaixo
					<farm-typography bold size="md" tag="span">“REMOVER”</farm-typography> para
					confirmar o fim da associação.
				</farm-typography>
			</template>
		</farm-prompt-user>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	notification,
	pageable,
	RequestStatusEnum,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import { format } from '@/helpers/formatUpdateUser';

import ProductsCard from '../ProductsCard';
import ProductsFilter from '../ProductsFilter';
import ProductsList from '../ProductsList';
import { sort } from '../../configurations/sorts';

export default defineComponent({
	components: {
		ProductsFilter,
		ProductsList,
		ProductsCard,
	},
	mixins: [pageable],
	data() {
		return {
			sortModel: 'startDate_DESC',
			sortOptions: sort,
			lastSearchFilters: { page: 0, limit: 10 },
			filter: false,
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'startDate',
				order: 'DESC',
			},
			productsType: [
				{
					id: 1,
					type: 'Atacado',
				},
				{
					id: 2,
					type: 'Varejo',
				},
			],
			filterInputKey: 'search',
			totalPages: 0,
			dataSelected: null,
			showModal: false,
			dataList: [],
		};
	},
	computed: {
		...mapGetters('cadastros', {
			originatorAssignorHeaderData: 'originatorAssignorHeaderData',
			originatorAssignorHeaderRequestStatus: 'originatorAssignorHeaderRequestStatus',
			originatorAssignorInProducts: 'originatorAssignorInProducts',
			originatorAssignorInProductsRequestStatus: 'originatorAssignorInProductsRequestStatus',
			finalizeAssociationOriginatorAssignorToProductRequestStatus:
				'finalizeAssociationOriginatorAssignorToProductRequestStatus',
			reassociateOriginatorAssignorToProductRequestStatus:
				'reassociateOriginatorAssignorToProductRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.originatorAssignorInProductsRequestStatus,
				this.finalizeAssociationOriginatorAssignorToProductRequestStatus,
				this.reassociateOriginatorAssignorToProductRequestStatus,
				this.originatorAssignorHeaderRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [this.originatorAssignorInProductsRequestStatus.type];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
	},
	mounted(): void {
		this.getOriginatorsAssignorsHeader({
			document: this.currentDocument,
		});
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getOriginatorsAssignorsHeader: 'getOriginatorsAssignorsHeader',
			getOriginatorAssignorInProducts: 'getOriginatorAssignorInProducts',
			finalizeAssociationOriginatorAssignorToProduct:
				'finalizeAssociationOriginatorAssignorToProduct',
			reassociateOriginatorAssignorToProduct: 'reassociateOriginatorAssignorToProduct',
		}),
		doSearch(): void {
			this.lastSearchFilters = { ...this.filters };
			const filters = { ...this.filters, ...this.hasSort };
			this.getOriginatorAssignorInProducts({
				filters,
				document: this.currentDocument,
			});
		},
		reload(): void {
			this.doSearch();
		},
		showFilters(): void {
			this.filter = !this.filter;
		},
		handleEdit(data): void {
			const idProduct = data.id;
			const document = this.currentDocument;
			const path = `/admin/cadastros/originadores/${document}/associacao_produto/${idProduct}/editar`;
			this.$router.push({
				path,
			});
		},
		changeSort(): void {
			const [orderby, order] = this.sortModel.split('_');
			this.hasSort = {
				orderby: orderby,
				order: order,
			};
			this.doSearch();
		},
		updatedList(): void {
			this.dataList = this.originatorAssignorInProducts.content;
		},
		updatedTotalPages(): void {
			this.totalPages = this.originatorAssignorInProducts.totalPages;
		},
		updatedHeader(): void {
			const headerData = this.originatorAssignorHeaderData.content;
			this.$emit('onUpdateHeaderForm', {
				title: headerData.name,
				listIcons: [headerData.id, [headerData.document, headerData.raiz]],
			});
		},
		updatedFooter(): void {
			const { meta } = this.originatorAssignorInProducts;
			const updatedFooterFormData = format(meta);
			this.$emit('onUpdateFooterFormData', updatedFooterFormData);
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/originadores`,
			});
		},
		handleFinalizeAssociation(data): void {
			this.showModal = true;
			this.dataSelected = data;
		},
		handleReAssociateOption(data): void {
			this.dataSelected = data;
			this.$dialog
				.confirm(
					{
						body: `Deseja associar novamente o produto <b>${stripTags(
							data.name
						)}</b>? `,
						title: 'Associar Novamente',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					const productId = this.dataSelected.id;
					const document = this.currentDocument;
					this.reassociateOriginatorAssignorToProduct({ productId, document });
				})
				.catch(() => {
					this.doSearch();
				});
		},
		createDialogEdit(): void {
			this.$dialog
				.confirm(
					{
						body: `Sacado associado com sucesso ao produto <b>${stripTags(
							this.dataSelected.name
						)}</b>!<br/>Deseja editar associação? `,
						title: 'Sucesso',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					const idProduct = this.dataSelected.id;
					const document = this.currentDocument;
					const path = `/admin/cadastros/originadores/${document}/associacao_produto/${idProduct}/editar`;
					this.$router.push({
						path,
					});
				})
				.catch(() => {
					this.doSearch();
				});
		},
		onConfirm(): void {
			const payload = {
				productId: this.dataSelected.id,
				document: this.currentDocument,
			};
			this.finalizeAssociationOriginatorAssignorToProduct(payload);
			this.onClose();
		},
		onClose(): void {
			this.showModal = false;
		},
		reloadPage(): void {
			setTimeout(() => {
				this.doSearch();
			}, 2000);
		},
	},
	watch: {
		originatorAssignorHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader();
			}
		},
		originatorAssignorInProductsRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedList();
				this.updatedTotalPages();
				this.updatedFooter();
			}
		},
		finalizeAssociationOriginatorAssignorToProductRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(
					newValue,
					`Associação finalizada com sucesso para o produto <b>${stripTags(
						this.dataSelected.name
					)}</b>`
				);

				if (newValue.type === RequestStatusEnum.ERROR) {
					notification(
						RequestStatusEnum.ERROR,
						`Não foi possível finalização da associação para o produto <b>${stripTags(
							this.dataSelected.name
						)}</b>.`
					);
				}
				this.reloadPage();
			}
		},
		reassociateOriginatorAssignorToProductRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.createDialogEdit();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					`Não foi associar novamente para o produto <b>${stripTags(
						this.dataSelected.name
					)}</b>.`
				);
			}
		},
	},
});
</script>
