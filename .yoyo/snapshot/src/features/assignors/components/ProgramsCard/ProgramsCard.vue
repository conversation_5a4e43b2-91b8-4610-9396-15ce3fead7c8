<template>
	<Cards>
		<template slot="header">
			<farm-row>
				<farm-col cols="12">
					<CardTitleHeader :value="data.name" class="mb-1" ellipsis />
				</farm-col>
				<farm-col cols="12">
					<CardListTextHeader :data="listIdAndType" noSpacing class="mb-1" />
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="6">
					<CardTextBody
						label="Início de Associação"
						:value="formatDateOrNA(data.startDate)"
					/>
				</farm-col>
				<farm-col cols="6">
					<CardTextBody label="Fim de Associação" :value="formatDateOrNA(data.endDate)" />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Cards from '@/components/Cards';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import CardListTextHeader from '@/components/CardListTextHeader';
import { formatDateOrNA } from '@/helpers/formatCards';

export default defineComponent({
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			formatDateOrNA,
			listIdAndType: [
				{ label: 'ID', value: this.data.id, copyText: '' },
				{ label: 'Tipo', value: this.data.type, copyText: '' },
			],
		};
	},
});
</script>
