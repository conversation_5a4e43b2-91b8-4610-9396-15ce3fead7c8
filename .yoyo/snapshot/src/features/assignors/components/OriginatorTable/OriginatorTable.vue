<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				hide-default-footer
				:class="{
					'elevation-0 mt-0 table-cadastros-assignors-list': true,
					'mb-8': data.length === 0,
				}"
				item-key="key"
				:items="data"
				:headers="headers"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader()"
				:options.sync="options"
				:header-props="headerProps"
			>
				<template slot="no-data">
					<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" />
				</template>

				<template v-slot:header="{ props: { headers } }" v-if="showCustomHeader()">
					<farm-datatable-header
						firstSelected
						:headers="headers"
						:sortClick="sortClicked"
						:selectedIndex="5"
						@onClickSort="onSort"
					/>
				</template>

				<template v-slot:[`item.status`]="{ item }">
					<StatusActiveAndInactive :status="item.status" uppercase />
				</template>

				<template v-slot:[`item.createdAt`]="{ item }">
					{{ defaultDateFormat(item.createdAt) }}
				</template>

				<template v-slot:[`item.infos`]="{ item }">
					<farm-context-menu
						:items="contextMenuItems(item)"
						@edit="editItem(item)"
						@remove="removeItem(item)"
					/>
				</template>
				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="data.length > 0"
						class="mt-6 mb-n6"
						:page="currentPagination"
						:totalPages="paginationTotalPages"
						@onChangePage="onChangePageTable"
						@onChangeLimitPerPage="onChangeLimitPerPageTable"
					/>
				</template>
			</v-data-table>
		</farm-box>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
	pageable,
	RequestStatusEnum,
	edit as editOption,
	defaultDateFormat,
} from '@farm-investimentos/front-mfe-libs-ts';

import StatusActiveAndInactive from '@/components/StatusActiveAndInactive';

import { headers } from '../../configurations/headers';
import { mapActions, mapGetters } from 'vuex';

export default defineComponent({
	components: {
		StatusActiveAndInactive,
	},
	props: {
		data: {
			type: Array,
			require: true,
		},
		paginationTotalPages: {
			type: Number,
			require: true,
		},
		paginationPageActive: {
			type: Number,
			default: 1,
		},
		filter: {
			type: Object,
			default: () => ({}),
		},
	},
	mixins: [pageable],
	data() {
		return {
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
			sortClicked: [],
			headerProps: {
				sortByText: 'Ordenar por',
			},
			headers,
			options: {},
			currentPagination: 1,
			idClicked: 0,
		};
	},
	computed: {
		...mapGetters('cadastros', {
			selectedProduct: 'selectedProduct',
			assignorsDataUserRequestStatus: 'assignorsDataUserRequestStatus',
			assignorsDataUser: 'assignorsDataUser',
		}),
		breakpoint() {
			return this.$vuetify.breakpoint.name;
		},
	},
	methods: {
		...mapActions('cadastros', {
			getDataAssignors: 'getDataAssignors',
			getCheckDocument: 'getCheckDocument',
		}),
		onSort(data) {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			const filtersActive = {
				...this.filters,
				name: this.filter.name || '',
				orderby: data.field,
				order: data.descending,
			};
			this.$emit('onRequest', filtersActive, 1);
		},
		contextMenuItems() {
			if (!this.canWrite) {
				return [];
			}
			return [editOption];
		},
		createParamsUrl(item): string {
			const urlRouter = '/admin/cadastros/originadores/';
			return `${urlRouter}${item.cnpj}/editar?path=dados`;
		},
		editItem(item): void {
			const path = this.createParamsUrl(item);
			this.$router.push({
				path,
			});
		},
		defaultDateFormat,
		showCustomHeader() {
			return this.breakpoint !== 'xs';
		},
		onChangePageTable(page: number) {
			const pageActive = page === 1 ? 1 : page - 1;
			this.filters.page = pageActive;
			this.currentPagination = page;
			this.$emit(
				'onRequest',
				{
					page: pageActive,
					limit: this.filters.limit,
					nameOrDocument: this.filter.nameOrDocument || '',
				},
				this.currentPagination
			);
		},
		onChangeLimitPerPageTable(limit: number) {
			this.filters.limit = limit;
			this.currentPagination = 1;
			this.$emit(
				'onRequest',
				{ page: 0, limit: limit, nameOrDocument: this.filter.nameOrDocument || '' },
				this.currentPagination
			);
		},
	},
	watch: {
		assignorsDataUserRequestStatus(newValue) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.$router.push({
					path: `/admin/cadastros/originadores/${this.idClicked}/editar`,
				});
			}
		},
		paginationPageActive(newValue) {
			this.currentPagination = newValue;
		},
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-cadastros-assignors-list', 1, (0));
</style>
