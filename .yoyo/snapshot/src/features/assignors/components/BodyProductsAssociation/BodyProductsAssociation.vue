<template>
	<farm-container>
		<HeaderForm v-if="!isError" :data="dataHeaderForm" withLine />
		<TitlePageForm
			v-if="!isError"
			value="Produto"
			noPipe
			:label="!isEdit ? 'Associar' : 'Editar'"
		/>
		<farm-row v-if="!isError">
			<farm-col cols="12" md="4">
				<farm-label for="form-address-type" required> Produto </farm-label>
				<farm-select-auto-complete
					id="form-address-type"
					item-text="label"
					item-value="value"
					v-model="form.productId"
					:items="productsList"
					:rules="[rules.required]"
					:disabled="isEdit"
				/>
			</farm-col>
			<farm-col cols="12" md="8" v-if="showFields">
				<ListInformation :data="dataListInformation" class="mt-5" />
			</farm-col>
			<farm-col cols="12" md="4" v-if="showFields">
				<farm-label for="form-wallet" placeholder="Selecione"> Carteira </farm-label>
				<farm-select-auto-complete
					id="form-wallet"
					item-text="label"
					item-value="value"
					v-model="form.taxonomyWalletId"
					:items="walletList"
				/>
			</farm-col>
			<farm-col cols="12" md="4" v-if="showFields">
				<farm-label for="form-concentration-percentage">
					Percentual de Concentração
					<farm-tooltip>
						Percentual de Concentração aprovado por exceção.
						<template v-slot:activator>
							<farm-icon size="sm" color="gray">help-circle</farm-icon>
						</template>
					</farm-tooltip>
				</farm-label>
				<farm-textfield-v2
					id="form-concentration-percentage"
					maxlength="4"
					v-model="form.concentrationPercentage"
					:rules="[rules.percentage]"
				/>
			</farm-col>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="isValid"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { notification, RequestStatusEnum, stripTags } from '@farm-investimentos/front-mfe-libs-ts';

import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import TitlePageForm from '@/components/TitlePageForm';
import HeaderForm, { HeaderFormTypes, headerFormModel } from '@/components/HeaderForm';
import ListInformation from '@/components/ListInformation';
import { EDIT } from '@/constants';
import { parseConcentrationPercentage } from '@/helpers/parseConcentrationPercentage';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';

import { headersPages, listBodyProductsAssociation } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		TitlePageForm,
		ListInformation,
		HeaderForm,
		FooterForm,
	},
	props: {
		typeProduct: {
			type: String,
		},
	},
	data() {
		return {
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			dataListInformation: listBodyProductsAssociation,
			labelButton: 'Associar',
			disabledButtonFooter: null,
			form: {
				concentrationPercentage: '',
				productId: null,
				taxonomyWalletId: '',
			},
			walletList: [],
			productsList: [],
			lastSearchFilters: { page: 0, limit: 10 },
			filters: {
				page: 0,
				limit: 10,
			},
			showFields: false,
			productNameSelected: '',
			peopleId: 0,
			rules: {
				required: value => !!value || 'Campo obrigatório',
				percentage: value => {
					if (value.length === 0) {
						return true;
					}
					const REGEXP = /^(\d+,)*(\d+)$/;
					if (!REGEXP.test(value)) {
						return 'Valor inválido';
					}
					if (parseInt(value, 10) < 0 || parseInt(value, 10) > 100) {
						return 'Campo inválido';
					}
					return true;
				},
			},
		};
	},
	computed: {
		...mapGetters('cadastros', {
			originatorAssignorHeaderData: 'originatorAssignorHeaderData',
			originatorAssignorHeaderRequestStatus: 'originatorAssignorHeaderRequestStatus',
			assignorsProductsAssociationById: 'assignorsProductsAssociationById',
			assignorsProductsAssociationByIdRequestStatus:
				'assignorsProductsAssociationByIdRequestStatus',
			assignorsNotRelatedProducts: 'assignorsNotRelatedProducts',
			assignorsNotRelatedProductsRequestStatus: 'assignorsNotRelatedProductsRequestStatus',
			saveAssignorsProductsAssociationtRequestStatus:
				'saveAssignorsProductsAssociationtRequestStatus',
			taxonomyWalletListRequestStatus: 'taxonomyWalletListRequestStatus',
			taxonomyWalletList: 'taxonomyWalletList',
		}),
		isLoading(): boolean {
			const requestStatus = [
				this.originatorAssignorHeaderRequestStatus,
				this.assignorsProductsAssociationByIdRequestStatus,
				this.assignorsNotRelatedProductsRequestStatus,
				this.saveAssignorsProductsAssociationtRequestStatus,
				this.taxonomyWalletListRequestStatus,
			];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [
				this.assignorsProductsAssociationByIdRequestStatus.type,
				this.assignorsNotRelatedProductsRequestStatus.type,
				this.originatorAssignorHeaderRequestStatus.type,
				this.taxonomyWalletListRequestStatus.type,
			];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
		currentProduct(): number {
			return this.$route.params.productId;
		},
		isEdit(): boolean {
			return this.typeProduct === EDIT;
		},
		isValid(): boolean {
			return this.form.productId !== null;
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.showFields = true;
			this.labelButton = 'Salvar';
		}
		this.getOriginatorsAssignorsHeader({
			document: this.currentDocument,
		});
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getOriginatorsAssignorsHeader: 'getOriginatorsAssignorsHeader',
			getAssignorsProductsAssociationById: 'getAssignorsProductsAssociationById',
			getAssignorsNotRelatedProducts: 'getAssignorsNotRelatedProducts',
			saveAssignorsProductsAssociation: 'saveAssignorsProductsAssociation',
			getTaxonomyWalletList: 'getTaxonomyWalletList',
		}),
		doSearch() {
			const productId = this.currentProduct;
			const document = this.currentDocument;
			this.lastSearchFilters = { ...this.filters };
			if (this.isEdit) {
				this.getAssignorsProductsAssociationById({
					document,
					productId,
				});
				this.getTaxonomyWalletList({ productId });
				return;
			}
			this.getAssignorsNotRelatedProducts({ document: this.currentDocument });
		},
		reload(): void {
			this.getOriginatorsAssignorsHeader({
				document: this.currentDocument,
			});
			this.doSearch();
		},
		onSave(): void {
			const payload = this.createPayload();
			const type = this.isEdit ? 'edit' : 'new';
			this.saveAssignorsProductsAssociation({ type, payload });
		},
		createPayload() {
			const id = this.isEdit ? this.peopleId : 0;
			const document = this.currentDocument;
			const taxonomyWalletId = this.form.taxonomyWalletId
				? Number(this.form.taxonomyWalletId)
				: null;
			const concentrationPercentage = this.form.concentrationPercentage;
			const productId = this.form.productId;

			return {
				id,
				document,
				productId,
				concentrationPercentage,
				taxonomyWalletId,
			};
		},
		onCancel(): void {
			const document = this.currentDocument;
			const path = `/admin/cadastros/originadores/${document}/editar?path=produtos`;
			this.$router.push({
				path,
			});
		},
		updatedHeader(): void {
			const data = this.originatorAssignorHeaderData.content;
			this.peopleId = data.id;
			const header = {
				title: data.name,
				listIcons: [data.id, [data.document, data.raiz]],
			};
			this.dataHeaderForm = parseDataHeader(header, headersPages);
		},
		updatedForm(data): void {
			this.productsList = [
				{
					label: data.name,
					value: data.id.toString(),
				},
			];
			this.form = {
				...this.form,
				concentrationPercentage: parseConcentrationPercentage(
					data.concentrationPercentage,
					'',
					''
				),
				productId: data.id,
				taxonomyWalletId: data.walletId,
			};
		},
		updateWalletList(data): void {
			this.walletList = data.map(x => ({ ...x, label: x.name, value: x.id.toString() }));
		},
		updatedProductInfo(data): void {
			this.productNameSelected = data.name;
			this.updatedDataListInformation([data.id, data.type, data.status || 'N/A']);
		},
		updatedProducts(data): void {
			const productsList = data.map(product => ({ label: product.name, value: product.id }));
			this.productsList = productsList;
		},
		backPage(): void {
			setTimeout(() => {
				this.onCancel();
			}, 1500);
		},
		createMessage(): string {
			if (this.isEdit) {
				return `Associação ao produto atualizada com sucesso!`;
			}
			return `Originador/Cedente associado com sucesso ao produto <b>${stripTags(
				this.productNameSelected
			)}</b>.`;
		},
		createMessageError(): string {
			if (this.isEdit) {
				return `Não foi possível fazer a atualização`;
			}
			return `Não foi possível associar o produto ao Originador/Cedente <b>${stripTags(
				this.productNameSelected
			)}</b>.`;
		},

		updatedDataListInformation(data): void {
			const newValues = listBodyProductsAssociation.map((item, index) => {
				return {
					...item,
					value: data[index],
				};
			});
			this.dataListInformation = newValues;
		},
	},
	watch: {
		originatorAssignorHeaderRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeader();
			}
		},
		assignorsProductsAssociationByIdRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				const data = this.assignorsProductsAssociationById.content;
				this.updatedForm(data);
				this.updatedProductInfo(data);
			}
		},
		taxonomyWalletListRequestStatus(newValue: string): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				const data = this.taxonomyWalletList;
				this.updateWalletList(data);
			}
		},
		assignorsNotRelatedProductsRequestStatus(newValue) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				const data = this.assignorsNotRelatedProducts.content;
				this.updatedProducts(data);
			}
		},
		saveAssignorsProductsAssociationtRequestStatus(newValue) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				notification(RequestStatusEnum.SUCCESS, this.createMessage());
				this.backPage();
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, this.createMessageError());
			}
		},
		'form.productId'(newValue) {
			if (newValue !== null && !this.isEdit) {
				this.showFields = true;
				const newProductInfo = this.assignorsNotRelatedProducts.content.filter(
					product => product.id === this.form.productId
				);
				this.updatedProductInfo(...newProductInfo);
				this.getTaxonomyWalletList({ productId: this.form.productId });
			}
		},
	},
});
</script>
