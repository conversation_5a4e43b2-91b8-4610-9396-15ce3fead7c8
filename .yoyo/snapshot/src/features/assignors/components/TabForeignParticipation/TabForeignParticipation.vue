<template>
	<farm-box>
		<PageHome domain="originadores" @onUpdateFooterFormData="onUpdateFooterFormData" />
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import PageHome from '@/modules/foreignParticipation/components/PageHome';
import { FooterFormDataType } from '@/components/FooterForm';

export default defineComponent({
	components: { PageHome },
	computed: {
		...mapGetters('cadastros', {
			originatorAssignorHeaderData: 'originatorAssignorHeaderData',
			originatorAssignorHeaderRequestStatus: 'originatorAssignorHeaderRequestStatus',
		}),
		isLoading(): boolean {
			const requestStatus = [this.originatorAssignorHeaderRequestStatus];
			return requestStatus.includes(RequestStatusEnum.START);
		},
		isError(): boolean {
			const requestStatus = [this.originatorAssignorHeaderRequestStatus.type];
			return requestStatus.includes(RequestStatusEnum.ERROR);
		},
		currentDocument(): string {
			return this.$route.params.document;
		},
	},
	methods: {
		...mapActions('cadastros', {
			getOriginatorsAssignorsHeader: 'getOriginatorsAssignorsHeader',
		}),
		doSearch(): void {
			this.getOriginatorsAssignorsHeader({
				document: this.currentDocument,
			});
		},
		reload(): void {
			this.doSearch();
		},
		onUpdateFooterFormData(data: FooterFormDataType): void {
			this.$emit('onUpdateFooterFormData', data);
		},
		updatedHeaderHome(data): void {
			this.$emit('onUpdateHeaderForm', {
				title: data.name,
				listIcons: [data.id, [data.document, data.raiz]],
			});
			this.currentPeopleId = data.id;
		},
	},
	watch: {
		originatorAssignorHeaderRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.updatedHeaderHome(this.originatorAssignorHeaderData.content);
			}
		},
	},
	mounted() {
		this.doSearch();
	},
});
</script>
