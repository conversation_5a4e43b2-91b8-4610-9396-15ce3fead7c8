<template>
	<farm-container>
		<farm-row v-if="!isError">
			<farm-col md="6" cols="12">
				<farm-form-mainfilter
					:showFilters="filter"
					:label="$tc('assignors.filter.mainLabel')"
					@onInputChange="filterInputChangedCustom"
					@onClick="showFilters"
				/>
			</farm-col>
			<farm-col align="right" md="6" sm="12" cols="12" lg="6">
				<farm-btn-confirm
					dense
					class="farm-btn--responsive mt-8"
					customIcon="plus"
					v-if="canWrite"
					:icon="true"
					:title="$tc('assignors.new')"
					@click="openModalDocument"
				>
					{{ $tc('assignors.new') }}
				</farm-btn-confirm>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300" v-if="!isError" class="pb-6">
			<Filters v-show="filter" key="filters" @onApply="searchListener" />
		</collapse-transition>
		<OriginatorTable
			v-if="!isError"
			:data="dataTable"
			:filter="filterTable"
			:paginationTotalPages="paginationTotalPages"
			:paginationPageActive="currentPage"
			@onRequest="onRequest"
		/>
		<ModalDocument
			v-model="modalDocument.show"
			title="Novo Originador/Cedente"
			:loading="modalDocument.loading"
			@onContinues="onConfirmModal"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="reload" />
		</div>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import {
	pageable,
	RequestStatusEnum,
	notification,
	isValidCNPJ,
	stripTags,
} from '@farm-investimentos/front-mfe-libs-ts';

import ModalDocument from '@/components/ModalDocument';
import { cleanDocument } from '@/helpers/masks';
import storage from '@/helpers/storage';

import OriginatorTable from '../OriginatorTable';
import Filters from '../Filters';
import { message } from '../../configurations/dialogMessages';

export default defineComponent({
	components: {
		Filters,
		ModalDocument,
		OriginatorTable,
	},
	mixins: [pageable],
	data() {
		return {
			dataTable: [],
			lastSearchFilters: {},
			filter: false,
			filterInputKey: 'search',
			filters: {
				page: 0,
				limit: 10,
			},
			idClicked: 0,

			modalDocument: {
				show: false,
				loading: false,
				value: '',
			},
			paginationTotalPages: 0,
			filterTable: {},
		};
	},
	computed: {
		...mapGetters('cadastros', {
			assignorsListResult: 'assignorsListResult',
			assignorsListRequestStatus: 'assignorsListRequestStatus',
			documentDataCheck: 'documentDataCheck',
			checkDocumentRequestStatus: 'checkDocumentRequestStatus',
		}),
		isLoading() {
			return this.assignorsListRequestStatus === RequestStatusEnum.START;
		},
		isError() {
			return this.assignorsListRequestStatus.type === RequestStatusEnum.ERROR;
		},
	},
	mounted() {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			fetchAssignors: 'fetchAssignors',
			getCheckDocument: 'getCheckDocument',
		}),
		doSearch() {
			this.lastSearchFilters = { ...this.filters };
			this.filterTable = { ...this.filters };
			this.fetchAssignors({
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		reload() {
			this.doSearch();
		},
		onRequest(filtersActive, page) {
			this.filters = {
				...this.filters,
				...filtersActive,
				[this.filterInputKey]: this.filters[this.filterInputKey],
				page: page - 1,
			};
			this.fetchAssignors({
				filters: { ...this.filters },
			});
		},
		showFilters() {
			this.filter = !this.filter;
		},
		openModalDocument() {
			this.modalDocument.show = true;
		},
		onConfirmModalRedirect() {
			this.$router.push({
				path: `/admin/cadastros/originadores/${this.modalDocument.value}/editar?path=dados`,
			});
		},
		onConfirmModal(document) {
			const payload = {
				document: document,
			};
			this.modalDocument.value = document;
			this.modalDocument.loading = true;
			this.getCheckDocument(payload);
		},
		filterInputChangedCustom(value: string) {
			if (value.length >= 3 || value === '') {
				this.filters = {
					...this.filters,
					[this.filterInputKey]: value,
					page: 0,
				};
				const dataValue = cleanDocument(value);
				if (dataValue.length > 12) {
					if (isValidCNPJ(dataValue)) {
						this.filters = {
							...this.filters,
							[this.filterInputKey]: dataValue,
						};
					}
				}
				this.fetchAssignors({
					filters: { ...this.filters },
				});
			}
		},
		createDialog(message) {
			this.$dialog
				.confirm(
					{
						body: stripTags(message),
						title: 'Novo Originador/Cedente',
					},
					{
						html: true,
						okText: 'Sim',
						cancelText: 'Cancelar',
					}
				)
				.then(() => {
					this.onConfirmModalRedirect();
				})
				.catch(() => {});
		},
	},
	watch: {
		assignorsListRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.dataTable = this.assignorsListResult.items.content;
				this.paginationTotalPages = this.assignorsListResult.items.totalPages;
			}
		},
		checkDocumentRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				if (parseInt(this.documentDataCheck.id, 10) === 0) {
					storage.clear();
					if (this.documentDataCheck.hasRegisteredRaiz === true) {
						storage.set('branch', 'true');
					}
					this.$router.push({
						path: `/admin/cadastros/originadores/${this.modalDocument.value}/novo`,
					});
					return;
				}
				this.idClicked = this.documentDataCheck.id;
				this.modalDocument.show = false;
				this.modalDocument.loading = false;
				const isAssignorPaper = this.documentDataCheck.documentsType.filter(item => {
					return item === 'ORIGINADOR/CEDENTE';
				});
				const index = isAssignorPaper.length === 1 ? 0 : 1;
				this.createDialog(
					index === 0 ? message.hasPaperRegister : message.otherPaperRegister
				);
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				this.modalDocument.show = false;
				this.modalDocument.loading = false;
				notification(RequestStatusEnum.ERROR, 'Erro ao consultar o CNPJ');
			}
		},
	},
});
</script>
