<template>
	<farm-container>
		<HeaderForm v-if="isEdit" :data="dataHeaderForm" />
		<farm-row extra-decrease>
			<farm-tabs
				ref="tabEl"
				:allowUserChange="true"
				:showCounter="false"
				:tabs="tabs"
				class="mt-n6 mb-6"
				@update="updateTab"
			/>
		</farm-row>
		<TabData
			v-if="currentTab === DATA"
			:isEdit="isEdit"
			@onDisabledButtonFooter="onDisabledButtonFooter"
			@onSubmit="onSubmit"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>

		<TabDocuments
			v-if="currentTab === DOCUMENTS"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabCorporateStructure
			v-if="currentTab === CORPORATE_STRUCTURE"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabPrograms v-if="currentTab === PROGRAMS" @onUpdateHeaderForm="onUpdateHeaderForm" />

		<TabProducts
			v-if="currentTab === PRODUCTS"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>

		<TabForeignParticipation
			v-if="currentTab === FOREIGNPARTICIPATION"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<TabBankData
			v-if="currentTab === BANK_DATA"
			@onUpdateFooterFormData="onUpdateFooterFormData"
			@onUpdateHeaderForm="onUpdateHeaderForm"
		/>
		<FooterForm
			:data="dataFooterForm"
			:hiddenButton="isNotData"
			:isDisabledButton="disabledButtonFooter"
			:labelButton="labelButton"
			:showLayoutData="isEdit"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { TabTypes } from '@/types';
import {
	BANK_DATA,
	CORPORATE_STRUCTURE,
	DATA,
	DOCUMENTS,
	EDIT,
	FOREIGNPARTICIPATION,
	PRODUCTS,
	PROGRAMS,
} from '@/constants';
import FooterForm, { FooterFormDataType, modelFooterFormData } from '@/components/FooterForm';
import HeaderForm, { headerFormModel, HeaderFormTypes } from '@/components/HeaderForm';
import { createObject } from '@/helpers/createObject';
import { parseDataHeader } from '@/helpers/parseDataHeaderForm';

import TabData from '../TabData';
import TabDocuments from '../TabDocuments';
import TabPrograms from '../TabPrograms';
import TabProducts from '../TabProducts';
import TabForeignParticipation from '../TabForeignParticipation';
import TabCorporateStructure from '../TabCorporateStructure';
import TabBankData from '../TabBankData';
import { tabDefault, tabEdit } from '../../configurations/tabs';
import { headersPages } from '../../configurations/headersPages';

export default defineComponent({
	components: {
		FooterForm,
		HeaderForm,
		TabData,
		TabDocuments,
		TabPrograms,
		TabProducts,
		TabForeignParticipation,
		TabCorporateStructure,
		TabBankData,
	},
	props: {
		type: {
			type: String,
			require: true,
		},
	},
	data() {
		return {
			labelButton: 'Cadastrar',
			tabs: tabDefault,
			currentTab: DATA,
			dataFooterForm: createObject<FooterFormDataType>(modelFooterFormData),
			dataHeaderForm: createObject<HeaderFormTypes>(headerFormModel),
			dispatchSubmit: null,
			disabledButtonFooter: null,
			DATA,
			DOCUMENTS,
			PROGRAMS,
			PRODUCTS,
			FOREIGNPARTICIPATION,
			CORPORATE_STRUCTURE,
			BANK_DATA,
			dataHeader: {},
		};
	},
	computed: {
		isEdit(): boolean {
			return this.type === EDIT;
		},
		isNotData(): boolean {
			return this.currentTab !== DATA;
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.configurationEdit();
		}
	},
	methods: {
		updateTab(item: TabTypes): void {
			if (!item) {
				return;
			}
			if (item.path === this.$route.query.path) {
				return;
			}
			this.$router.replace({
				query: {
					path: item.path,
				},
			});
			this.currentTab = item.path;
		},
		configurationEdit(): void {
			this.labelButton = 'Salvar';
			this.tabs = tabEdit;
			let path = this.$route.query.path;
			if (!path) {
				path = DATA;
				this.$router.replace({
					path: this.$route.path,
					query: { path },
				});
			}
			this.currentTab = path;
			const index = this.tabs.findIndex(tab => path === tab.path);
			if (this.$refs.tabEl && this.$refs.tabEl.toIndex) {
				this.$refs.tabEl.toIndex(index);
			}
		},
		onCancel(): void {
			this.$router.push({
				path: `/admin/cadastros/originadores`,
			});
		},
		onSubmit(call: () => void): void {
			this.dispatchSubmit = call;
		},
		onSave(): void {
			this.dispatchSubmit();
		},
		onUpdateFooterFormData(data: FooterFormDataType): void {
			this.dataFooterForm = data;
		},
		onDisabledButtonFooter(value: boolean): void {
			this.disabledButtonFooter = value;
		},
		onUpdateHeaderForm(data): void {
			this.dataHeaderForm = parseDataHeader(data, headersPages);
		},
	},
});
</script>
