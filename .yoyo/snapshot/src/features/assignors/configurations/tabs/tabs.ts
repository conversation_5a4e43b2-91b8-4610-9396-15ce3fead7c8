import { TabTypes } from '@/types';

import {
	BANK_DATA,
	CORPORATE_STRUCTURE,
	DATA,
	DOCUMENTS,
	FOREIGNPARTICIPATION,
	PRODUCTS,
	PROGRAMS,
} from '@/constants';

export const tabDefault: Array<TabTypes> = [
	{
		name: 'dados',
		path: DATA,
	},
];

export const tabEdit: Array<TabTypes> = [
	...tabDefault,
	{
		name: '<PERSON>ume<PERSON><PERSON>',
		path: DOCUMENTS,
	},
	{
		name: 'QUADRO SOCIETÁRIO',
		path: CORPORATE_STRUCTURE,
	},
	{
		name: 'PARTICIPAÇÃO ESTRANGEIRA',
		path: FOREIGNPARTICIPATION,
	},
	{
		name: '<PERSON><PERSON> Ban<PERSON>',
		path: BANK_DATA,
	},
	{
		name: 'Programas',
		path: PROGRAMS,
	},
	{
		name: 'Produ<PERSON>',
		path: PRODUCTS,
	},
];
