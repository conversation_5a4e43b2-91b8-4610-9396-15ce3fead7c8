import { useGetter } from '@/composibles';
import { computed, ref } from 'vue';

const formFilters = ref({
	productName: '',
	commercialProductId: null,
	campaignCommercialProductStatus: null,
	hasMargin: null,
});

export default function useMarginListFilters(callback?: Function) {
	const isShowingFilters = ref(false);
	const filterFormComponent = ref();

	const campaignMarginListPageable = computed(useGetter('margins', 'campaignMarginListPageable'));
	const filters = computed(() =>
		Object.fromEntries(
			Object.entries({
				produtoName: formFilters.value.productName,
				commercialProductId: formFilters.value.commercialProductId,
				campaignCommercialProductStatus: formFilters.value.campaignCommercialProductStatus,
				marginComercial: formFilters.value.hasMargin,
				page: 0,
				limit: campaignMarginListPageable.value.pageSize,
			}).filter(([key, value]) => value !== null && Boolean(String(value)))
		)
	);

	const updateListFilters = () => {
		if (!callback) return;

		callback({
			query: filters.value,
		});
	};
	const updateNameFilter = (value: string) => {
		formFilters.value.productName = value;

		updateListFilters();
	};
	const clearFilters = () => {
		formFilters.value.productName = '';
		filterFormComponent.value.reset();

		updateListFilters();
	};
	const filterByProductsWithNoMargin = () => {
		formFilters.value.hasMargin = 2;

		updateListFilters();
	};
	const toggleFilters = () => {
		isShowingFilters.value = !isShowingFilters.value;
	};

	return {
		formFilters,
		isShowingFilters,
		filterFormComponent,
		filters,
		toggleFilters,
		clearFilters,
		updateListFilters,
		updateNameFilter,
		filterByProductsWithNoMargin,
	};
}
