import {
	RequestStatusEnum,
	errorBuilder,
	fetchDefaultParser,
	fetchDefaultParserPagination,
} from '@farm-investimentos/front-mfe-libs-ts';

import { getFinancialVehicle, getCommercialProducts } from '@/services/registerV3';

import {
	exportCampaignMarginList as exportCampaignMarginListService,
	exportCampaignMarginSpreadsheet as exportCampaignMarginSpreadsheetService,
	getCampaignMarginDetail as getCampaignMarginDetailService,
	getCampaignMarginHistoryList as getCampaignMarginHistoryListService,
	getCampaignMarginList as getCampaignMarginListService,
	importCampaignMargin as importCampaignMarginService,
} from '../services';

import type { GetFinancialVehiclesRequest } from '@/types';
import type {
	GetCampaignMarginDetailRequest,
	GetCampaignMarginHistoryListRequest,
	GetCampaignMarginListRequest,
	ImportCampaignMarginListRequest,
	GetCommercialProductsListRequest,
} from '../services/types';

export default {
	async getCampaignMarginList({ commit }, request: GetCampaignMarginListRequest) {
		commit('setCampaignMarginListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getCampaignMarginListService(request);

			fetchDefaultParserPagination(commit, data, null, 'CampaignMarginList');
			commit('setCampaignMarginListPageable', data.data.pageable);
			commit('setCampaignMarginListAlert', data.data?.alert);
		} catch (error) {
			commit('setCampaignMarginListRequestStatus', errorBuilder(error));
		}
	},

	async getCampaignMarginDetail({ commit }, request: GetCampaignMarginDetailRequest) {
		commit('setCampaignMarginDetailRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getCampaignMarginDetailService(request);

			fetchDefaultParser(commit, data, null, 'CampaignMarginDetail');
		} catch (error) {
			commit('setCampaignMarginDetailRequestStatus', errorBuilder(error));
		}
	},

	async exportCampaignMarginList({ commit }) {
		commit('setExportCampaignMarginListRequestStatus', RequestStatusEnum.START);
		try {
			await exportCampaignMarginListService();

			commit('setExportCampaignMarginListRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setExportCampaignMarginListRequestStatus', errorBuilder(error));
		}
	},

	async getCampaignMarginHistoryList({ commit }, request: GetCampaignMarginHistoryListRequest) {
		commit('setCampaignMarginHistoryListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getCampaignMarginHistoryListService(request);

			fetchDefaultParserPagination(commit, data, null, 'CampaignMarginHistoryList');
			commit('setCampaignMarginHistoryListPageable', data.data.pageable);
		} catch (error) {
			commit('setCampaignMarginHistoryListRequestStatus', errorBuilder(error));
		}
	},

	async importCampaignMargin({ commit }, request: ImportCampaignMarginListRequest) {
		commit('setImportCampaignMarginDataRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await importCampaignMarginService(request);

			fetchDefaultParser(commit, data, null, 'ImportCampaignMarginData');
		} catch (error) {
			const validationErrors = error.response?.data?.errors;
			const hasValidationErrors = Boolean(validationErrors);

			if (hasValidationErrors) {
				error.response.data.message = validationErrors.join('\n');

				delete error.response.data.errors;
			}

			commit('setImportCampaignMarginDataRequestStatus', errorBuilder(error));
		}
	},

	async exportCampaignMarginSpreadsheet({ commit }) {
		commit('setExportCampaignMarginListRequestStatus', RequestStatusEnum.START);
		try {
			await exportCampaignMarginSpreadsheetService();

			commit('setExportCampaignMarginListRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setExportCampaignMarginListRequestStatus', errorBuilder(error));
		}
	},

	async getVehicleList({ commit }, request: GetFinancialVehiclesRequest) {
		commit('setVehicleListRequestStatus', RequestStatusEnum.START);
		try {
			const response = await getFinancialVehicle(request.query);
			fetchDefaultParserPagination(commit, response, null, 'VehicleList');
		} catch (error) {
			commit('setVehicleListRequestStatus', errorBuilder(error));
		}
	},
	async getCommercialProductsList({ commit }, request: GetCommercialProductsListRequest) {
		commit('setCommercialProductsListRequestStatus', RequestStatusEnum.START);
		try {
			const response = await getCommercialProducts(request.query);
			fetchDefaultParserPagination(commit, response, null, 'CommercialProductsList');
		} catch (error) {
			commit('setCommercialProductsListRequestStatus', errorBuilder(error));
		}
	},
};
