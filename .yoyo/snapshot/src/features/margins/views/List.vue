<template>
	<farm-container>
		<ListFilters>
			<template #actions>
				<farm-col class="d-md-flex justify-end mt-7" md="6">
					<farm-btn outlined @click="handleExport">
						<farm-icon class="ma-0">download-outline</farm-icon>
						Exportar
					</farm-btn>

					<farm-btn v-if="canWrite" class="ml-2" @click="openModal('import')">
						<farm-icon class="ma-0">upload-outline</farm-icon>
						Importar
					</farm-btn>
				</farm-col>
			</template>
		</ListFilters>

		<farm-alertbox v-if="campaignMarginListAlert" color="warning" class="mb-6">
			<div class="d-flex align-center justify-space-between">
				<farm-subtitle
					tag="p"
					color="warning"
					color-variation="darken"
					class="d-flex align-center"
				>
					<farm-icon
						class="mr-3"
						color="warning"
						variation="darken"
						role="presentation"
						size="md"
					>
						alert-circle-outline
					</farm-icon>
					Não foram encontradas margens comerciais para alguns produtos/campanhas
				</farm-subtitle>

				<button
					type="button"
					class="text-decoration-underline"
					@click="filterByProductsWithNoMargin"
				>
					<farm-typography weight="700" color="warning" color-variation="darken">
						Clique para visualizar
					</farm-typography>
				</button>
			</div>
		</farm-alertbox>

		<template v-if="campaignMarginList.length">
			<MarginListCard
				v-for="margin in campaignMarginList"
				class="mb-6"
				:key="margin.productId"
				:item="margin"
			/>

			<farm-datatable-paginator
				v-if="campaignMarginListPageable"
				class="mt-12"
				:has-gutter="false"
				:page="presentableFilterValues.pageNumber"
				:total-pages="filters.totalPages"
				:initial-limit-per-page="filters.pageSize"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
			/>
		</template>
		<template v-else-if="!isLoading">
			<farm-emptywrapper />
		</template>

		<MarginDetailModal
			v-if="modals.detail.status"
			:value="modals.detail.status"
			@on-close="closeModal('detail')"
		/>

		<MarginHistoryModal
			v-if="modals.history.status"
			:value="modals.history.status"
			@on-close="closeModal('history')"
		/>

		<MarginImportModal
			v-if="modals.import.status"
			:value="modals.import.status"
			@on-close="closeModal('import')"
		/>

		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-container>
</template>

<script lang="ts">
import { usePageable } from '@/features/pricing/composables';
import { defineAsyncComponent, defineComponent, onBeforeMount, watch } from 'vue';

import ListFilters from '../components/ListFilters';
import MarginListCard from '../components/MarginListCard';
import { useMargin, useMarginListFilters } from '../composables';

import type { GetCampaignMarginListRequest } from '../services/types';

export default defineComponent({
	components: {
		ListFilters,
		MarginListCard,
		MarginDetailModal: defineAsyncComponent(() => import('../components/MarginDetailModal')),
		MarginHistoryModal: defineAsyncComponent(() => import('../components/MarginHistoryModal')),
		MarginImportModal: defineAsyncComponent(() => import('../components/MarginImportModal')),
	},
	setup() {
		const {
			campaignMarginList,
			campaignMarginListPageable,
			campaignMarginListAlert,
			isLoading,
			modals,
			handleExport,
			openModal,
			closeModal,
			fetchMarginList,
			fetchCommercialProductsList,
		} = useMargin();
		const { filters: campaignFilters, filterByProductsWithNoMargin } =
			useMarginListFilters(fetchMarginList);

		const {
			filters,
			presentableFilterValues,
			setPageableFilters,
			onChangePage,
			onChangePageLimit,
		} = usePageable(
			filters => {
				let query = {
					...campaignFilters.value,
					...Object.fromEntries(filters),
				};

				fetchMarginList({
					query,
				});
			},
			campaignMarginListPageable,
			{}
		);

		watch(campaignMarginListPageable, () => {
			setPageableFilters(campaignMarginListPageable.value);
		});

		onBeforeMount(() => {
			const request: GetCampaignMarginListRequest = {
				query: {
					page: '0',
					limit: '10',
				},
			};

			fetchMarginList(request);

			fetchCommercialProductsList({
				query: 'page=0&limit=50&order=ASC&orderby=name',
			});
		});

		return {
			filters,
			presentableFilterValues,
			campaignMarginList,
			campaignMarginListPageable,
			campaignMarginListAlert,
			isLoading,
			modals,
			openModal,
			closeModal,
			handleExport,
			onChangePage,
			onChangePageLimit,
			filterByProductsWithNoMargin,
		};
	},
});
</script>
