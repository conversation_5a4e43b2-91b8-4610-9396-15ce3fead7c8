<template>
	<farm-modal
		class="margin-import-modal"
		:value="value"
		:offset-top="52"
		:offset-bottom="68"
		@input="$emit('on-close')"
	>
		<template #header>
			<farm-dialog-header title="Importar" :has-close-icon="false" />
		</template>

		<template #content>
			<farm-heading type="6" class="mb-4">Importação de arquivo</farm-heading>

			<div class="d-flex align-center mb-4">
				<farm-bodytext type="2" variation="regular" class="mr-4 mb-0">
					Não possui a Planilha Modelo?
				</farm-bodytext>
				<farm-btn plain @click="fetchMarginSpreadsheetModel">
					<farm-icon>download-outline</farm-icon>
					Baixar Planilha Modelo
				</farm-btn>
			</div>

			<farm-multiple-filepicker
				ref="filePicker"
				:max-file-size="10"
				:max-files-number="1"
				:accepted-file-types="EXCEL_ACCEPT_FILETYPE"
				@onFileChange="onFileChange"
				@onMaxFileSizeWarning="setMaxSizeExceededWarning"
				@onInvalidFiles="setInvalidFileWarning"
			/>

			<template v-if="hasFilePickerWarning || hasErrorMessage">
				<farm-alertbox
					icon="alert-circle"
					color="error"
					v-if="filePickerWarnings.isMaxSizeExceeded"
				>
					Você excedeu o limite permitido de 10mb. Envie arquivos de até 10mb para
					prosseguir com a solicitação.
				</farm-alertbox>

				<farm-alertbox
					icon="alert-circle"
					color="error"
					v-if="filePickerWarnings.isFileInvalid"
				>
					O arquivo não pode ser enviado. Envie arquivos no formato: .xls, .xlsx
				</farm-alertbox>

				<template v-if="hasErrorMessage">
					<farm-alertbox icon="alert-circle" color="error" class="my-4">
						Houve erro na validação de algumas linhas.
					</farm-alertbox>

					<farm-textarea disabled rows="6" :value="errorMessage" />

					<div class="d-flex">
						<farm-copytoclipboard
							class="clipboard-copy-button ml-auto"
							:to-copy="errorMessage"
							:is-icon="false"
						/>
					</div>
				</template>
			</template>
		</template>

		<template #footer>
			<footer class="d-flex align-center justify-end pa-4">
				<farm-btn plain @click="$emit('on-close')">Voltar</farm-btn>
				<farm-btn :disabled="isImportButtonDisabled" @click="handleImport">
					<farm-icon>send</farm-icon>
					Importar
				</farm-btn>
			</footer>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { getCurrentInstance, defineComponent, onBeforeUnmount, ref, computed, watch } from 'vue';

import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { EXCEL_ACCEPT_FILETYPE } from '@/constants';
import { useStore } from '@/composibles';

import { useMargin, useMarginListFilters } from '../../composables';

import type {
	GetCampaignMarginListRequest,
	ImportCampaignMarginListRequest,
} from '../../services/types';

export default defineComponent({
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup(props, { emit }) {
		const internalInstance: any = getCurrentInstance().proxy;

		const {
			importCampaignMarginDataRequestStatus,
			fetchMarginList,
			handleImport,
			fetchMarginSpreadsheetModel,
		} = useMargin();
		const { filters } = useMarginListFilters();
		const store = useStore();

		const filePicker = ref();
		const files = ref([] as File[]);
		const filePickerWarnings = ref({
			isFileInvalid: false,
			maxFileSizeExceeded: false,
		});

		const request = computed(() => {
			const formData = new FormData();
			formData.append('file', files.value[0], files.value[0].name);

			return {
				payload: formData,
			} as ImportCampaignMarginListRequest;
		});
		const hasFile = computed(() => files.value.length > 0);
		const hasFilePickerWarning = computed(() =>
			Object.values(filePickerWarnings.value).some(Boolean)
		);
		const errorMessage = computed(() => importCampaignMarginDataRequestStatus.value?.message);
		const hasErrorMessage = computed(
			() =>
				importCampaignMarginDataRequestStatus.value?.type === 'ERROR' &&
				!!errorMessage.value
		);
		const isImportButtonDisabled = computed(
			() => !hasFile.value || hasFilePickerWarning.value || hasErrorMessage.value
		);

		const setMaxSizeExceededWarning = () => {
			filePickerWarnings.value.maxFileSizeExceeded = true;
		};
		const setInvalidFileWarning = () => {
			filePickerWarnings.value.isFileInvalid = true;
		};
		const resetRequestStatus = (): void => {
			store.commit(
				'margins/setImportCampaignMarginDataRequestStatus',
				RequestStatusEnum.IDLE
			);
		};
		const resetWarningsAndErrors = () => {
			Object.keys(filePickerWarnings.value).forEach(key => {
				filePickerWarnings.value[key] = false;
			});
		};
		const onFileChange = (pickerFiles: File[]) => {
			resetWarningsAndErrors();
			if (pickerFiles.length) {
				resetRequestStatus();
			}

			files.value = pickerFiles;
		};

		watch(importCampaignMarginDataRequestStatus, newValue => {
			if (newValue === RequestStatusEnum.SUCCESS) {
				internalInstance.$dialog
					.alert(
						{
							title: 'Importar',
							body: 'Seu arquivo foi importado com sucesso!',
						},
						{
							okText: 'Fechar',
						}
					)
					.then(() => {
						emit('on-close');

						const request: GetCampaignMarginListRequest = {
							query: filters.value,
						};

						fetchMarginList(request);
					});
			}
		});

		onBeforeUnmount(() => {
			resetRequestStatus();
		});

		return {
			EXCEL_ACCEPT_FILETYPE,
			filePicker,
			filePickerWarnings,
			hasFilePickerWarning,
			hasErrorMessage,
			isImportButtonDisabled,
			errorMessage,
			setMaxSizeExceededWarning,
			setInvalidFileWarning,
			onFileChange,
			fetchMarginSpreadsheetModel,
			handleImport: () => handleImport(request.value),
		};
	},
});
</script>

<style lang="scss">
@import './MarginImportModal.scss';
</style>
