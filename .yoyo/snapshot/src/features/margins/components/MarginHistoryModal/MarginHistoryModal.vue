<template>
	<farm-modal
		class="margin-history-modal"
		:value="value"
		:offset-top="52"
		:offset-bottom="68"
		@input="$emit('on-close')"
	>
		<template #header>
			<farm-dialog-header title="Histórico" @onClose="$emit('on-close')" />
		</template>

		<template #content v-if="!isLoading">
			<template v-if="campaignMarginHistoryList.length && !isError">
				<farm-collapsible
					v-for="(item, index) in campaignMarginHistoryList"
					:key="`${index}-${item.updatedAt}`"
					title=""
					custom-header
					class="margin-history-modal__collapsible mb-4"
				>
					<template #header-content>
						<farm-caption type="regular" color="black" color-variation="40">
							Atualização feita por <b>{{ item.updatedByName }} </b>, dia
							<b>
								{{ defaultDateFormat(item.updatedAt) }}
							</b>
							às
							<b>{{ formatHour(item.updatedAt) }}</b>
						</farm-caption>
					</template>

					<farm-line no-spacing class="mb-4" />

					<MarginDetailCard :campaign-margin-detail="item" />
				</farm-collapsible>

				<farm-datatable-paginator
					v-if="campaignMarginHistoryListPageable"
					class="mt-4"
					:has-gutter="false"
					:page="presentableFilterValues.pageNumber"
					:total-pages="filters.totalPages"
					:initial-limit-per-page="filters.pageSize"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</template>
			<template v-else>
				<farm-alert-reload
					label="Um erro ocorreu ao buscar o histórico"
					@onClick="fetchWithParameters"
				/>
			</template>
		</template>

		<template #footer>
			<footer class="d-flex align-center justify-end pa-4">
				<farm-btn @click="$emit('on-close')">Fechar</farm-btn>
			</footer>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, computed, watch } from 'vue';

import { defaultDateFormat, formatHour } from '@farm-investimentos/front-mfe-libs-ts';

import { OPERATION_TYPES_ENUM } from '@/constants';

import MarginDetailCard from '../MarginDetailCard';
import { useMargin } from '../../composables';

import type { GetCampaignMarginHistoryListRequest } from '../../services/types';
import { usePageable } from '@/features/pricing/composables';

export default defineComponent({
	components: {
		MarginDetailCard,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup() {
		const {
			modals,
			campaignMarginHistoryList,
			campaignMarginHistoryListPageable,
			campaignMarginHistoryListRequestStatus,
			isLoading,
			fetchMarginHistory,
		} = useMargin();

		const modal = modals.value.history;

		const request: GetCampaignMarginHistoryListRequest = {
			params: {
				campaign_id: modal.campaignId,
			},
			query: {
				page: 0,
				limit: 5,
				commercial_product_id: modal.commercialProductId,
				product_id: modal.productId,
			},
		};

		const isError = computed(
			() => campaignMarginHistoryListRequestStatus.value?.type === 'ERROR'
		);
		const operationsToShow = computed(() =>
			campaignMarginHistoryList.value?.marginOperations?.filter(
				operation => !!operation.campaignOperationId
			)
		);

		const fetchWithParameters = () => fetchMarginHistory(request);

		const {
			filters,
			presentableFilterValues,
			setPageableFilters,
			onChangePage,
			onChangePageLimit,
		} = usePageable(
			filters => {
				let query = {
					...request.query,
					...Object.fromEntries(filters),
				};

				fetchMarginHistory({
					...request,
					query,
				});
			},
			campaignMarginHistoryListPageable,
			{}
		);

		watch(campaignMarginHistoryListPageable, () => {
			setPageableFilters(campaignMarginHistoryListPageable.value);
		});

		onBeforeMount(() => fetchWithParameters());

		return {
			OPERATION_TYPES_ENUM,
			campaignMarginHistoryList,
			campaignMarginHistoryListPageable,
			isError,
			isLoading,
			filters,
			operationsToShow,
			presentableFilterValues,
			onChangePage,
			onChangePageLimit,
			fetchWithParameters,
			defaultDateFormat,
			formatHour,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './MarginHistoryModal.scss';
</style>
