import { OPERATION_TYPES_TOKEN_ENUM } from '@/constants';
import { marginHeaders } from '@/features/margins/configurations/headers';
import { VuetifyTableSort } from '@/features/netWorth/composables/usePageable/usePageable';
import { ComputedRef, computed, ref, toRefs } from 'vue';

export default function useMarginTable(props) {
	const sortClick = ref([]);

	const { items: marginList } = toRefs(props);

	const marginsWithUniqueId = computed(() =>
		marginList.value.map((margin, index) => ({
			...margin,
			uniqId: `${index}-${margin.campaignId}-${margin.vehicleId}`,
		}))
	);
	const sortedByItem: ComputedRef<{
		field: (typeof marginHeaders)[number]['value'];
		descending: VuetifyTableSort['descending'];
	}> = computed(() => sortClick.value.find(sortItem => sortItem?.clicked));
	const sortedMargins = computed(() => {
		if (!sortedByItem.value || marginsWithUniqueId.value.length === 1) {
			return marginsWithUniqueId.value;
		}

		const { field, descending } = sortedByItem.value;

		return marginsWithUniqueId.value.toSorted((a, b) => {
			let AValue = a[field];
			let BValue = b[field];

			if (Object.keys(OPERATION_TYPES_TOKEN_ENUM).includes(field)) {
				const AOperation = a['marginOperations'].find(
					({ operationType }) => OPERATION_TYPES_TOKEN_ENUM[field].id === operationType
				);
				const BOperation = b['marginOperations'].find(
					({ operationType }) => OPERATION_TYPES_TOKEN_ENUM[field].id === operationType
				);

				AValue = AOperation?.marginTax;
				BValue = BOperation?.marginTax;
			}

			if (typeof AValue === 'string') {
				return descending === 'ASC'
					? AValue.localeCompare(BValue)
					: BValue.localeCompare(AValue);
			}

			return descending === 'DESC' ? AValue - BValue : BValue - AValue;
		});
	});

	const findOperation = (operations, operationId) => {
		const operation = operations.find(({ operationType }) => operationType === operationId);

		if (!operation || !operation.campaignOperationId) {
			return false;
		}

		const isMissingMarginOperation =
			operation.campaignOperationId && operation.marginTax === null;

		if (isMissingMarginOperation) {
			return null;
		}

		return operation;
	};
	const initTableSorting = () => {
		if (!sortClick.value.length) {
			return;
		}

		sortClick.value[0].clicked = true;
		sortClick.value[0].show = true;
	};

	return {
		margins: sortedMargins,
		sortedByItem,
		sortClick,
		findOperation,
		initTableSorting,
	};
}
