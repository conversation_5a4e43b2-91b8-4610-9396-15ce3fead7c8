<template>
	<v-data-table
		id="margin-table"
		class="margin-table"
		hide-default-footer
		hide-default-header
		item-key="uniqId"
		:items="margins"
		:headers="headers"
		:server-items-length="margins.length"
	>
		<template #header="{ props: { headers } }">
			<farm-datatable-header
				:headers="headers"
				:sort-click="sortClick"
				:show-checkbox="false"
			/>
		</template>

		<template #no-data>
			<DataTableEmptyWrapper />
		</template>

		<template v-slot:[`item.campaignName`]="{ item }">
			<farm-bodytext type="2" variation="regular">
				{{ item.campaignName }}
			</farm-bodytext>
		</template>

		<template v-slot:[`item.commercialProductName`]="{ item }">
			<farm-bodytext type="2" variation="regular">
				{{ item.commercialProductName }}
			</farm-bodytext>
		</template>

		<template v-slot:[`item.campaignCommercialProductStatus`]="{ item }">
			<farm-chip
				dense
				class="ml-2"
				:color="item.campaignCommercialProductStatus ? 'primary' : 'neutral'"
			>
				{{ item.campaignCommercialProductStatus ? 'Ativa' : 'Inativa' }}
			</farm-chip>
		</template>

		<template
			v-for="(operation, token) in OPERATION_TYPES_TOKEN_ENUM"
			v-slot:[`item.${token}`]="{ item }"
		>
			<MarginTableOperationCell
				:key="operation.id"
				:operation="findOperation(item.marginOperations, operation.id)"
			/>
		</template>

		<template v-slot:[`item.contextMenu`]="{ item }">
			<farm-contextmenu>
				<farm-list>
					<farm-listitem
						clickable
						hover-color="primary"
						@click="
							openModal('detail', {
								campaignId: item.campaignId,
								commercialProductId: item.commercialProductId,
								productId,
							})
						"
					>
						<farm-icon size="sm" class="mr-2">open-in-new</farm-icon>
						Ver Detalhes
					</farm-listitem>
					<farm-listitem
						clickable
						hover-color="primary"
						@click="
							openModal('history', {
								campaignId: item.campaignId,
								commercialProductId: item.commercialProductId,
								productId,
							})
						"
					>
						<farm-icon size="sm" class="mr-2">history</farm-icon> Histórico
					</farm-listitem>
				</farm-list>

				<template #activator="{ on, attrs }">
					<farm-btn v-bind="attrs" v-on="on" icon class="ma-0">
						<farm-icon>dots-horizontal</farm-icon>
					</farm-btn>
				</template>
			</farm-contextmenu>
		</template>
	</v-data-table>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';

import { OPERATION_TYPES_TOKEN_ENUM } from '@/constants';

import MarginTableOperationCell from './components/MarginTableOperationCell';
import { useMarginTable } from './composables';

import { useMargin } from '../../composables';
import { marginHeaders as headers } from '../../configurations/headers';

export default defineComponent({
	components: {
		MarginTableOperationCell,
	},
	props: {
		items: {
			type: Array,
			required: true,
		},
		productId: {
			type: Number,
			required: true,
		},
	},
	setup(props) {
		const { openModal } = useMargin();
		const { margins, sortedByItem, sortClick, findOperation, initTableSorting } =
			useMarginTable(props);

		onMounted(initTableSorting);

		return {
			OPERATION_TYPES_TOKEN_ENUM,
			headers,
			sortClick,
			margins,
			sortedByItem,
			findOperation,
			openModal,
		};
	},
});
</script>
