import { file as downloadFileHandler } from '@farm-investimentos/front-mfe-libs-ts';

import configurationClient from '@/configurations/services/configurations';
import excelClient from '@/configurations/services/excel';

import type {
	GetCampaignMarginDetailRequest,
	GetCampaignMarginHistoryListRequest,
	GetCampaignMarginListRequest,
	ImportCampaignMarginListRequest,
} from './types';

export const getCampaignMarginList = (request: GetCampaignMarginListRequest) => {
	return configurationClient.get('/v1/products/campaign/margin/list', {
		params: request.query,
	});
};

export const getCampaignMarginDetail = (request: GetCampaignMarginDetailRequest) => {
	return configurationClient.get(
		`/v1/products/campaign/${request.params.campaign_id}/margin/detail`,
		{
			params: request.query,
		}
	);
};

export const getCampaignMarginHistoryList = (request: GetCampaignMarginHistoryListRequest) => {
	return configurationClient.get(
		`/v1/products/campaign/${request.params.campaign_id}/margin/history`,
		{
			params: request.query,
		}
	);
};
export const importCampaignMargin = (request: ImportCampaignMarginListRequest) => {
	return excelClient.post('/v1/products/contracts/excel/pricing/margin/upload', request.payload);
};

export const exportCampaignMarginList = () => {
	const domain = excelClient.defaults.baseURL;
	const url = '/v1/products/campaign/margin/excel/export';
	return downloadFileHandler(`${domain}${url}?`);
};

export const exportCampaignMarginSpreadsheet = () => {
	const domain = excelClient.defaults.baseURL;
	const url = '/v1/products/excel/model/pricing/margin';
	return downloadFileHandler(`${domain}${url}?`);
};
