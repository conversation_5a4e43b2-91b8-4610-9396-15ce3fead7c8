<template>
	<farm-card
		class="date-history-card d-flex mb-4 pa-4"
		:key="`card-${dateHistory.updatedBy}-${dateHistory.createdAt}`"
	>
		<div class="date-history-card__item d-flex flex-column pr-8">
			<farm-caption class="mb-2" variation="semiBold">Incluídos</farm-caption>

			<farm-typography size="sm" color="black" color-variation="50">
				{{ addedDates }}
			</farm-typography>
		</div>
		<div class="date-history-card__item d-flex flex-column pl-8">
			<farm-caption class="mb-2" variation="semiBold" color="black">Removidos</farm-caption>

			<farm-typography size="sm" color="black" color-variation="50">
				{{ removedDates }}
			</farm-typography>
		</div>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import type { PropType } from 'vue';

import type { DateHistory } from '../../types';
import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

export default defineComponent({
	props: {
		dateHistory: {
			type: Object as PropType<DateHistory>,
			required: true,
		},
	},
	setup(props) {
		const addedDates = computed(() =>
			props.dateHistory.dates_added.map(defaultDateFormat).join(', ')
		);
		const removedDates = computed(() =>
			props.dateHistory.dates_removed.map(defaultDateFormat).join(', ')
		);

		return {
			addedDates,
			removedDates,
		};
	},
});
</script>

<style lang="scss" scoped>
.date-history-card {
	background-color: var(--farm-background-lighten);

	&__item {
		width: 100%;

		&:first-child {
			border-right: 1px solid var(--farm-bw-black-10);
		}
	}
}
</style>
