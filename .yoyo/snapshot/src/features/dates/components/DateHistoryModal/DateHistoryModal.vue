<template>
	<farm-modal :value="value" :offset-top="52" :offset-bottom="68" @input="onClose">
		<template #header>
			<farm-row class="pa-4 pb-0">
				<farm-col cols="10">
					<farm-caption variation="semiBold"> Histórico </farm-caption>
				</farm-col>

				<farm-col cols="2" align="end">
					<farm-icon aria-label="Fe<PERSON><PERSON> o histórico" role="button" @click="onClose">
						close
					</farm-icon>
				</farm-col>
			</farm-row>
		</template>
		<template #content>
			<template v-if="!isLoading && hasHistory">
				<template v-for="dateHistory in dateHistoryList">
					<div
						class="d-flex align-center mb-4"
						:key="`${dateHistory.updatedBy}-${dateHistory.createdAt}`"
					>
						<farm-icon class="mr-2" size="16" color="black" variation="40">
							update
						</farm-icon>

						<farm-typography size="sm" color="black" color-variation="40">
							Atualização feita por
							<b>{{ dateHistory.updatedBy }}</b
							>, dia <b>{{ defaultDateFormat(dateHistory.createdAt) }}</b> às
							<b>{{ formatHour(dateHistory.createdAt) }}</b>
						</farm-typography>
					</div>

					<DateHistoryModalCard
						:key="`card-${dateHistory.updatedBy}-${dateHistory.createdAt}`"
						:date-history="dateHistory"
					/>
				</template>
			</template>

			<span v-if="!isLoading && !hasHistory">Nada a encontrar aqui</span>

			<farm-loader mode="overlay" v-if="isLoading" />
		</template>
		<template #footer>
			<farm-line no-spacing />
			<div class="d-flex align-center justify-end pa-4">
				<farm-btn @click="onClose"> Fechar </farm-btn>
			</div>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, computed } from 'vue';
import { useGetter, useIsLoading, useStore } from '@/composibles';
import { defaultDateFormat, formatHour } from '@farm-investimentos/front-mfe-libs-ts';
import type { DatesHistoryRequest } from '../../services/types';

import DateHistoryModalCard from './../DateHistoryModalCard';
import type { DateHistory } from '../../types';

export default defineComponent({
	components: {
		DateHistoryModalCard,
	},
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		onClose: {
			type: Function,
			required: true,
		},
		productId: {
			type: Number,
			required: true,
		},
	},
	setup(props) {
		const store = useStore();

		const dateHistoryList = computed<DateHistory[]>(useGetter('dates', 'dateHistoryList'));
		const datesHistoryRequestStatus = computed(
			useGetter('dates', 'dateHistoryListRequestStatus')
		);
		const hasHistory = computed(() => !!dateHistoryList.value.length);

		const isLoading = useIsLoading([datesHistoryRequestStatus]);

		const request: DatesHistoryRequest = {
			params: {
				productId: props.productId,
			},
		};

		onBeforeMount(() => {
			if (!props.value) return;

			store.dispatch('dates/getDatesHistory', request);
		});

		return {
			dateHistoryList,
			isLoading,
			hasHistory,
			formatHour,
			defaultDateFormat,
		};
	},
});
</script>
