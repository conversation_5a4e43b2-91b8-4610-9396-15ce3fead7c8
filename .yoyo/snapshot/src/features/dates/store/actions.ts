import {
	RequestStatusEnum,
	errorBuilder,
	fetchDefaultParser,
	notification,
} from '@farm-investimentos/front-mfe-libs-ts';

import {
	fetchDates,
	fetchDatesHistory,
	updateDatesList as updateDatesListService,
} from '../services';

import type { DatesHistoryRequest, UpdateDatesRequest } from './../services/types';
import dateListFromAPI from '@/helpers/dtos/dateListFromAPI';

export default {
	async getDates({ commit }) {
		commit('setDateListRequestStatus', RequestStatusEnum.START);

		try {
			const { data } = await fetchDates();

			fetchDefaultParser(commit, data, dateListFromAPI, 'DateList');
		} catch (error) {
			commit('setDateListRequestStatus', errorBuilder(error));
		}
	},

	async getDatesHistory({ commit }, request: DatesHistoryRequest) {
		commit('setDateHistoryListRequestStatus', RequestStatusEnum.START);

		try {
			const { data } = await fetchDatesHistory(request);

			fetchDefaultParser(commit, data.data.content, null, 'DateHistoryList');
		} catch (error) {
			commit('setDateHistoryListRequestStatus', errorBuilder(error));
		}
	},

	async updateDatesList({ commit }, request: UpdateDatesRequest) {
		commit('setUpdateDatesRequestStatus', RequestStatusEnum.START);

		try {
			await updateDatesListService(request);

			notification(RequestStatusEnum.SUCCESS, {
				title: 'Sucesso',
				message: 'Suas alterações foram salvas com sucesso!',
			});
			commit('setUpdateDatesRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (error) {
			commit('setUpdateDatesRequestStatus', errorBuilder(error));
		}
	},
};
