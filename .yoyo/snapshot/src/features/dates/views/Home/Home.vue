<template>
	<farm-container>
		<farm-heading type="6" class="mb-6">
			Configure as datas de vencimento para cada veículo financeiro
		</farm-heading>

		<template v-if="!isLoading || hasDates">
			<farm-row>
				<farm-col
					cols="12"
					md="6"
					class="date-card-column"
					v-for="(product, index) in datesByProduct"
					:key="product.idVehicle"
				>
					<DateCard
						:product="product"
						@open-modal="openHistory"
						:datepicker-position="cardsToOpenToTop.includes(index) ? 'top' : 'bottom'"
					/>
				</farm-col>
			</farm-row>

			<DateHistoryModal
				v-if="productId"
				:value="isOpenModal"
				:on-close="closeModal"
				:product-id="productId"
			/>
		</template>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-container>
</template>

<script lang="ts">
import { computed, defineAsyncComponent, defineComponent, onBeforeMount, ref, watch } from 'vue';

import DateCard from './../../components/DateCard';
import useModal from '../../composables/useModal';
import { useGetter, useIsLoading, useStore } from '@/composibles';
import type { DatesByProduct } from '../../types';

const DateHistoryModal = defineAsyncComponent(() => import('../../components/DateHistoryModal'));

export default defineComponent({
	components: {
		DateCard,
		DateHistoryModal,
	},
	setup() {
		const store = useStore();
		const { isOpenModal, openModal, closeModal } = useModal();

		const productId = ref(null);

		const datesByProduct = computed<DatesByProduct[]>(useGetter('dates', 'dateList'));
		const dateListRequestStatus = computed(useGetter('dates', 'dateListRequestStatus'));
		const updateDatesRequestStatus = computed(useGetter('dates', 'updateDatesRequestStatus'));
		const hasDates = computed(() => !!datesByProduct.value.length);
		const cardsToOpenToTop = computed(() =>
			[-1, -2, -3, -4].map(indexNumber => datesByProduct.value.length + indexNumber)
		);

		const isLoading = useIsLoading([dateListRequestStatus, updateDatesRequestStatus]);

		const openHistory = (id: number) => {
			productId.value = id;

			openModal();
		};

		watch(isOpenModal, newValue => {
			// reset productId read by modal
			if (!newValue) productId.value = null;
		});

		onBeforeMount(() => {
			store.dispatch('dates/getDates');
		});

		return {
			datesByProduct,
			productId,
			cardsToOpenToTop,
			isOpenModal,
			isLoading,
			hasDates,
			openHistory,
			closeModal,
		};
	},
});
</script>

<style lang="scss">
.date-card-column {
	&:not(:last-of-type) {
		margin-bottom: 24px;
	}

	@media (width >= 960px) {
		&:not(:nth-last-of-type(1), :nth-last-of-type(2)) {
			margin-bottom: 24px;
		}
	}
}
</style>
