<template>
	<farm-box class="mt-4">
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="4" v-for="(item, index) in data" :key="index">
				<AvailableCashCard
					:data="item"
					@updatedAvailableCashValues="updatedAvailableCashValues"
					@reValidate="reValidate"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import AvailableCashCard from '../AvailableCashCard';

export default defineComponent({
	components: {
		AvailableCashCard,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
	methods: {
		updatedAvailableCashValues(value) {
			this.$emit('updatedAvailableCashValues', value);
		},
		reValidate(){
			this.$emit('reValidate');
		}
	},
});
</script>
