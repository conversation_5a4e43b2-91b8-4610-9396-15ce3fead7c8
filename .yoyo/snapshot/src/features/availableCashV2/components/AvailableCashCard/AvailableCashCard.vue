<template>
	<farm-card>
		<farm-card-content gutter="md">
			<farm-row>
				<farm-col cols="6" class="cash-available-content">
					<farm-typography tag="h3" bold size="sm" class="mt-1">
						Veículo Financeiro
					</farm-typography>
					<farm-typography
						tag="p"
						size="md"
						class="cash-name"
						ellipsis
						:title="data.name"
					>
						{{ data.name }}
					</farm-typography>
				</farm-col>
				<farm-col cols="6" class="cash-available-content">
					<farm-label>{{ $tc('availableCash.availableCashLabel') }}</farm-label>
					<farm-textfield-v2
						v-model="inputVal"
						:mask="currencyMask"
						:rules="[rules.required]"
						@change="onChange"
					/>
				</farm-col>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

import { currency as currencyMask } from '@/helpers/masks';
import { parse, format } from '@/helpers/availableCash';

export default defineComponent({
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			rules: {
				required: value =>
					(value !== 'R$' && !!value) || this['$tc']('defaults.rules.required'),
			},
			inputVal: format(this.data.availableCash),
			timer: null,
		};
	},

	methods: {
		currencyMask,
		onChange(value: string): void {
			if (this.timer) {
				clearTimeout(this.timer);
				this.timer = null;
			}
			this.timer = setTimeout(() => {
				this.$emit('updatedAvailableCashValues', {
					...this.data,
					availableCash: parse(value),
				});
			}, 750);
		},
	},
	watch: {
		inputVal(newValue: string): void {
			this.$emit('input', newValue);
			this.$emit('reValidate');
		},
		data(): void {
			this.inputVal = format(this.data.availableCash);
		},
	},
});
</script>
<style lang="scss" scoped>
@import './AvailableCashCard.scss';
</style>
