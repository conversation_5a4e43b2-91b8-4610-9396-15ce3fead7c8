<template>
	<farm-modal v-model="inputVal" size="md" :offsetTop="48" :offsetBottom="68">
		<template v-slot:header>
			<farm-dialog-header title="Sucesso" @onClose="close" />
		</template>
			<template v-slot:content>
				<farm-row >
					<farm-col cols="12">
						<farm-typography size="sm" class="pt-3">
							Associação ao grupo realizado com sucesso para
							<b>{{ message }}</b>!
						</farm-typography>
						<farm-typography size="sm" class="pt-3">
							O(s) cliente(s) abaixo já estão associado(s) a outro(s) grupo(s):
						</farm-typography>
					</farm-col>
					<farm-col cols="8">
						<farm-textarea rows="3" :value="errorMessage"></farm-textarea>
					</farm-col>
					<farm-col cols="3">
						<farm-copytoclipboard
							class="mt-1"
							tooltip-color="gray"
							:toCopy="errorMessage"
							:isIcon="false"
						/>
					</farm-col>
				</farm-row>
			</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Continuar"
				:hasCancel="false"
				@onConfirm="confirm" 
			/>
		</template>
	</farm-modal>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		value: {
			required: true,
		},
		quant: {
			type: Number,
			required: true,
		},
		data: {
			type: Array,
		},
	},
	data() {
		return {
			valid: null,
			message: '',
			errorMessage: '',
		};
	},
	mounted(): void {
		const text = this.quant === 1 ? 'Cliente' : 'Cliente(s)';
		this.message = `${this.quant} ${text}`;
		this.addCustomers();
	},
	computed: {
		inputVal: {
			get(): boolean {
				return this.value;
			},
			set(val: boolean): void {
				this.$emit('input', val);
			},
		},
	},
	methods: {
		close(): void {
			this.inputVal = false;
		},
		confirm() {
			this.close();
			this.$emit('onConfirm');
		},

		addCustomers() {
			let contentText = '';
			for (let i = 0; i < this.data.length; i++) {
				const name = this.data[i].clientName;
				const raiz = this.data[i].raiz;
				const group = this.data[i].groupName;
				contentText += `${name} - ${raiz} - ${group} \n`;
			}
			this.errorMessage = contentText;
		},
	},
});
</script>


