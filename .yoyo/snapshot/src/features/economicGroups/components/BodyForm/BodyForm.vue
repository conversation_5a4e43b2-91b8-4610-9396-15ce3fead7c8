<template>
	<farm-container>
		<TabsForm
			:tabList="tabs"
			:valueDefault="DATA"
			:isEdit="isEdit"
			@onUpdateCurrentTab="onUpdatedCurrentTab"
		/>
		<farm-box>
			<TabData
				v-if="currentTab === DATA"
				:isEdit="isEdit"
				@onSubmit="onSubmit"
				@onUpdateDataUser="onUpdateDataUser"
				@onDisabledButton="onDisabledButton"
			/>
			<TabAssociateCustomers
				v-if="currentTab === ASSOCIATE_CUSTOMERS"
				@onUpdateDataUser="onUpdateDataUser"
				@onSubmit="onSubmit"
				@onDisabledButton="onDisabledButton"
			/>
			<TabJoinGroups
				v-if="currentTab === JOIN_GROUPS"
				@onUpdateDataUser="onUpdateDataUser"
				@onSubmit="onSubmit"
				@onDisabledButton="onDisabledButton"
			/>
		</farm-box>
		<FooterForm
			:labelButton="labelButton"
			:isDisabledButton="disabledButtonFooter"
			:showLayoutData="isEdit"
			:data="dataFooterForm"
			@onCancel="onCancel"
			@onSave="onSave"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapGetters } from 'vuex';

import FooterForm from '@/components/FooterForm';
import TabsForm, { TabsFormTypes } from '@/components/TabsForm';
import { DATA, EDIT } from '@/constants';
import { format } from '@/helpers/formatUpdateUser';
import TabData from '../TabData';
import TabAssociateCustomers from '../TabAssociateCustomers';
import TabJoinGroups from '../TabJoinGroups';
import { tabDefault, tabEdit } from '../../configurations/tabs';
import { ASSOCIATE_CUSTOMERS, JOIN_GROUPS } from '../../constants';

export default defineComponent({
	components: {
		TabsForm,
		TabData,
		TabAssociateCustomers,
		TabJoinGroups,
		FooterForm,
	},
	props: {
		type: {
			type: String,
		},
	},
	data() {
		return {
			disabledButtonFooter: null,
			tabs: tabDefault,
			currentTab: DATA,
			DATA,
			ASSOCIATE_CUSTOMERS,
			JOIN_GROUPS,
			dataFooterForm: {},
			dispatchSubmit: null,
			labelButton: 'Cadastrar',
		};
	},
	mounted(): void {
		if (this.isEdit) {
			this.configurationEdit();
		}
	},
	computed: {
		...mapGetters('cadastros', {
			economicGroupItem: 'economicGroupItem',
		}),
		isEdit(): boolean {
			return this.type === EDIT;
		},
		currentId(): number {
			return parseInt(this.$route.params.id, 10);
		},
	},
	watch: {
		economicGroupItem(newValue) {
			if (this.economicGroupItem && this.economicGroupItem.meta) {
				this.dataFooterForm = format({ ...this.economicGroupItem.meta });
			}
			return newValue;
		},
	},
	methods: {
		configurationEdit(): void {
			this.labelButton = 'Salvar';
			this.tabs = tabEdit;
			let path = this.$route.query.path;
			if (!path) {
				path = DATA;
				this.$router.replace({
					path: this.$route.path,
					query: { path },
				});
			}
			this.currentTab = path;
			const index = this.tabs.findIndex(tab => path === tab.path);
			if (this.$refs.tabEl && this.$refs.tabEl.toIndex) {
				this.$refs.tabEl.toIndex(index);
			}
		},
		updateTab(item): void {
			if (!item) {
				return;
			}
			if (item.path === this.$route.query.path) {
				return;
			}
			this.$router.replace({ query: { path: item.path } });
			this.currentTab = item.path;
		},
		onCancel() {
			this.$router.push({
				path: `/admin/cadastros/grupos_economicos`,
			});
		},
		onSubmit(call: () => void): void {
			this.dispatchSubmit = call;
		},
		onSave(): void {
			this.dispatchSubmit();
		},
		onUpdateDataUser(data): void {
			this.dataFooterForm = data;
		},
		onDisabledButton(value: boolean): void {
			this.disabledButtonFooter = value;
		},
		onUpdatedCurrentTab(value: Array<TabsFormTypes>): void {
			this.currentTab = value;
		},
	},
});
</script>
