<template>
	<div>
		<farm-row>
			<farm-col cols="12">
				<farm-label for="form-customers">
					Clientes
					<farm-chip v-if="selectItem.length > 0" dense color="gray">
						{{ selectItem.length }} Selecionado{{ selectItem.length > 1 ? 's' : '' }}
					</farm-chip>
				</farm-label>
				<farm-textfield-v2 v-model="input" id="form-customers" @keyup="onKeyUp" />
				<div class="d-flex justify-end mb-6">
					<farm-btn-confirm
						class="farm-btn--responsive"
						title="Adicionar Selecionados"
						:disabled="selectItem.length === 0"
						@click="onAddCustomers"
					>
						Adicionar Selecionados
					</farm-btn-confirm>
				</div>
			</farm-col>
		</farm-row>
		<v-data-table
			class="elevation-0 pb-4"
			id="table-customers-economicgroups-list"
			item-key="id"
			hide-default-footer
			v-model="selectItem"
			ref="table"
			:headers="headers"
			:items="data"
			:show-select="true"
			:server-items-length="data.length"
			:hide-default-header="showCustomHeader()"
			:options.sync="options"
			:header-props="headerProps"
		>
			<template slot="no-data">
				<DataTableEmptyWrapper subtitle="Tente filtrar novamente sua pesquisa" />
			</template>

			<template #header="{ props, on }" v-if="showCustomHeader()">
				<DataTableHeader
					firstSelected
					:headers="props.headers"
					:sortClick="sortClicked"
					:selectedIndex="1"
					v-model="props.everyItem"
					:headerProps="props"
					@onClickSort="onSort"
					@toggleSelectAll="on['toggle-select-all']"
				/>
			</template>

			<template v-slot:footer>
				<DataTablePaginator
					class="mt-6"
					:page="currentPagination"
					:totalPages="paginationTotalPages"
					@onChangePage="onChangePageTable"
					@onChangeLimitPerPage="onChangeLimitPerPageTable"
				/>
			</template>
		</v-data-table>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { pageable } from '@farm-investimentos/front-mfe-libs-ts';

import { headerClients as headers } from '../../configurations/headers';

export default defineComponent({
	props: {
		data: {
			type: Array,
			require: true,
		},
		paginationTotalPages: {
			type: Number,
			require: true,
		},
		paginationPageActive: {
			type: Number,
			default: 1,
		},
		filter: {
			type: Object,
			default: () => ({}),
		},
	},
	mixins: [pageable],
	data() {
		return {
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
			sortClicked: [],
			headerProps: {
				sortByText: 'Ordenar por',
			},
			headers,
			options: {},
			currentPagination: 1,
			selectItem: [],
			input: '',
			timer: null,
			inputEmpty: false,
		};
	},
	computed: {
		breakpoint(): string {
			return this.$vuetify.breakpoint.name;
		},
	},
	methods: {
		onSort(data): void {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			const filtersActive = {
				...this.filters,
				search: this.filter.search || '',
				orderby: data.field,
				order: data.descending,
				page: 0,
			};
			this.$emit('onRequest', filtersActive, 1);
		},

		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		onChangePageTable(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			this.filters.page = pageActive;
			this.currentPagination = page;
			this.$emit(
				'onRequest',
				{ page: pageActive, limit: this.filters.limit, search: this.filter.search || '' },
				this.currentPagination
			);
		},
		onChangeLimitPerPageTable(limit: number): void {
			this.filters.limit = limit;
			this.currentPagination = 1;
			this.$emit(
				'onRequest',
				{ page: 0, limit: limit, search: this.filter.search || '' },
				this.currentPagination
			);
		},
		onAddCustomers() {
			this.$emit('onUpdateItemSelect', this.selectItem);
		},
		onKeyUp() {
			if (this.input.length > 2) {
				this.inputEmpty = true;
				if (this.timer) {
					clearTimeout(this.timer);
					this.timer = null;
				}
				this.timer = setTimeout(() => {
					this.$emit(
						'onRequest',
						{
							...this.filter,
							page: 0,
							search: this.input,
						},
						this.currentPagination
					);
				}, 750);
				return false;
			}
			if (this.input.length === 0 && this.inputEmpty) {
				this.inputEmpty = false;
				this.$emit(
					'onRequest',
					{
						...this.filter,
						page: 0,
						search: '',
					},
					this.currentPagination
				);
			}
		},
	},
	watch: {
		paginationPageActive(newValue): void {
			this.currentPagination = newValue;
		},
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#table-customers-economicgroups-list', 1, (0));
</style>

<style lang="scss" scoped>
@import './CustomersTable.scss';
</style>
