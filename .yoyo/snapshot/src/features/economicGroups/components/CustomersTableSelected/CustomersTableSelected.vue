<template>
	<div>
		<farm-row>
			<farm-col cols="12">
				<farm-label for="form-customers"> Clientes Associados/Selecionados </farm-label>
				<farm-textfield-v2 v-model="input" id="form-customers" @keyup="onKeyUp" />
				<div class="d-flex justify-end mb-6">
					<farm-btn-confirm
						class="farm-btn--responsive"
						title="Remover Selecionados"
						:disabled="selectItem.length === 0"
						@click="onHandleRemoveItem"
					>
						Remover Selecionados
					</farm-btn-confirm>
					<farm-btn-confirm
						class="farm-btn--responsive ml-2"
						title="Remover Todos"
						:disabled="data.length === 0"
						@click="onHandleRemoveAll"
					>
						Remover Todos
					</farm-btn-confirm>
				</div>
			</farm-col>
		</farm-row>
		<v-data-table
			v-model="selectItem"
			class="elevation-0 pb-4 mt-0"
			id="table-customers-economicgroups"
			item-key="id"
			hide-default-footer
			:headers="headers"
			:items="data"
			:show-select="true"
			:server-items-length="data.length"
			:hide-default-header="showCustomHeader()"
			:options.sync="options"
			:header-props="headerProps"
		>
			<template slot="no-data">
				<DataTableEmptyWrapper
					subtitle="Adicione os clientes selecionados para associar ao grupo."
				/>
			</template>

			<template #header="{ props, on }" v-if="showCustomHeader()">
				<DataTableHeader
					v-model="props.everyItem"
					firstSelected
					:headers="props.headers"
					:sortClick="sortClicked"
					:selectedIndex="1"
					:headerProps="props"
					@onClickSort="onSort"
					@toggleSelectAll="on['toggle-select-all']"
				/>
			</template>

			<template v-slot:[`item.startRelationship`]="{ item }">
				{{ defaultDateFormat(item.startRelationship) }}
			</template>

			<template v-slot:footer>
				<DataTablePaginator
					class="mt-6"
					:disabled="data.length === 0"
					:page="currentPagination"
					:totalPages="paginationTotalPages"
					@onChangePage="onChangePageTable"
					@onChangeLimitPerPage="onChangeLimitPerPageTable"
				/>
			</template>
		</v-data-table>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { pageable, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { headerClientsSelected as headers } from '../../configurations/headers';

export default defineComponent({
	props: {
		data: {
			type: Array,
			require: true,
		},
		paginationTotalPages: {
			type: Number,
			require: true,
		},
		paginationPageActive: {
			type: Number,
			default: 1,
		},
		filter: {
			type: Object,
			default: () => ({}),
		},
		customersSelected: {
			type: Array,
			default: () => [],
		},
	},
	mixins: [pageable],
	data() {
		return {
			filters: {
				page: 0,
				limit: 10,
			},
			hasSort: {
				orderby: 'name',
				order: 'ASC',
			},
			sortClicked: [],
			headerProps: {
				sortByText: 'Ordenar por',
			},
			headers,
			options: {},
			currentPagination: 1,
			selectItem: [],
			input: '',
			defaultDateFormat,
			inputEmpty: false,
		};
	},
	computed: {
		breakpoint(): string {
			return this.$vuetify.breakpoint.name;
		},
	},
	methods: {
		onSort(data): void {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;
			const filtersActive = {
				...this.filters,
				search: this.filter.search || '',
				orderby: data.field,
				order: data.descending,
			};
			this.$emit('onRequest', filtersActive, 1);
		},

		showCustomHeader(): boolean {
			return this.breakpoint !== 'xs';
		},
		onChangePageTable(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			this.filters.page = pageActive;
			this.currentPagination = page;
			this.$emit(
				'onRequest',
				{ page: pageActive, limit: this.filters.limit, name: this.filter.name || '' },
				this.currentPagination
			);
		},
		onChangeLimitPerPageTable(limit: number): void {
			this.filters.limit = limit;
			this.currentPagination = 1;
			this.$emit('onUpdatedTotalPagesCustomersSelected', limit);
			this.$emit(
				'onRequest',
				{ page: 0, limit: limit, name: this.filter.name || '' },
				this.currentPagination
			);
		},
		onHandleRemoveItem() {
			const ids = this.selectItem.map(el => el.id);
			const dataUpdated = this.data.filter(el => !ids.includes(el.id));
			this.$emit('onUpdatedCustomersSelectedRemoveItem', {
				itemRemoved: this.selectItem,
				newDataTable: dataUpdated,
			});
		},
		onHandleRemoveAll() {
			this.selectItem = [];
			this.$emit('onUpdatedCustomersSelectedRemoveAll', true);
		},
		onKeyUp() {
			if (this.input.length > 2) {
				this.inputEmpty = true;
				if (this.timer) {
					clearTimeout(this.timer);
					this.timer = null;
				}
				this.timer = setTimeout(() => {
					this.$emit(
						'onRequest',
						{
							...this.filter,
							page: 0,
							search: this.input,
						},
						this.currentPagination
					);
				}, 750);
				return false;
			}
			if (this.input.length === 0 && this.inputEmpty) {
				this.inputEmpty = false;
				this.$emit(
					'onRequest',
					{
						...this.filter,
						page: 0,
						search: '',
					},
					this.currentPagination
				);
			}
		},
	},
	watch: {
		paginationPageActive(newValue): void {
			this.currentPagination = newValue;
		},
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#table-customers-economicgroups', 1, (0));
</style>

<style lang="scss" scoped>
@import './CustomersTableSelected.scss';
</style>
