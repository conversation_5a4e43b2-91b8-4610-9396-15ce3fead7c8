import { DATA } from '@/constants';
import { TabTypes } from '@/types';

import { ASSOCIATE_CUSTOMERS, JOIN_GROUPS } from '../../constants';

export const tabDefault: Array<TabTypes> = [
	{
		name: 'dados',
		path: DATA,
	},
];

export const tabEdit: Array<TabTypes> = [
	...tabDefault,
	{
		name: 'Associar Clientes',
		path: ASSOCIATE_CUSTOMERS,
	},
	{
		name: 'Unir Grupos',
		path: JOIN_GROUPS,
	},
];
