<template>
	<farm-modal v-model="inputVal" size="sm" :offsetTop="48" :offsetBottom="68">
		<template v-slot:header>
			<farm-dialog-header title="Dados do responsável pelo preenchimento" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-row class="my-3" no-default-gutters>
				<farm-box>
					<ul>
						<li><strong>Nome Completo:</strong> {{ responsibleDetails.name }}</li>
						<li><strong>E-mail:</strong> {{ responsibleDetails.email }}</li>
						<li><strong>Celular:</strong> {{ responsibleDetails.phone }}</li>
					</ul>
				</farm-box>
			</farm-row>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer :hasConfirm="false" @onClose="close" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { mapGetters } from 'vuex';
import { defineComponent } from 'vue';

export default defineComponent({
	name: 'ModalResponsibleDetails',
	props: {
		value: {
			required: true,
		},
	},
	computed: {
		...mapGetters('onboarding', {
			responsibleDetails: 'responsibleDetails',
		}),
		inputVal: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			},
		},
	},
	methods: {
		close() {
			this.inputVal = false;
		},
	},
});
</script>
<style lang="scss" scoped>
@import './ModalResponsibleDetails.scss';
</style>
