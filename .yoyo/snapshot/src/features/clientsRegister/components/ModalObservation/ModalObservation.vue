<template>
	<farm-modal v-model="inputVal" :offsetTop="48" :offsetBottom="68" size="sm">
		<template v-slot:header>
			<farm-dialog-header title="Observação" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-box class="my-3">
				<farm-caption variation="regular">
					Justifique a alteração do Status para
					<strong>{{ description }}</strong>
					.
				</farm-caption>
				<farm-label class="my-3">Mensagem</farm-label>
				<farm-textarea maxlength="1000" v-model="textObservation" rows="5" />
			</farm-box>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				closeLabel="Cancelar"
				confirmLabel="Alterar Status"
				:hasCancel="true"
				:hasConfirm="true"
				:isConfirmDisabled="disabledConfirmButton"
				@onClose="close"
				@onConfirm="confirmChangeStatus"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { mapActions } from 'vuex';
import { defineComponent } from 'vue';

export default defineComponent({
	name: 'ModalObservation',
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		idProduct: {
			type: Number,
			required: true,
		},
		id: {
			type: Number,
			required: true,
		},
		idStatus: {
			type: Number,
			required: true,
		},
		description: {
			type: String,
			required: true,
		},
	},
	data() {
		return {
			textObservation: '',
		};
	},
	computed: {
		disabledConfirmButton() {
			return this.textObservation.length < 5;
		},
		inputVal: {
			get() {
				return this.value;
			},
			set(val) {
				this.$emit('input', val);
			},
		},
	},
	methods: {
		...mapActions('onboarding', {
			changeStatusOnboarding: 'changeStatusOnboarding',
		}),
		close() {
			this.inputVal = false;
		},
		confirmChangeStatus() {
			this.close();
			this.changeStatusOnboarding({
				idProduct: this.idProduct,
				idStatus: this.idStatus,
				id: this.id,
				observation: this.textObservation,
			});
		},
	},
});
</script>
