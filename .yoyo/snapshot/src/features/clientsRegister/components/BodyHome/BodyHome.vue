<template>
	<farm-container>
		<farm-row justify="space-between">
			<farm-col cols="12" md="6">
				<MainFilter
					:hasExtraFilters="isFeatureEnabled('clients.onboarding')"
					:label="$tc('clients.filter.mainLabel')"
					:showFilters="filter"
					@onInputChange="filterInputChanged"
					@onClick="onClick"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<Filters
				v-show="filter"
				:statusOnboardingList="statusOnboardingList"
				:clientTypeOnboardingList="clientTypeOnboardingList"
				@onApply="searchListener"
			/>
		</collapse-transition>
		<farm-row extra-decrease>
			<farm-box>
				<v-data-table
					hide-default-footer
					class="elevation-0 mt-0 table-cadastros-clients-list"
					:headers="headers"
					:items="invitesList"
					:server-items-length="invitesList.length"
					:hide-default-header="!checkHeader()"
					:options.sync="options"
					:header-props="headerProps"
				>
					<template slot="no-data">
						<DataTableEmptyWrapper />
					</template>

					<template v-slot:header="{ props: { headers } }" v-if="!checkHeader()">
						<DataTableHeader
							firstSelected
							:headers="headers"
							:sortClick="sortClicked"
							:selectedIndex="6"
							@onClickSort="onSort"
						/>
					</template>

					<template v-slot:[`item.statusOnboarding`]="{ item }">
						<farm-chip-invite :status="item.idStatus" is-full />
					</template>

					<template v-slot:[`item.type`]="{ item }">
						{{ typePerson(item.document) }}
					</template>

					<template v-slot:[`item.lastOnboardUpdate`]="{ item }">
						{{ dateFormatter(item.updatedAt) }}
					</template>

					<template v-slot:[`item.rating`]="{ item }">
						<farm-chip color="neutral" v-if="item.classification">
							{{ item.classification }}
						</farm-chip>
					</template>

					<template v-slot:[`item.infos`]="{ item }">
						<farm-context-menu
							:items="contextMenuItems(item)"
							@documents="documentsItem(item)"
							@edit="editItem(item)"
							@historic="historicItem(item)"
							@details="details(item)"
							@invite="invite(item)"
							@onHold="onModalObservation(item, { id: 17, name: 'Em Espera' })"
							@onUnderAnalysis="onUnderAnalysis(item)"
							@onDeclined="onModalObservation(item, { id: 18, name: 'Declinado' })"
						/>
					</template>

					<template v-slot:footer>
						<DataTablePaginator
							class="mt-6 mb-n6"
							:page="currentPage"
							:totalPages="invitesListTotalPages"
							@onChangePage="onChangePage"
							@onChangeLimitPerPage="onChangeLimitPerPage"
						/>
					</template>
				</v-data-table>
			</farm-box>
		</farm-row>

		<ModalHistoric
			v-if="showDocuments"
			v-model="showDocuments"
			:itemsInviteHistoric="historicInviteList"
		/>

		<ModalResponsibleDetails
			v-if="showDetails"
			v-model="showDetails"
			:item="defaultModalDetailsModel"
		/>

		<ModalObservation
			v-if="showModalObservation"
			v-model="showModalObservation"
			:idProduct="this.selectedProduct.id"
			:id="this.currentItem.registerId"
			:idStatus="this.currentOption.id"
			:description="this.currentOption.name"
		/>

		<ModalInvite
			v-model="showModalInvite"
			v-if="showModalInvite"
			:item="currentItem"
			:idProduct="this.selectedProduct.id"
			@onSendSuccess="doSearch"
		/>

		<Loader mode="overlay" v-if="isLoading" />
	</farm-container>
</template>

<script lang="ts">
/* eslint-disable no-mixed-spaces-and-tabs */

import { mapActions, mapGetters } from 'vuex';
import { defineComponent } from 'vue';
import {
	RequestStatusEnum,
	defaultDateFormat,
	notification,
	notificationWrapper,
	pageable,
} from '@farm-investimentos/front-mfe-libs-ts';

import {
	documents as documentsOption,
	onHold as onHoldOption,
	declined as declinedOption,
	underAnalysis as underAnalysisOption,
	historic as historicOption,
} from '@/configurations/contextMenuOptions';

import {
	edit as editOption,
	invite as inviteOption,
	details as detailsOption,
} from '@farm-investimentos/front-mfe-libs-ts/configurations/contextMenuOptions';

import { detailRegisterToModalFromRegister } from '@/helpers/dtoBuilders';
import { IRequestStatus, IClientRegister, IDefaultRegister } from '@/helpers/interfaces';

import ModalResponsibleDetails from '../ModalResponsibleDetails';
import Filters from '../Filters';
import ModalHistoric from '../ModalHistoric';
import ModalInvite from '../ModalInvite';
import ModalObservation from '../ModalObservation';
import { headersHome as headers } from '../../configurations/headers';

export default defineComponent({
	components: {
		ModalHistoric,
		ModalInvite,
		ModalResponsibleDetails,
		ModalObservation,
		Filters,
	},
	mixins: [pageable],
	data() {
		return {
			filter: false,
			filters: {
				page: 0,
				limit: 10,
			},
			lastSearchFilters: {},
			currentItem: null,
			currentOption: null,
			dateRange: [],
			menuDate: [],
			date: [],
			headers: (this as any).isFeatureEnabled('clients.onboarding')
				? [...headers]
				: headers.filter(item => item.value !== 'statusOnboarding'),
			RequestStatusEnum,
			defaultDateFormat,
			idRegister: null,
			showDetails: false,
			showDocuments: false,
			showModalInvite: false,
			showModalObservation: false,
			filterInputKey: 's',
			switchesToShow: ['poder', 'adesaoPrograma', 'liberacaoOriginador', 'operaDolar'],
			defaultModalDetailsModel: {},
			headerProps: {
				sortByText: 'Ordenar por',
			},
			options: {},
			sortClicked: [],
			hasSort: {
				orderby: 'lastOnboardUpdate',
				order: 'DESC',
			},
		};
	},
	methods: {
		...mapActions('cadastros', {
			fetchRegister: 'fetchRegister',
		}),
		...mapActions('onboarding', {
			fetchInvites: 'fetchInvites',
			fetchResponsibleDetails: 'fetchResponsibleDetails',
			fetchStatus: 'fetchStatus',
			fetchInviteHistoric: 'fetchInviteHistoric',
			dismissInviteHistoric: 'dismissInviteHistoric',
			changeStatusOnboarding: 'changeStatusOnboarding',
			fetchClientType: 'fetchClientType',
			dismissInvites: 'dismissInvites',
		}),
		dateFormatter(data) {
			return data != null
				? new Intl.DateTimeFormat('default', {
						hour: 'numeric',
						minute: 'numeric',
						year: 'numeric',
						month: 'numeric',
						day: 'numeric',
				  })
						.format(new Date(data))
						.split(' ')
						.join(' às ')
				: '';
		},
		onSort(data) {
			this.hasSort.orderby = data.field;
			this.hasSort.order = data.descending;

			const filtersActive = {
				...this.filters,
				orderby: data.field,
				order: data.descending,
			};

			const payload = {
				idProduct: this.selectedProduct.id,
				filters: filtersActive,
			};

			this.lastSearchFilters = { ...filtersActive };

			this.fetchInvites(payload);
		},
		checkHeader() {
			if (this.breakpoint === 'xs') {
				return true;
			}
			return false;
		},
		doHistoricSearch() {
			this.dismissInviteHistoric();
			this.fetchInviteHistoric({
				idProduct: this.selectedProduct.id,
				idRegister: this.idRegister,
			});
		},
		doSearch() {
			this.lastSearchFilters = { ...this.filters };
			this.fetchInvites({
				idProduct: this.selectedProduct.id,
				filters: { ...this.filters, ...this.hasSort },
			});
		},
		onClick() {
			this.filter = !this.filter;
		},

		details(item: IClientRegister) {
			this.fetchResponsibleDetails({
				idProduct: this.selectedProduct.id,
				id: item.registerId,
			});
		},
		documentsItem(item: IClientRegister) {
			this.$router.push({
				path: `sacados/${this.documentType(item.document)}/${
					item.document
				}/editar?path=documentos&origin=cadastros/onboarding`,
			});
		},
		historicItem(item: IClientRegister) {
			this.showDocuments = true;

			if (this.showDocuments === true) {
				this.idRegister = item.registerId;
				this.doHistoricSearch();
			}
		},
		editItem(item: IClientRegister) {
			this.$router.push({
				path: `sacados/${this.documentType(item.document)}/${
					item.document
				}/editar?path=dados&origin=cadastros/onboarding`,
			});
		},
		invite(item) {
			this.currentItem = { ...item };
			this.showModalInvite = true;
		},
		onModalObservation(item: IClientRegister, status: IDefaultRegister) {
			this.currentItem = { ...item };
			this.showModalObservation = true;
			this.currentOption = { ...status };
		},
		onUnderAnalysis(item: IClientRegister) {
			this.currentItem = { ...item };
			this.changeStatusOnboarding({
				idProduct: this.selectedProduct.id,
				idStatus: 15,
				id: item.registerId,
			});
		},
		contextMenuItems(item: IClientRegister) {
			const statusOnboarding = item.idStatus;
			if (statusOnboarding === 18) {
				return [documentsOption, historicOption];
			}

			if (statusOnboarding === 17) {
				return [underAnalysisOption];
			}

			if (!this.canWrite) {
				return [detailsOption];
			}

			const listEdits = [detailsOption, editOption];
			let bottomItems = [];

			if (!this.canDeclineCustomerByStatus(statusOnboarding)) {
				bottomItems.push(declinedOption);
			}

			if (this.isFeatureEnabled('clients.onboarding')) {
				let middleItems = [];

				if (this.canSendInviteByStatus(statusOnboarding)) {
					middleItems = [...middleItems, inviteOption];

					const statusOnboardingOnHold = 15;
					const statusOnboardingUnderAnalysis = 17;
					if (statusOnboarding === statusOnboardingOnHold) {
						middleItems = [onHoldOption, inviteOption];
					}
					if (statusOnboarding === statusOnboardingUnderAnalysis) {
						middleItems = [underAnalysisOption, inviteOption];
					}
				}

				return this.internalUser
					? [
							documentsOption,
							historicOption,
							...listEdits,
							...middleItems,
							...bottomItems,
					  ]
					: [documentsOption, historicOption, ...listEdits, ...middleItems];
			}

			return this.internalUser ? [...listEdits, ...bottomItems] : listEdits;
		},

		canSendInviteByStatus(statusId: number) {
			const ALLOWED_SEND_INVITE_IDS = [14, 15, 16, 17, 18];
			return ALLOWED_SEND_INVITE_IDS.includes(statusId);
		},

		canDeclineCustomerByStatus(statusId: number) {
			const ALLOW_CLIENT_DECLINE_IDS = [10, 18, 13];
			return ALLOW_CLIENT_DECLINE_IDS.includes(statusId);
		},
		typePerson(document) {
			return document?.length > 11 ? 'JURÍDICA' : 'FÍSICA';
		},
		documentType(document) {
			return document.length > 11 ? 'pj' : 'pf';
		},
	},
	mounted() {
		this.dismissInvites();
		this.doSearch();
		this.fetchStatus({ idProduct: this.selectedProduct.id });
		this.fetchClientType();
	},
	watch: {
		options: {
			handler() {
				if (this.options.sortBy.length > 0) {
					const data = {
						descending: this.options.sortDesc[0] ? 'ASC' : 'DESC',
						field: this.options.sortBy[0],
						[this.options.sortBy[0]]: this.options.sortDesc[0],
					};
					this.$emit('onClickSort', data);
				}
			},
			deep: true,
		},
		currentRegister(newValue: IRequestStatus) {
			this.defaultModalDetailsModel = detailRegisterToModalFromRegister(newValue);
		},
		responsibleDetailsRequestStatus(newValue: IRequestStatus) {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.showDetails = true;
				return RequestStatusEnum.SUCCESS;
			}
			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(
					RequestStatusEnum.ERROR,
					'Ocorreu um erro ao retornar os dados: ' + newValue.message
				);
				return RequestStatusEnum.ERROR;
			}
		},

		changeStatusOnboardingRequestStatus(newValue: IRequestStatus) {
			notificationWrapper(newValue, 'Status alterado', 'alterar o status');
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.doSearch();
				this.fetchStatus({ idProduct: this.selectedProduct.id });
			}
		},
	},
	computed: {
		...mapGetters('cadastros', {
			selectedProduct: 'selectedProduct',
			currentRegister: 'currentRegister',
			currentRegisterRequestStatus: 'currentRegisterRequestStatus',
		}),
		...mapGetters('onboarding', {
			invitesList: 'invitesList',
			invitesListTotalPages: 'invitesListTotalPages',
			invitesListRequestStatus: 'invitesListRequestStatus',
			statusOnboardingList: 'statusList',
			clientTypeOnboardingList: 'clientTypeList',
			documentsList: 'documentsList',
			historicInviteList: 'historicInviteList',
			responsibleDetailsRequestStatus: 'responsibleDetailsRequestStatus',
			changeStatusOnboardingRequestStatus: 'changeStatusOnboardingRequestStatus',
		}),
		breakpoint() {
			return this.$vuetify.breakpoint.name;
		},
		isLoading() {
			return [
				this.invitesListRequestStatus,
				this.currentRegisterRequestStatus,
				this.responsibleDetailsRequestStatus,
				this.changeStatusOnboardingRequestStatus,
			].includes(RequestStatusEnum.START);
		},
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-cadastros-clients-list', 1, (0));
</style>
<style lang="scss" scoped>
@import './BodyHome';
</style>
