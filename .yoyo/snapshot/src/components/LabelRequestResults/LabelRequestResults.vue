<template>
	<div class="d-flex">
		<farm-chip
			v-if="hasItems"
			color="primary"
			variation="lighten" 
			:dense="true"
		>
			{{ formatResult(totalItems) }}
		</farm-chip>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	props: {
		totalItems: {
			required: true,
			type: Number,
		},
		type: {
			type: String,
			default: 'results',
		},
	},
	computed: {
		hasItems(): boolean {
			return this.totalItems > 0;
		},
	},
	methods:{
		formatResult(value): string{
			return parseInt(value, 10) > 1 ? `${value} resultados`:`${value} resultado`;
		}
	}
});
</script>
