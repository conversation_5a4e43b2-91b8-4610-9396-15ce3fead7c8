<template>
	<farm-card>
		<farm-card-content gutter="md">
			<slot name="header"></slot>
		</farm-card-content>
		<farm-line noSpacing />
		<farm-card-content gutter="md" background="base">
			<slot name="body"></slot>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({});
</script>

<style lang="scss" scoped>
@import './Cards';
</style>
