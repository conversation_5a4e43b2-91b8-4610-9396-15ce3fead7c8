<template>
	<farm-row justify="space-between">
		<farm-col cols="12" md="4">
			<farm-label for="form-billing-income"> {{ title }} </farm-label>
			<farm-textfield-v2
				id="form-billing-income"
				v-model="formattedValue"
				:mask="currencyMask"
			/>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { currency as currencyMask } from '@/helpers/masks';
import { receivedValueTypes } from './types';

export default defineComponent({
	props: {
		value: {
			type: Object as PropType<receivedValueTypes>,
			required: true,
		},
		title: {
			type: String,
			default: ' Média Faturamento (12 Meses)',
		},
	},
	data() {
		return {
			formattedValue: this.value.receivedValue
				? this.value.receivedValue.toString().replace('.', ',')
				: '0',
		};
	},
	methods: {
		currencyMask,
	},
	watch: {
		formattedValue(newValue) {
			this.value.receivedValue = newValue;
		},
	},
});
</script>
