<template>
	<farm-row>
		<farm-col cols="12" md="4">
			<farm-label for="form-accountable-name"> Nome Completo </farm-label>
			<farm-textfield-v2 v-model="value.name" type="name" id="form-accountable-name" />
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-accountable-cell"> Celular </farm-label>
			<farm-textfield-v2
				v-model="value.cellphoneNumber"
				mask="(##) # ####-####"
				id="form-accountable-cell"
				:rules="[rules.isValidCellphone]"
			/>
		</farm-col>
		<farm-col cols="12" md="4">
			<farm-label for="form-accountable-email"> E-mail </farm-label>
			<farm-textfield-v2
				v-model="value.email"
				type="email"
				id="form-accountable-email"
				:rules="[rules.email]"
			/>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { email as emailValidator } from '@farm-investimentos/front-mfe-libs-ts';
import { removeMask } from '@/helpers/removeMask';

import { AccountableTypes } from './types';

export default defineComponent({
	props: {
		value: {
			type: Object as PropType<AccountableTypes>,
		},
	},
	data() {
		return {
			rules: {
				email: v => {
					if (v === null || v === '') {
						return true;
					}
					return emailValidator(v) || 'E-mail inválido';
				},
				isValidCellphone: v => {
					if (v === null || v === '') {
						return true;
					}
					return removeMask(v).length >= 11 || 'Campo inválido';
				},
			},
		};
	},
});
</script>
