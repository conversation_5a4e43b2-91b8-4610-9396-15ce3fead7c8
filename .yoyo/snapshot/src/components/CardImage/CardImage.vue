<template>
	<div @click="onClick" class="card">
		<v-skeleton-loader
			v-if="isLoading"
			:class="['skeleton-loader', { 'skeleton-card-back': isBack }]"
			type="image"
		/>
		<img v-show="!isLoading" :src="src" :alt="alt" @load="onImageLoad" />
	</div>
</template>
<script lang="ts">
import { defineComponent, toRefs, ref } from 'vue';

export default defineComponent({
	name: 'card-image',
	props: {
		src: {
			type: String,
			required: true,
		},
		alt: {
			type: String,
			required: true,
		},
		isBack: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		const { src, alt, isBack } = toRefs(props);
		const isLoading = ref(true);

		function onClick() {
			emit('onClick');
		}

		function onImageLoad() {
			isLoading.value = false;
			emit('onImageLoad');
		}

		return {
			src,
			alt,
			onClick,
			onImageLoad,
			isLoading,
			isBack,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './CardImage.scss';
</style>
