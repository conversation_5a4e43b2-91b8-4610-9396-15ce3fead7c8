<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="68" :size="size">
		<template v-slot:header>
			<farm-dialog-header title="Importação" @onClose="handleCloseModal" />
		</template>
		<template v-slot:content>
			<farm-row class="my-4" v-if="data.statusId === 1">
				<farm-col cols="3">
					<HistoricStatus dense :status="data.statusId" />
				</farm-col>
				<farm-col cols="12" class="mt-3">
					<farm-typography size="sm" color="secondary" colorVariation="darken">
						A importação está em andamento e assim que for concluído, será possível
						visualizar o resultado final.
					</farm-typography>
				</farm-col>
			</farm-row>
			<farm-row class="my-4" v-if="data.statusId === 3">
				<farm-col cols="3">
					<HistoricStatus dense :status="data.statusId" />
				</farm-col>
				<farm-col cols="12" class="mt-3">
					<farm-typography size="sm" color="secondary" colorVariation="darken">
						Ocorreu um erro ao realizar a importação! Tente novamente.
					</farm-typography>
				</farm-col>
			</farm-row>

			<farm-row class="my-4" v-if="data.statusId === 2">
				<farm-col cols="3">
					<HistoricStatus dense :status="data.statusId" />
				</farm-col>
				<farm-col cols="12" class="my-3">
					<farm-typography size="sm" color="secondary" colorVariation="darken">
						A importação foi concluída com sucesso!
					</farm-typography>
				</farm-col>
				<farm-col cols="12" class="my-3">
					<div class="d-flex space-between">
						<farm-typography size="md" color="primary">
							<b>{{ processed }}</b>
							{{ processed === 1 ? 'Processado' : 'Processados' }}
						</farm-typography>
						<farm-typography size="md" class="mx-3" color="neutral">
							|
						</farm-typography>
						<farm-typography size="md" color="error" colorVariation="darken">
							<b>{{ notProcessed }}</b>
							{{ notProcessed === 1 ? 'Não Processado' : 'Não Processados' }}
						</farm-typography>
					</div>
				</farm-col>
				<farm-col cols="12" class="mt-3 text-to-copy" v-if="dataTextarea.length > 0">
					<farm-collapsible title="Motivos de Recusa">
						<farm-textarea readonly :rows="5" :value="dataTextarea"></farm-textarea>
						<farm-box align="end">
							<farm-copytoclipboard
								class="mt-1"
								success-message="Motivos de recusa
								copiados para área
								de transferência!"
								tooltip-color="gray"
								:toCopy="dataTextarea"
								:isIcon="false"
							/>
						</farm-box>
					</farm-collapsible>
				</farm-col>
			</farm-row>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Fechar"
				:hasCancel="false"
				@onConfirm="handleCloseModal"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
	toClipboard,
	RequestStatusEnum,
	notification,
} from '@farm-investimentos/front-mfe-libs-ts';

import HistoricStatus from '@/components/HistoricStatus';

export default defineComponent({
	components: {
		HistoricStatus,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		data: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			dataTextarea: this.data.resume,
			size: 'md',
			processed: this.data.processed,
			notProcessed: this.data.notProcessed,
		};
	},
	mounted(): void {
		if (this.data.statusId === 2) {
			this.size = 'default';
		}
	},
	methods: {
		handleCloseModal(): void {
			this.$emit('onClose');
		},
		async copyMessageToClipboard(): Promise<void> {
			try {
				await toClipboard(this.dataTextarea);
				notification(RequestStatusEnum.SUCCESS, 'Mensagem copiada para o clipboard');
			} catch (e) {
				notification(
					RequestStatusEnum.ERROR,
					'Ocorreu um erro ao copiar a mensagem para o clipboard'
				);
			}
		},
	},
});
</script>
