<template>
	<farm-box>
		<farm-caption
			variation="regular"
			color="gray"
			:ellipsis="ellipsisLabel"
			:title="hasTitleLabel"
		>
			{{ label }}
		</farm-caption>
		<farm-bodytext
			variation="bold"
			color="gray"
			color-variation="Base"
			:type="2"
			:ellipsis="ellipsisValue"
			:title="hasTitleValue"
		>
			{{ value }}
		</farm-bodytext>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		label: {
			type: String,
			required: true,
		},
		value: {
			type: [Number, String],
			required: true,
		},
		ellipsisLabel: {
			type: Boolean,
			default: false,
		},
		ellipsisValue: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		hasTitleLabel(): string {
			return this.ellipsisLabel ? this.label : '';
		},
		hasTitleValue(): string {
			return this.ellipsisValue ? this.value : '';
		},
	},
});
</script>

<style scoped>
.description{
	color: red;
	/* color: rgba(133, 133, 133, 1) */
}
</style>
