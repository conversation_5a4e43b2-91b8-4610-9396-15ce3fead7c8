export type GeneralInfoPJTypes = {
	document?: string;
	acronym: string;
	fantasyName: string;
	establishmentDate: string;
	coreBusinessCode: string;
	coreBusinessDescription: string;
	legalNature: string;
	registrationStatus: string;
	registrationStatusDate: string;
	companySize?: string;
	companyType: string | null;
	companyTypeId: number | null;
	stateRegistration?: string;
	ruralRegistration?: string;
	status?: string | number | boolean;
	headOffice?: string | number | boolean;
	financialInstitution?: string | number | boolean;
	analysisDescription?: string;
	analysisDescriptionDate?: string;
	ruralActivity?: boolean;
};

export const generalInfoModel: GeneralInfoPJTypes = {
	document: null,
	acronym: null,
	fantasyName: null,
	establishmentDate: '',
	coreBusinessCode: null,
	coreBusinessDescription: null,
	legalNature: null,
	registrationStatus: null,
	registrationStatusDate: '',
	companySize: null,
	stateRegistration: null,
	ruralRegistration: null,
	status: false,
	headOffice: false,
	financialInstitution: false,
	companyType: null,
	companyTypeId: 0,
};

export const generalInfoModelWithDrawee: GeneralInfoPJTypes = {
	document: null,
	acronym: null,
	fantasyName: null,
	establishmentDate: '',
	coreBusinessCode: null,
	coreBusinessDescription: null,
	legalNature: null,
	registrationStatus: null,
	registrationStatusDate: '',
	companySize: null,
	stateRegistration: null,
	ruralRegistration: null,
	status: false,
	headOffice: false,
	financialInstitution: false,
	analysisDescription: null,
	analysisDescriptionDate: '',
	ruralActivity: false,
	companyType: null,
	companyTypeId: 0,
};
