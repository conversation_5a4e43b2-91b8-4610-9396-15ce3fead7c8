<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" md="4">
				<farm-label for="form-pf-document"> CNPJ </farm-label>
				<farm-textfield-v2 disabled v-model="value.document" id="form-pf-document" />
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-acronym" required> Razão Social </farm-label>
				<farm-textfield-v2
					id="form-pj-acronym"
					v-model="value.acronym"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-fantasyName" required> Nome Fantasia </farm-label>
				<farm-textfield-v2
					id="form-pj-fantasyName"
					v-model="value.fantasyName"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-establishmentDate" required>
					Data de Constituição
				</farm-label>
				<farm-input-datepicker
					v-model="value.establishmentDate"
					ref="establishmentDate"
					inputId="form-pj-establishmentDate"
					:max="maxDate"
					:required="true"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-coreBusinessCode" required>
					Código da Atividade Principal
				</farm-label>
				<farm-textfield-v2
					id="form-pj-coreBusinessCode"
					mask="####-#/##"
					v-model="value.coreBusinessCode"
					:rules="[rules.required, rules.coreBusinessCode]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-coreBusinessDescription" required>
					Descrição da Atividade Principal
				</farm-label>
				<farm-textfield-v2
					id="form-pj-coreBusinessDescription"
					v-model="value.coreBusinessDescription"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-legalNature" required> Natureza Jurídica </farm-label>
				<farm-select-auto-complete
					id="form-pj-legalNature"
					v-model="value.companyTypeId"
					item-text="label"
					item-value="value"
					:items="legalNatureItems"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-registrationStatus" required>
					Situação Cadastral
				</farm-label>
				<farm-select-auto-complete
					id="form-pj-registrationStatus"
					v-model="value.registrationStatus"
					:items="registrationStatusItems"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<div class="d-flex">
					<farm-label for="form-pj-registrationStatusDate" required>
						Data da Situação Cadastral
					</farm-label>
					<farm-tooltip>
						Data de Consulta da Situação Cadastral na Receita Federal
						<template v-slot:activator>
							<farm-icon class="ml-1" size="md" color="gray">help-circle</farm-icon>
						</template>
					</farm-tooltip>
				</div>
				<farm-input-datepicker
					ref="registrationStatusDate"
					inputId="form-pj-registrationStatusDate"
					v-model="value.registrationStatusDate"
					:max="maxDate"
					:required="true"
					:rules="[rules.required]"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-companySize"> Porte da Empresa </farm-label>
				<farm-textfield-v2 id="form-pj-companySize" v-model="value.companySize" />
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-stateRegistration"> Inscrição Estadual </farm-label>
				<farm-textfield-v2
					id="form-pj-stateRegistration"
					v-model="value.stateRegistration"
				/>
			</farm-col>
			<farm-col cols="12" md="4">
				<farm-label for="form-pj-ruralRegistration"> Inscrição Rural </farm-label>
				<farm-textfield-v2
					id="form-pj-ruralRegistration"
					v-model="value.ruralRegistration"
				/>
			</farm-col>

			<farm-col cols="12" md="4" v-if="type !== 'common'">
				<farm-label for="form-pj-analysisDescription">
					Descrição da Atualização
				</farm-label>
				<farm-textfield-v2
					id="form-pj-analysisDescription"
					v-model="value.analysisDescription"
				/>
			</farm-col>
			<farm-col cols="12" md="4" v-if="type !== 'common'">
				<farm-label for="form-pj-analysisDescriptionDate"> Data da Descrição </farm-label>
				<farm-input-datepicker
					ref="startRelationship"
					inputId="form-pj-analysisDescriptionDate"
					v-model="value.analysisDescriptionDate"
					:max="maxDate"
				/>
			</farm-col>
		</farm-row>
		<farm-row v-if="type === 'common'" class="mb-4">
			<farm-col cols="2">
				<farm-label for="form-pj-status"> Status </farm-label>
				<farm-switcher
					class="mt-3"
					id="form-pj-status"
					v-model="value.status"
					block
				></farm-switcher>
			</farm-col>
			<farm-col cols="2">
				<farm-label for="form-pj-headOffice"> Matriz </farm-label>
				<farm-switcher
					class="mt-3"
					id="form-pj-headOffice"
					v-model="value.headOffice"
					block
				></farm-switcher>
			</farm-col>
			<farm-col cols="2">
				<farm-label for="form-pj-financialInstitution"> Instituição Financeira </farm-label>
				<farm-switcher
					class="mt-3"
					id="form-pj-financialInstitution"
					v-model="value.financialInstitution"
					block
				>
				</farm-switcher>
			</farm-col>
		</farm-row>
		<farm-row v-if="type !== 'common'">
			<farm-col cols="2">
				<farm-label id="form-pj-headOffice"> Matriz </farm-label>
				<farm-switcher
					class="mt-3"
					id="form-pj-headOffice"
					v-model="value.headOffice"
					block
				>
				</farm-switcher>
			</farm-col>
			<farm-col cols="2">
				<farm-label for="form-pj-financialInstitution"> Instituição Financeira </farm-label>
				<farm-switcher
					class="mt-3"
					id="form-pj-financialInstitution"
					v-model="value.financialInstitution"
					block
				>
				</farm-switcher>
			</farm-col>
			<farm-col cols="3">
				<farm-label for="form-pj-ruralActivity"> Atividade Agro (ou via CNAE) </farm-label>
				<farm-switcher
					class="mt-3"
					id="form-pj-ruralActivity"
					v-model="value.ruralActivity"
					block
				>
				</farm-switcher>
			</farm-col>
			<farm-col cols="2">
				<farm-label for="form-pj-status"> Status </farm-label>
				<farm-switcher class="mt-3" id="form-pj-status" v-model="value.status" block>
				</farm-switcher>
			</farm-col>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>

<script lang="ts">
import { PropType, defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { RegistrationStatusItems } from '@/constants';

import { GeneralInfoPJTypes } from './types';

export default defineComponent({
	props: {
		value: {
			type: Object as PropType<GeneralInfoPJTypes>,
			required: true,
		},
		type: {
			type: String,
			default: 'common',
		},
	},
	data() {
		return {
			selected: null,
			legalNatureItems: [],
			registrationStatusItems: RegistrationStatusItems.map(item => ({
				value: item,
				text: item,
			})),
		};
	},
	computed: {
		...mapGetters('cadastros', {
			peopleCompanyTypeData: 'peopleCompanyTypeData',
			peopleCompanyTypeDataRequestStatus: 'peopleCompanyTypeDataRequestStatus',
		}),
		isLoading() {
			return this.peopleCompanyTypeDataRequestStatus === RequestStatusEnum.START;
		},
		maxDate() {
			return new Date().toISOString();
		},
		rules() {
			return {
				required: value => !!value || 'Campo obrigatório',
				coreBusinessCode: value => (value && value.length === 9) || 'Código inválido',
			};
		},
	},
	mounted(): void {
		this.getPeopleCompanyType();
	},
	methods: {
		...mapActions('cadastros', {
			getPeopleCompanyType: 'getPeopleCompanyType',
		}),
	},
	watch: {
		peopleCompanyTypeDataRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.legalNatureItems = this.peopleCompanyTypeData;
			}
			return newValue;
		},
	},
});
</script>
