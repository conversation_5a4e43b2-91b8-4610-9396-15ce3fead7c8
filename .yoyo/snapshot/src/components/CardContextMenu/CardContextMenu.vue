<template>
	<div class="d-flex justify-end align-center">
		<farm-context-menu
			v-if="showMenu && !disabledMenu"
			:items="items"
			@finalizeAssociation="handleFinalizeAssociation(data)"
			@reAssociate="handleReAssociateOption(data)"
			@edit="handleEdit(data)"
		/>
		<div class="farm-context-menu-disabled" v-if="disabledMenu">
			<farm-icon color="neutral"> dots-horizontal </farm-icon>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
		items: {
			type: Array,
			default: () => [],
		},
		showMenu: {
			type: Boolean,
			default: () => false,
		},
		disabledMenu: {
			type: Boolean,
			default: () => false,
		},
	},
	methods: {
		handleEdit(data): void {
			this.$emit('edit', data);
		},
		handleFinalizeAssociation(data): void {
			this.$emit('finalizeAssociation', data);
		},
		handleReAssociateOption(data): void {
			this.$emit('reAssociate', data);
		},
	},
});
</script>

<style lang="scss" scoped>
@import './CardContextMenu';
</style>
