<template>
	<farm-row justify="space-between">
		<farm-col cols="12">
			<farm-row>
				<farm-col sm="12" md="4" v-if="!isCompany">
					<farm-label for="form-address-type" required> Tipo</farm-label>
					<farm-select-auto-complete
						id="form-address-type"
						item-text="label"
						item-value="value"
						v-model="value.addressTypeId"
						:items="typesList"
						:rules="[rules.required]"
					/>
				</farm-col>
				<farm-col sm="12" md="4">
					<farm-label for="form-address-zipcode" required> CEP </farm-label>
					<farm-textfield-v2
						id="form-address-zipcode"
						v-model="value.zipCode"
						mask="#####-###"
						:rules="[rules.required]"
						@keyup="changeZipCode()"
					/>
				</farm-col>
				<farm-col sm="12" md="4">
					<farm-label for="form-address-address" required> Endereço </farm-label>
					<farm-textfield-v2
						id="form-address-address"
						v-model="value.address"
						:rules="[rules.required]"
					/>
				</farm-col>
				<farm-col sm="12" md="4">
					<farm-label for="form-address-number" required> Número </farm-label>
					<farm-textfield-v2
						id="form-address-number"
						ref="inputAddressNumber"
						v-model="value.number"
						:rules="[rules.required]"
					/>
				</farm-col>
				<farm-col sm="12" md="4">
					<farm-label for="form-address-complement"> Complemento </farm-label>
					<farm-textfield-v2 id="form-address-complement" v-model="value.complement" />
				</farm-col>
				<farm-col sm="12" md="4">
					<farm-label for="form-address-district" required> Bairro </farm-label>
					<farm-textfield-v2
						id="form-address-district"
						v-model="value.district"
						:rules="[rules.required]"
					/>
				</farm-col>
				<farm-col sm="12" md="4">
					<farm-label for="form-address-city" required> Cidade </farm-label>
					<farm-textfield-v2
						id="form-address-city"
						v-model="value.city"
						:rules="[rules.required]"
					/>
				</farm-col>
				<farm-col sm="12" md="4">
					<farm-label for="form-address-state" required> UF </farm-label>
					<farm-select-auto-complete
						id="form-address-state"
						v-model="value.state"
						:items="enrichedStates"
						:rules="[rules.required]"
					/>
				</farm-col>
			</farm-row>
		</farm-col>
		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { notification, RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { AddressTypes } from './types';

export default defineComponent({
	props: {
		value: {
			type: Object as PropType<AddressTypes>,
			required: true,
		},
		isCompany: {
			type: Boolean,
			default: false,
		},
	},
	mounted(): void {
		if (this.isCompany) {
			this.value.addressTypeId = 1;
		}
	},
	data() {
		return {
			typesList: [
				{ label: 'Comercial', value: 1 },
				{ label: 'Residencial', value: 2 },
			],
			proxyAddress: this.value,
			fetchActive: false,
		};
	},
	methods: {
		...mapActions('zipcode', {
			fetchZipCode: 'fetchZipCode',
			resetZipCode: 'resetZipCode',
		}),
		changeZipCode(): void {
			if (!this.value.zipCode) return;
			this.resetZipCode();
			if (this.value.zipCode.length === 9 && !this.fetchActive) {
				this.fetchActive = true;
				this.fetchZipCode({ zipcode: this.value.zipCode });
			}
		},
	},
	computed: {
		...mapGetters('cadastros', {
			states: 'statesEnumAsArray',
		}),
		...mapGetters('zipcode', {
			zipcode: 'zipcode',
			zipcodeRequestStatus: 'zipcodeRequestStatus',
		}),
		rules() {
			return { required: value => !!value || 'Campo obrigatório' };
		},
		isLoading(): boolean {
			return this.zipcodeRequestStatus === RequestStatusEnum.START;
		},
		enrichedStates(): Array<Record<string, string>> {
			return this.states.map(state => ({ value: state, text: state }));
		},
	},
	watch: {
		value(newValue): void {
			this.proxyAddress = newValue;
		},
		zipcodeRequestStatus(newValue): string {
			if (newValue === RequestStatusEnum.SUCCESS) {
				if (this.zipcode.logradouro.length > 0) {
					this.proxyAddress.address = this.zipcode.logradouro;
				}
				if (this.zipcode.localidade.length > 0) {
					this.proxyAddress.city = this.zipcode.localidade;
				}
				if (this.zipcode.uf.length > 0) {
					this.proxyAddress.state = this.zipcode.uf;
				}
				if (this.zipcode.bairro.length > 0) {
					this.proxyAddress.district = this.zipcode.bairro;
				}
				this.$refs.inputAddressNumber.$el.querySelector('input').focus();
				this.fetchActive = false;
				return RequestStatusEnum.SUCCESS;
			}

			if (newValue.type === RequestStatusEnum.ERROR) {
				notification(RequestStatusEnum.ERROR, 'CEP não encontrado');
				this.proxyAddress.street = '';
				this.proxyAddress.city = '';
				this.proxyAddress.state = '';
				this.proxyAddress.district = '';
				setTimeout(() => {
					this.fetchActive = false;
				}, 2000);
				return RequestStatusEnum.ERROR;
			}
		},
	},
});
</script>
