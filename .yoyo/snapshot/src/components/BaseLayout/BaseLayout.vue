<template>
	<farm-box>
		<router-view
			v-if="isOpenRoute($router.currentRoute.path) || (userHasAccess && hasSelectedProduct)"
		/>
		<farm-container v-else>
			<h3>Sem acesso</h3>
		</farm-container>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { notification, localStorageWrapper } from '@farm-investimentos/front-mfe-libs-ts';

const SelectedProduct = 'SelectedProduct';

export default defineComponent({
	computed: {
		...mapGetters('cadastros', {
			statesEnumAsArray: 'statesEnumAsArray',
			selectedProduct: 'selectedProduct',
		}),
		...mapGetters('userAccess', {
			currentUserRoles: 'currentUserRoles',
		}),
		hasSelectedProduct() {
			return !!this.selectedProduct.id;
		},
	},
	data() {
		return {
			userHasAccess: false,
		};
	},
	methods: {
		...mapActions('cadastros', {
			updateBaseData: 'updateBaseData',
			fetchStates: 'fetchStates',
			fetchUserRolesV2: 'fetchUserRolesV2',
		}),
		emitRouteMeta(route) {
			notification('ROUTE_META', route.meta);
		},
		isOpenRoute(path: String): Boolean {
			return ['/admin/cadastros/perfil/nova-senha'].some(item => item === path);
		},
	},
	created() {
		const initProduct = localStorageWrapper.getItem(SelectedProduct) || {};
		this.listenToUserRolesChange();
		notification('RETRIEVE_CURRENT_USER_ROLES');

		window.addEventListener('CURRENT_USER_INTERNAL', (data: any) => {
			this.updateInternalUser(data.detail.message);
		});

		notification('RETRIEVE_CURRENT_USER_INTERNAL');

		window.addEventListener('PRODUCT', (data: any) => {
			this.updateBaseData({
				selectedProduct: { ...data.detail.message },
			});
		});

		if (this.statesEnumAsArray.length === 0) this.fetchStates();

		this.fetchUserRolesV2();
		this.updateBaseData({
			selectedProduct: { ...initProduct },
		});
		this.emitRouteMeta(this.$route);
	},
	watch: {
		$route(to) {
			this.checkAccess();
			this.emitRouteMeta(to);
		},
	},
});
</script>

<style src="@farm-investimentos/front-mfe-components/dist/front-mfe-components.css" />
