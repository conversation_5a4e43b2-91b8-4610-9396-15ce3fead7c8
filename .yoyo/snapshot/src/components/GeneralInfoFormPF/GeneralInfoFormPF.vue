<template>
	<farm-row justify="space-between" class="my-6">
		<farm-col cols="12">
			<farm-row>
				<farm-col cols="12" md="4">
					<farm-label for="form-pf-document"> CPF </farm-label>
					<farm-textfield-v2 disabled v-model="value.document" id="form-pf-document" />
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-label for="form-pf-fullName" required> Nome Completo </farm-label>
					<farm-textfield-v2
						id="form-pf-fullName"
						v-model="value.fullName"
						:rules="[rules.required]"
					/>
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-label for="form-pf-socialName"> Nome Social </farm-label>
					<farm-textfield-v2 v-model="value.socialName" forKey="form-pf-socialName" />
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-label for="form-pf-nationality"> Nacionalidade </farm-label>
					<farm-select-auto-complete
						id="form-pf-nationality"
						item-text="label"
						item-value="value"
						v-model="value.peopleNationalityId"
						:items="selectNationality"
					/>
				</farm-col>

				<farm-col cols="12" md="4">
					<farm-label for="form-pf-rg"> RG </farm-label>
					<farm-textfield-v2 v-model="value.rg" forKey="form-pf-rg" />
				</farm-col>

				<farm-col cols="12" md="4">
					<farm-label for="form-pf-rgUfStateRegistration"> UF Emissor </farm-label>
					<farm-select-auto-complete
						id="form-pf-rgUfStateRegistration"
						item-text="text"
						item-value="value"
						v-model="value.rgUfStateRegistration"
						:items="enrichedStates"
					/>
				</farm-col>

				<farm-col cols="12" md="4">
					<farm-label for="form-pf-gender"> Gênero </farm-label>
					<farm-select-auto-complete
						id="form-pf-gender"
						item-text="label"
						item-value="value"
						v-model="value.peopleGenderId"
						:items="selectGender"
					/>
				</farm-col>

				<farm-col cols="12" md="4">
					<farm-label for="form-pf-maritalStatus"> Estado Civil </farm-label>
					<farm-select-auto-complete
						id="form-pf-maritalStatus"
						item-text="label"
						item-value="value"
						v-model="value.peopleMaritalStatusId"
						:items="selectMaritalStatus"
					/>
				</farm-col>

				<farm-col cols="12" md="4">
					<farm-label for="form-pf-profession"> Profissão </farm-label>
					<farm-select-auto-complete
						id="form-pf-profession"
						item-text="label"
						item-value="value"
						v-model="value.professionId"
						:items="selectProfession"
					/>
				</farm-col>

				<farm-col cols="12" md="4">
					<farm-label for="form-pf-birthDate"> Data de Nascimento </farm-label>
					<farm-input-datepicker
						ref="birthDate"
						inputId="form-pf-birthDate"
						v-model="value.birthDate"
						:max="maxDate"
					/>
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-label for="form-pf-birthPlace"> Local de Nascimento </farm-label>
					<farm-textfield-v2 v-model="value.birthplace" id="form-pf-birthPlace" />
				</farm-col>

				<farm-col cols="12" md="4">
					<farm-label for="form-pf-registrationStatus"> Situação Cadastral </farm-label>
					<farm-select-auto-complete
						id="form-pf-registrationStatus"
						v-model="value.registrationStatus"
						:items="registrationStatusItems"
					/>
				</farm-col>
				<farm-col cols="12" md="4">
					<div class="d-flex">
						<farm-label for="form-pf-registrationStatusDate">
							Data da Situação Cadastral
						</farm-label>

						<farm-tooltip>
							Data de Consulta da Situação Cadastral na Receita Federal
							<template v-slot:activator>
								<farm-icon class="ml-1" size="md" color="gray"
									>help-circle</farm-icon
								>
							</template>
						</farm-tooltip>
					</div>

					<farm-input-datepicker
						ref="registrationStatusDate"
						inputId="form-pf-registrationStatusDate"
						v-model="value.registrationStatusDate"
						:max="maxDate"
					/>
				</farm-col>

				<farm-col cols="12" md="4">
					<farm-label for="form-pf-stateRegistration"> Inscrição Estadual </farm-label>
					<farm-textfield-v2
						id="form-pf-stateRegistration"
						v-model="value.stateRegistration"
					/>
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-label for="form-pf-ruralRegistration"> Inscrição Rural </farm-label>
					<farm-textfield-v2
						id="form-pf-ruralRegistration"
						v-model="value.ruralRegistration"
					/>
				</farm-col>
			</farm-row>
			<farm-row align="center">
				<farm-col md="2" class="farm-col_switch">
					<div class="d-flex">
						<farm-label for="form-pf-pep" class="mb-0">PEP</farm-label>
						<farm-tooltip>
							<b>PEP</b> <br />
							Pessoa Politicamente Exposta
							<template v-slot:activator>
								<farm-icon class="ml-2" size="md" color="gray"
									>help-circle</farm-icon
								>
							</template>
						</farm-tooltip>
					</div>
					<farm-switcher
						class="mt-3"
						id="form-pf-pep"
						v-model="value.pep"
						block
					></farm-switcher>
				</farm-col>

				<farm-col md="2" class="farm-col_switch">
					<farm-label for="form-pf-status"> Status</farm-label>
					<farm-switcher
						class="mt-3"
						id="form-pf-status"
						v-model="value.status"
						block
					></farm-switcher>
				</farm-col>
			</farm-row>
		</farm-col>
		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { mapActions, mapGetters } from 'vuex';
import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';

import { RegistrationStatusItems, LegalNatureItems } from '@/constants';

export default defineComponent({
	props: {
		value: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			selected: null,
			legalNatureItems: LegalNatureItems,
			registrationStatusItems: RegistrationStatusItems.map(item => ({
				value: item,
				text: item,
			})),
			selectGender: [],
			selectMaritalStatus: [],
			selectNationality: [],
			selectProfession: [],
		};
	},
	computed: {
		...mapGetters('cadastros', {
			states: 'statesEnumAsArray',
			nationalityData: 'nationalityData',
			genderData: 'genderData',
			maritalStatusData: 'maritalStatusData',
			professionData: 'professionData',
			nationalityDataRequestStatus: 'nationalityDataRequestStatus',
			genderDataRequestStatus: 'genderDataRequestStatus',
			maritalStatusDataRequestStatus: 'maritalStatusDataRequestStatus',
			professionDataRequestStatus: 'professionDataRequestStatus',
		}),
		maxDate() {
			return new Date().toISOString();
		},
		rules() {
			return { required: value => !!value || 'Campo obrigatório' };
		},
		isLoading() {
			const requestStatuses = [
				this.nationalityDataRequestStatus,
				this.genderDataRequestStatus,
				this.maritalStatusDataRequestStatus,
				this.professionDataRequestStatus,
			];
			return requestStatuses.includes(RequestStatusEnum.START);
		},
		enrichedStates(): Array<Record<string, string>> {
			return this.states.map(state => ({ value: state, text: state }));
		},
	},
	mounted(): void {
		this.doSearch();
	},
	methods: {
		...mapActions('cadastros', {
			getNationality: 'getNationality',
			getGender: 'getGender',
			getMaritalStatus: 'getMaritalStatus',
			getProfession: 'getProfession',
		}),
		doSearch(): void {
			this.getNationality();
			this.getGender();
			this.getMaritalStatus();
			this.getProfession();
		},
	},
	watch: {
		nationalityDataRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.selectNationality = this.nationalityData;
			}
		},
		genderDataRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.selectGender = this.genderData;
			}
		},
		maritalStatusDataRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.selectMaritalStatus = this.maritalStatusData;
			}
		},
		professionDataRequestStatus(newValue): void {
			if (newValue === RequestStatusEnum.SUCCESS) {
				this.selectProfession = this.professionData;
			}
		},
	},
});
</script>
