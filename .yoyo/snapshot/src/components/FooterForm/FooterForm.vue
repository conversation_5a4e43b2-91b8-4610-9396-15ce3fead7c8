<template>
	<farm-row extra-decrease>
		<farm-container-footer noTop>
			<div class="footer-form">
				<div class="footer-form-content">
					<div v-if="showLayoutData">
						<farm-resource-metainfo :infos="informationData" />
					</div>
				</div>
				<div class="footer-form-content" v-if="showLayoutButtons">
					<div class="footer-form-buttons">
						<farm-btn
							v-if="!hiddenButtonCancel"
							class="farm-btn--responsive"
							outlined
							title="Cancelar"
							@click="handleCancel"
						>
							Cancelar
						</farm-btn>
						<farm-btn
							v-if="!hiddenButton"
							class="farm-btn--responsive ml-0 ml-2 mt-3 mt-sm-0"
							:title="labelButton"
							:disabled="!isDisabledButton"
							@click="handleSave"
						>
							{{ labelButton }}
						</farm-btn>
					</div>
				</div>
			</div>
		</farm-container-footer>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { createObject } from '@/helpers/createObject';

import { FooterFormDataType, modelFooterFormData } from './types';

type FooterFormDataDataType = {
	informationData: FooterFormDataType;
};

export default defineComponent({
	props: {
		data: {
			type: Object as PropType<FooterFormDataType>,
			default: () => null,
		},
		showLayoutData: {
			type: Boolean,
			default: true,
		},
		showLayoutButtons: {
			type: Boolean,
			default: true,
		},
		hiddenButton: {
			type: Boolean,
			default: false,
		},
		hiddenButtonCancel: {
			type: Boolean,
			default: false,
		},
		isDisabledButton: {
			type: Boolean,
		},
		labelButton: {
			type: String,
			default: 'Salvar',
		},
	},
	data(): FooterFormDataDataType {
		return {
			informationData: createObject<FooterFormDataType>(modelFooterFormData),
		};
	},
	methods: {
		handleCancel(): void {
			this.$emit('onCancel');
		},
		handleSave(): void {
			this.$emit('onSave');
		},
	},
	watch: {
		data(value: FooterFormDataType | null): void {
			if (value !== null) {
				this.informationData = {
					createdAt: value.createdAt,
					createdBy: value.createdBy,
					createdHours: value.createdHours,
					updatedAt: value.updatedAt,
					username: value.username,
					updatedHours: value.updatedHours,
				};
			}
		},
	},
});
</script>

<style lang="scss" scoped>
@import './FooterForm';
</style>
