export const documents = {
	label: 'Documentos',
	handler: 'documents',
	icon: { color: 'gray', type: 'open-in-new' },
};

export const historic = {
	label: 'Hist<PERSON>ric<PERSON>',
	handler: 'historic',
	icon: { color: 'gray', type: 'history' },
};

export const onHold = {
	label: 'Em Espera',
	handler: 'onHold',
	icon: { color: 'secondary', type: 'pause-circle' },
};

export const underAnalysis = {
	label: '<PERSON> Anális<PERSON>',
	handler: 'onUnderAnalysis',
	icon: { color: 'secondary', type: 'magnify' },
};

export const declined = {
	label: 'Declinar',
	handler: 'onDeclined',
	icon: { color: 'error', type: 'close' },
};
