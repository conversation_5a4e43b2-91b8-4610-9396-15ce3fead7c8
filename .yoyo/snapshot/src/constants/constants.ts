export const RegistersStatus = {
	0: { label: 'Pendente', color: 'status-chip-pending' },
	1: { label: 'Completo', color: 'status-chip-complete' },
};

export const RegistersStatusConsult = {
	0: { label: 'Inativo', color: 'neutral' },
	1: { label: 'Ativo', color: 'primary' },
};

export const getStatusColorForChip = id => defaultStatusColors[id];
export const getStatusColorForChipInverted = id => defaultStatusColorsInverted[id];

const defaultStatusColors = {
	1: 'status-chip-error',
	2: 'status-chip-warning',
	3: 'status-chip-info',
	4: 'status-chip-secondary',
};
const defaultStatusColorsInverted = {
	1: 'status-chip-secondary',
	2: 'status-chip-info',
	3: 'status-chip-warning',
	4: 'status-chip-error',
};

export enum RegistersStatusValueEnum {
	Inativo = 0,
	Ativo = 1,
}

export enum RegistersSalesPerformedValueEnum {
	Não = 0,
	Sim = 1,
}

export enum RegistersStatusColorEnum {
	Inativo = 'status-chip-gray',
	Ativo = 'status-chip-secondary',
}

export const LegalNatureItems = [
	'Empresário/Empresa individual (EI)',
	'Empresa Individual de Responsabilidade Limitada (EIRELI)',
	'Microempreendedor Individual (MEI)',
	'Outro',
	'Sociedade Anônima de Capital Aberto (S/A)',
	'Sociedade Anônima de Capital Fechado (S/A)',
	'Sociedade Cooperativa',
	'Sociedade Empresária Limitada (LTDA)',
	'Sociedade Limitada Unipessoal (SLU)',
];

export const RegistrationStatusItems = [
	'ATIVO',
	'BAIXADA',
	'INAPTA',
	'NÃO CONFIRMADO',
	'NULA',
	'SUSPENSA',
];

export const ACCETPTYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
export const EXCEL_ACCEPT_FILETYPE =
	'.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel';

export const EDIT = 'edit';
export const NEW = 'new';

export const DATA = 'dados';
export const DOCUMENTS = 'documentos';
export const PROGRAMS = 'programas';

export const MIN_VALUE = 'min_value';
export const TAB_DATE = 'tab_date';

export const ORIGINATORS_ASSIGNORS = 'originadores_cedentes';

export const FINANCIAL_VEHICLE = 'veiculos_financeiro';

export const PRODUCTS = 'produtos';

export const CFOP = 'cfop';

export const FOREIGNPARTICIPATION = 'participacao_estrangeira';
export const MOBLILITY = 'modalidades';
export const COMMERCIAL_PRODUCTS = 'produtos_comerciais';
export const CARD = 'cartao';

export const PF = 'PF';
export const PJ = 'PJ';

export const DRAWEE = 'sacados';

export const IMPORT = 'importar';

export const HISTORIC = 'historico';

export const CORPORATE_STRUCTURE = 'quadro_societario';

export const FOREIGN_PARTICIPATION = 'participacao-estrangeira';

export const BANK_DATA = 'dados-bancarios';

export const HISTORIC_STATUS = {
	1: { label: 'Em andamento', color: 'warning' },
	2: { label: 'Concluído', color: 'primary' },
	3: { label: 'Erro', color: 'error' },
};

export const typePerson = [
	{
		id: 'PF',
		label: 'FÍSICA',
	},
	{
		id: 'PJ',
		label: 'JURÍDICA',
	},
];

export const LIMITS = 'Limites';
export const COMUNICATIONS = 'Comunicações';

export const PRODUCT_TYPE = {
	ATACADO: { id: 1, label: 'ATACADO' },
	VAREJO: { id: 2, label: 'VAREJO' },
};

export const OPERATION_NAMES = {
	CESSION: 'cession',
	ORIGINATION: 'origination',
	BRIDGE: 'bridge',
} as const;

export const OPERATION_TYPES = [
	{ id: 1, name: 'Cessão', token: OPERATION_NAMES.CESSION },
	{ id: 2, name: 'Originação', token: OPERATION_NAMES.ORIGINATION },
	{ id: 3, name: 'Ponte', token: OPERATION_NAMES.BRIDGE },
];

export const OPERATION_TYPES_ENUM = OPERATION_TYPES.reduce((accumulator, operation) => {
	return {
		...accumulator,
		[operation.id]: {
			name: operation.name,
			token: operation.token,
		},
	};
}, {});

export const OPERATION_TYPES_TOKEN_ENUM = OPERATION_TYPES.reduce((accumulator, operation) => {
	return {
		...accumulator,
		[operation.token]: {
			id: operation.id,
			name: operation.name,
		},
	};
}, {} as Record<typeof OPERATION_TYPES[number]['token'], Pick<typeof OPERATION_TYPES[number], 'id' | 'name'>>);
