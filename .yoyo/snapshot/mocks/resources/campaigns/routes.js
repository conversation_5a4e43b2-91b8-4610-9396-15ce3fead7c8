const { campaignList, validation } = require('.');
const { rawCampaignList } = require('../../db/campaigns');

module.exports = {
	getCampaignRoutes(server) {
		server.get('/v1/products/campaign', (req, res) => {
			if (req.query?.forceError) {
				return res.status(400);
			}

			return res.json(campaignList(req.query?.page, req.query?.limit)).status(200);
		});

		server.get('/v1/products/campaign/detail/:id', (req, res) => {
			if (req.query?.forceError) {
				return res.status(400);
			}

			const foundCampaign = rawCampaignList.find(
				campaign => campaign.id === Number(req.params?.id)
			);

			if (!foundCampaign) {
				return res.json().status(404);
			}

			return res.json(foundCampaign).status(200);
		});

		server.get('/v1/products/campaign/history', (req, res) => {
			if (req.query?.forceError) {
				return res.status(400);
			}

			if (req.query?.forceValidation) {
				return res.status(400).json({ ...validation });
			}

			return res.status(200).json();
		});

		server.post('/v1/products/campaign/save', (req, res) => {
			if (req.query?.forceError) {
				return res.status(400);
			}

			if (req.query?.forceValidation) {
				return res.status(400).json({ ...validation });
			}

			return res.status(200).json();
		});
	},
};
