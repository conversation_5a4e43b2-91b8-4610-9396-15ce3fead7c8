const { uploadError, uploadSuccess, history } = require('.');

module.exports = {
	getPricingRoutes(server) {
		server.get('/v1/products/:id/settings/pricing/history', (req, res) => {
			const page = req.query?.page || 0;
			const limit = req.query?.limit || 10;

			const mock = history(page, limit);

			return res.json({ ...mock });
		});

		server.post('/v1/products/contracts/excel/pricing/upload', (req, res) => {
			if(req.query.forceError){
				return res.status(200).json(uploadError);
			}

			return res.status(201).json(uploadSuccess);
		});
	}
};
