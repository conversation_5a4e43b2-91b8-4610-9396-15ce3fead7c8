const { campaignMarginList, campaignMarginHistoryList } = require('.');
const { rawCampaignMarginList } = require('../../db/margins');

const names = [
	'<PERSON><PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON>',
	'<PERSON><PERSON><PERSON>',
	'<PERSON><PERSON>',
	'<PERSON><PERSON>',
	'<PERSON><PERSON><PERSON><PERSON>',
	'<PERSON>',
	'<PERSON><PERSON>',
];

const processMargin = (productId, campaignId, vehicleId) => {
	let margin = null;

	for (const marginItem of rawCampaignMarginList) {
		const isCorrectProduct = marginItem.productId === Number(productId);

		if (!isCorrectProduct) {
			continue;
		}

		if (margin) {
			break;
		}

		margin = marginItem.campaigns.find(
			campaign =>
				campaign.campaignId === Number(campaignId) &&
				campaign.vehicleId === Number(vehicleId)
		);

		if (margin) {
			margin = JSON.parse(JSON.stringify(margin));

			margin.productId = marginItem.productId;
			margin.productName = marginItem.productName;
			margin.campaignStatus = true;
			margin.vigCampaignVehicleStart = '2024-01-11';
			margin.vigCampaignVehicleEnd = '2024-01-11';
			margin.updatedAt = '2024-01-11T21:03:29.236Z';
			margin.updatedByName = names[parseInt(Math.random().toString()[2], 10)];
		}
	}

	return margin;
};

module.exports = {
	getCampaignMarginRoutes(server) {
		server.get('/v1/products/campaign/margin/list', (req, res) => {
			if (req.query?.forceError) {
				return res.status(400);
			}

			const marginList = campaignMarginList(req.query?.page, req.query?.limit);

			marginList.data.alert = !req.query?.noAlert;

			return res.json(marginList).status(201);
		});

		server.get('/v1/products/campaign/:campaignId/margin/detail', (req, res) => {
			if (req.query?.forceError) {
				return res.status(400);
			}

			const { campaignId } = req.params;
			const { product_id: productId, financial_vehicle_id: vehicleId } = req.query;

			const margin = processMargin(productId, campaignId, vehicleId);

			if (!margin) {
				return res.json().status(404);
			}

			return res.json(margin).status(200);
		});

		server.get('/v1/products/campaign/:campaignId/margin/history', (req, res) => {
			if (req.query?.forceError) {
				return res.status(400);
			}

			const response = campaignMarginHistoryList(req.query?.page, req.query?.limit);

			return res.json(response).status(200);
		});
	},
};
