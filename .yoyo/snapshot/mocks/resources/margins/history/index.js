const { rawCampaignMarginHistoryList } = require('../../../db/margins');

const TOTAL_ITEMS = rawCampaignMarginHistoryList?.length || 0;
const INITIAL_LIMIT = 5;

module.exports = (page = 0, limit = 5) => {
	const pagination = {
		current: Number(page),
		size: Number(limit ?? INITIAL_LIMIT),
	};
	const numberOfItems = pagination.current * pagination.size;
	const maxNumber = pagination.size * (pagination.current + 1);

	return {
		data: {
			content: rawCampaignMarginHistoryList.slice(numberOfItems, maxNumber),
			pageable: {
				sort: null,
				pageNumber: Number(page),
				pageSize: pagination.size,
				totalPages: Math.ceil(TOTAL_ITEMS / pagination.size),
				totalElements: TOTAL_ITEMS,
			},
		},
		errors: [],
		meta: [],
	};
};
