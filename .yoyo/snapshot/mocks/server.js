const jsonServer = require('json-server');

const mockClientes = require('./db/clientes/');
const mockAvailableCash = require('./db/availableCash/');
const mockHolidays = require('./db/holidays/');
const mockFinancialVehicles = require('./db/financialVehicles/');
const allProducts = require('./db/allProducts.json');
const clientType = require('./db/clientType.json');
const mockPricings = require('./db/pricings');

const server = jsonServer.create();
const router = jsonServer.router({
	...mockClientes,
	...allProducts,
	...mockHolidays,
	...clientType,
	...mockFinancialVehicles,
	...mockPricings,
});
const middlewares = jsonServer.defaults();
const customRoutes = require('./routes.json');
const { getPricingRoutes } = require('./resources/pricings/routes');
const { getCampaignRoutes } = require('./resources/campaigns/routes');
const { getCampaignMarginRoutes } = require('./resources/margins/routes');

server.use(middlewares);

server.post('/clientes/:id/empresa', (req, res) => {
	return res.json({ data: {} });
});

server.put('/v1/available_cash/products', (req, res) => {
	return res.json({ data: {} });
});

server.get('/v1/available_cash/products', (req, res) => {
	return res.json({ ...mockAvailableCash });
});

server.get('/v1/available_cash/products', (req, res) => {
	return res.json({ ...mockAvailableCash });
});

server.post('/v1/holidays', (req, res) => {
	return res.json({ ...mockHolidays['holidays-created'] });
});

server.delete('/v1/holidays/:id', (req, res) => {
	return res.json({ ...mockHolidays['holidays-deleted'] });
});

server.patch('/v1/holidays/:id', (req, res) => {
	return res.json({ ...mockHolidays['holidays-created'] });
});

getPricingRoutes(server);
getCampaignRoutes(server);
getCampaignMarginRoutes(server);

server.use(jsonServer.rewriter(customRoutes));
server.use(router);
console.log('Routes -->', customRoutes);

server.listen(3000, () => {
	console.log('JSON Server is running! port 3000');
});
