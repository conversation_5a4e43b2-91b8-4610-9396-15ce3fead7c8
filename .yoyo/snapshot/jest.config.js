module.exports = {
	preset: '@vue/cli-plugin-unit-jest/presets/typescript-and-babel',
	setupFiles: ['<rootDir>/tests/unit/index.js'],
	modulePaths: ['<rootDir>', 'node_modules'],
	moduleFileExtensions: ['js', 'ts', 'json', 'vue'],
	collectCoverageFrom: [
		'src/**/*.{js,vue,ts}',
		'!src/**/index.js',
		'!src/**/index.ts',
		'!src/configuration/setPublicPath/setPublicPath.ts',
	],
	coveragePathIgnorePatterns: [
		'node_modules',
		'.mock.js',
		'.stories.js',
		'src/i18n/',
		'src/main.ts',
		'src/configurations/',
		'src/constants/',
		'src/routes/',
		'src/mixins/',
		'_basicKeys.ts',
		'_requestStatusKeys.ts',
		'src/store/localStorage',
		'src/composibles/composibles.ts',
	],
	transformIgnorePatterns: ['/node_modules/(?!@farm-investimentos/front-mfe-libs-ts)'],
};
